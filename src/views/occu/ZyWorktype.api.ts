import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/occu/zyWorktype/list',
  save = '/occu/zyWorktype/add',
  edit = '/occu/zyWorktype/edit',
  deleteOne = '/occu/zyWorktype/delete',
  deleteBatch = '/occu/zyWorktype/deleteBatch',
  importExcel = '/occu/zyWorktype/importExcel',
  exportXls = '/occu/zyWorktype/exportXls',
  // 危害因素相关接口
  queryRiskFactorsByWorktypeId = '/occu/zyWorktype/queryRiskFactorsByWorktypeId',
  addRiskFactorsBatch = '/occu/zyWorktype/addRiskFactorsBatch',
  removeRiskFactorsBatch = '/occu/zyWorktype/removeRiskFactorsBatch',
  getAvailableRiskFactors = '/occu/zyWorktype/getAvailableRiskFactors',
  copyRiskFactorsToWorktypes = '/occu/zyWorktype/copyRiskFactorsToWorktypes',
  // 自动完成相关接口
  autoComplete = '/occu/zyWorktype/autoComplete',
  updateUseCount = '/occu/zyWorktype/updateUseCount',
  autoCreate = '/occu/zyWorktype/autoCreate',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 查询工种关联的危害因素
 * @param worktypeId 工种ID
 */
export const queryRiskFactorsByWorktypeId = (worktypeId: string) => {
  return defHttp.get(
    {
      url: Api.queryRiskFactorsByWorktypeId,
      params: { worktypeId },
    },
    { isTransformResponse: false }
  );
};

/**
 * 批量添加危害因素关联
 * @param params
 */
export const addRiskFactorsBatch = (params: { worktypeId: string; riskFactorIds: string }) => {
  return defHttp.post({ url: Api.addRiskFactorsBatch, data: params }, { isTransformResponse: false });
};

/**
 * 批量移除危害因素关联
 * @param params
 */
export const removeRiskFactorsBatch = (params: { worktypeId: string; riskFactorIds: string }) => {
  return defHttp.post({ url: Api.removeRiskFactorsBatch, data: params }, { isTransformResponse: false });
};

/**
 * 获取所有可用的危害因素
 */
export const getAvailableRiskFactors = () => {
  return defHttp.get({ url: Api.getAvailableRiskFactors }, { isTransformResponse: false });
};

/**
 * 复制危害因素关联到其他工种
 * @param params
 */
export const copyRiskFactorsToWorktypes = (params: { sourceWorktypeId: string; targetWorktypeIds: string }) => {
  return defHttp.post(
    {
      url: Api.copyRiskFactorsToWorktypes,
      params,
    },
    { isTransformResponse: false }
  );
};

/**
 * 代码重复校验
 */
export async function codeDuplicatevalidate(rule, value) {
  return new Promise((resolve, reject) => {
    const params = { tableName: 'zy_worktype', fieldName: 'code', fieldVal: value, dataId: '' };
    defHttp
      .get({ url: '/sys/duplicate/check', params }, { isTransformResponse: false })
      .then((res) => {
        res.success ? resolve() : reject(res.message || rule.message);
      })
      .catch((err) => {
        reject(err.message || rule.message);
      });
  });
}

/**
 * 工种自动完成搜索
 * @param params 搜索参数
 */
export const autoComplete = (params: { keyword: string; limit?: number; searchType?: string }) => {
  return defHttp.get({ url: Api.autoComplete, params }, { isTransformResponse: false });
};

/**
 * 更新工种使用频次
 * @param params 参数
 */
export const updateUseCount = (params: { id: string }) => {
  return defHttp.post({ url: Api.updateUseCount, params }, { isTransformResponse: false });
};

/**
 * 自动创建工种
 * @param params 工种信息
 */
export const autoCreate = (params: { name: string; helpChar?: string; enableFlag?: number; sort?: number }) => {
  return defHttp.post({ url: Api.autoCreate, params }, { isTransformResponse: false });
};
