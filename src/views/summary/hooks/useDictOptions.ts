import { ref, onMounted } from 'vue';
import { getDictItems } from '/@/utils/dict/JDictSelectUtil';

/**
 * 获取字典选项的Hook
 */
export function useDictOptions() {
  const conclusionOptions = ref<any[]>([]);
  const accordingOptions = ref<any[]>([]);
  const diseaseOptions = ref<any[]>([]);
  const symptomOptions = ref<any[]>([]);

  // 获取职业检结论字典
  const loadConclusionOptions = async () => {
    try {
      const options = await getDictItems('zy_conclusion_dict,dict_text,code');
      conclusionOptions.value = options.map(item => ({
        label: item.text || item.dict_text,
        value: item.value || item.code,
      }));
    } catch (error) {
      console.error('获取职业检结论字典失败:', error);
      conclusionOptions.value = [];
    }
  };

  // 获取结论依据字典
  const loadAccordingOptions = async () => {
    try {
      const options = await getDictItems('zy_conclusion_according,content,content');
      accordingOptions.value = options.map(item => ({
        label: item.text || item.content,
        value: item.value || item.content,
      }));
    } catch (error) {
      console.error('获取结论依据字典失败:', error);
      accordingOptions.value = [];
    }
  };

  // 获取职业病字典
  const loadDiseaseOptions = async () => {
    try {
      const options = await getDictItems('zy_disease_dict,dict_text,code');
      diseaseOptions.value = options.map(item => ({
        label: item.text || item.dict_text,
        value: item.value || item.code,
      }));
    } catch (error) {
      console.error('获取职业病字典失败:', error);
      diseaseOptions.value = [];
    }
  };

  // 获取职业禁忌证字典
  const loadSymptomOptions = async () => {
    try {
      const options = await getDictItems('zy_taboo_symptom,name,code');
      symptomOptions.value = options.map(item => ({
        label: item.text || item.name,
        value: item.value || item.code,
      }));
    } catch (error) {
      console.error('获取职业禁忌证字典失败:', error);
      symptomOptions.value = [];
    }
  };

  // 初始化所有字典数据
  const initDictOptions = async () => {
    await Promise.all([
      loadConclusionOptions(),
      loadAccordingOptions(),
      loadDiseaseOptions(),
      loadSymptomOptions(),
    ]);
  };

  onMounted(() => {
    initDictOptions();
  });

  return {
    conclusionOptions,
    accordingOptions,
    diseaseOptions,
    symptomOptions,
    loadConclusionOptions,
    loadAccordingOptions,
    loadDiseaseOptions,
    loadSymptomOptions,
    initDictOptions,
  };
}
