import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { saveOrUpdate } from '../ZyConclusionDetail.api';
import type { EditRecordRow } from '/@/components/Table/src/components/editable';

/**
 * 可编辑表格状态管理Hook
 */
export function useEditableTable() {
  // 编辑状态管理
  const editingRows = ref<Set<string>>(new Set());
  const savingRows = ref<Set<string>>(new Set());
  const editingData = reactive<Record<string, any>>({});

  /**
   * 开始编辑行
   */
  const startEdit = (record: EditRecordRow) => {
    if (!record.id) return;
    
    // 保存原始数据用于取消编辑时恢复
    editingData[record.id] = { ...record };
    editingRows.value.add(record.id);
    
    // 设置行为可编辑状态
    record.editable = true;
    
    // 设置编辑回调
    record.onSubmitEdit = async () => {
      return await saveEdit(record);
    };
    
    record.onCancelEdit = () => {
      cancelEdit(record);
    };
  };

  /**
   * 保存编辑
   */
  const saveEdit = async (record: EditRecordRow): Promise<boolean> => {
    if (!record.id) return false;
    
    try {
      savingRows.value.add(record.id);
      
      // 数据验证
      if (!validateRecord(record)) {
        return false;
      }
      
      // 调用保存接口
      const result = await saveOrUpdate(record, true);
      
      if (result.success) {
        // 保存成功，退出编辑状态
        record.editable = false;
        editingRows.value.delete(record.id);
        delete editingData[record.id];
        
        message.success('保存成功');
        return true;
      } else {
        message.error(result.message || '保存失败');
        return false;
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
      return false;
    } finally {
      savingRows.value.delete(record.id);
    }
  };

  /**
   * 取消编辑
   */
  const cancelEdit = (record: EditRecordRow) => {
    if (!record.id) return;
    
    // 恢复原始数据
    const originalData = editingData[record.id];
    if (originalData) {
      Object.assign(record, originalData);
    }
    
    // 退出编辑状态
    record.editable = false;
    editingRows.value.delete(record.id);
    delete editingData[record.id];
  };

  /**
   * 批量保存所有编辑中的行
   */
  const saveAllEditing = async (): Promise<boolean> => {
    const editingRowIds = Array.from(editingRows.value);
    if (editingRowIds.length === 0) {
      message.info('没有需要保存的数据');
      return true;
    }

    let successCount = 0;
    let failCount = 0;

    for (const rowId of editingRowIds) {
      const record = editingData[rowId];
      if (record) {
        const success = await saveEdit(record);
        if (success) {
          successCount++;
        } else {
          failCount++;
        }
      }
    }

    if (failCount === 0) {
      message.success(`批量保存成功，共保存 ${successCount} 条记录`);
      return true;
    } else {
      message.warning(`批量保存完成，成功 ${successCount} 条，失败 ${failCount} 条`);
      return false;
    }
  };

  /**
   * 取消所有编辑
   */
  const cancelAllEditing = () => {
    const editingRowIds = Array.from(editingRows.value);
    editingRowIds.forEach(rowId => {
      const record = editingData[rowId];
      if (record) {
        cancelEdit(record);
      }
    });
    message.info('已取消所有编辑');
  };

  /**
   * 数据验证
   */
  const validateRecord = (record: any): boolean => {
    // 必填字段验证
    if (!record.conclusion) {
      message.error('请选择职业检结论');
      return false;
    }
    
    if (!record.according) {
      message.error('请选择结论依据');
      return false;
    }
    
    if (!record.advice) {
      message.error('请输入处理意见');
      return false;
    }
    
    return true;
  };

  /**
   * 检查是否有未保存的编辑
   */
  const hasUnsavedChanges = computed(() => {
    return editingRows.value.size > 0;
  });

  /**
   * 检查指定行是否正在编辑
   */
  const isRowEditing = (rowId: string): boolean => {
    return editingRows.value.has(rowId);
  };

  /**
   * 检查指定行是否正在保存
   */
  const isRowSaving = (rowId: string): boolean => {
    return savingRows.value.has(rowId);
  };

  return {
    editingRows,
    savingRows,
    editingData,
    hasUnsavedChanges,
    startEdit,
    saveEdit,
    cancelEdit,
    saveAllEditing,
    cancelAllEditing,
    validateRecord,
    isRowEditing,
    isRowSaving,
  };
}
