<template>
  <div class="editable-table-toolbar">
    <a-space>
      <!-- 新增按钮 -->
      <a-button 
        type="primary" 
        @click="handleAdd"
        :disabled="hasUnsavedChanges"
      >
        <template #icon>
          <PlusOutlined />
        </template>
        新增结论
      </a-button>
      
      <!-- 批量保存按钮 -->
      <a-button 
        v-if="hasUnsavedChanges"
        type="primary" 
        ghost
        @click="handleSaveAll"
        :loading="batchSaving"
      >
        <template #icon>
          <SaveOutlined />
        </template>
        保存全部 ({{ editingCount }})
      </a-button>
      
      <!-- 取消全部编辑按钮 -->
      <a-button 
        v-if="hasUnsavedChanges"
        @click="handleCancelAll"
        :disabled="batchSaving"
      >
        <template #icon>
          <CloseOutlined />
        </template>
        取消全部
      </a-button>
      
      <!-- 批量删除按钮 -->
      <a-dropdown v-if="selectedRowKeys.length > 0 && !hasUnsavedChanges">
        <template #overlay>
          <a-menu>
            <a-menu-item key="1" @click="handleBatchDelete">
              <DeleteOutlined />
              删除选中项
            </a-menu-item>
          </a-menu>
        </template>
        <a-button>
          批量操作 ({{ selectedRowKeys.length }})
          <DownOutlined />
        </a-button>
      </a-dropdown>
    </a-space>
    
    <!-- 状态提示 -->
    <div class="toolbar-status" v-if="hasUnsavedChanges">
      <a-alert
        :message="`有 ${editingCount} 条记录正在编辑中，请保存或取消后再进行其他操作`"
        type="warning"
        show-icon
        closable
        @close="handleCancelAll"
      />
    </div>
    
    <!-- 快捷键提示 -->
    <div class="keyboard-shortcuts" v-if="showShortcuts">
      <a-typography-text type="secondary" class="shortcuts-text">
        快捷键：Ctrl+S 保存全部 | Esc 取消全部 | Enter 保存当前行
      </a-typography-text>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { 
  PlusOutlined, 
  SaveOutlined, 
  CloseOutlined, 
  DeleteOutlined, 
  DownOutlined 
} from '@ant-design/icons-vue';

interface Props {
  hasUnsavedChanges: boolean;
  editingCount: number;
  selectedRowKeys: string[];
  showShortcuts?: boolean;
}

interface Emits {
  (e: 'add'): void;
  (e: 'save-all'): void;
  (e: 'cancel-all'): void;
  (e: 'batch-delete'): void;
}

const props = withDefaults(defineProps<Props>(), {
  showShortcuts: true,
});

const emit = defineEmits<Emits>();

const batchSaving = ref(false);

const handleAdd = () => {
  emit('add');
};

const handleSaveAll = async () => {
  batchSaving.value = true;
  try {
    emit('save-all');
  } finally {
    batchSaving.value = false;
  }
};

const handleCancelAll = () => {
  emit('cancel-all');
};

const handleBatchDelete = () => {
  emit('batch-delete');
};
</script>

<style lang="less" scoped>
.editable-table-toolbar {
  margin-bottom: 16px;
  
  .toolbar-status {
    margin-top: 12px;
  }
  
  .keyboard-shortcuts {
    margin-top: 8px;
    
    .shortcuts-text {
      font-size: 12px;
    }
  }
}
</style>
