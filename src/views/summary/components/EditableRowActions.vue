<template>
  <div class="editable-row-actions">
    <!-- 非编辑状态：显示编辑按钮 -->
    <template v-if="!record.editable">
      <a-button 
        type="link" 
        size="small" 
        @click="handleEdit"
        :loading="loading"
        class="edit-btn"
      >
        <template #icon>
          <EditOutlined />
        </template>
        编辑
      </a-button>
    </template>
    
    <!-- 编辑状态：显示保存和取消按钮 -->
    <template v-else>
      <a-space size="small">
        <a-button 
          type="link" 
          size="small" 
          @click="handleSave"
          :loading="saving"
          class="save-btn"
        >
          <template #icon>
            <CheckOutlined />
          </template>
          保存
        </a-button>
        
        <a-button 
          type="link" 
          size="small" 
          @click="handleCancel"
          :disabled="saving"
          class="cancel-btn"
        >
          <template #icon>
            <CloseOutlined />
          </template>
          取消
        </a-button>
      </a-space>
    </template>
    
    <!-- 删除按钮（始终显示） -->
    <a-popconfirm
      title="确定要删除这条记录吗？"
      ok-text="确定"
      cancel-text="取消"
      @confirm="handleDelete"
      placement="topLeft"
    >
      <a-button 
        type="link" 
        size="small" 
        danger
        :disabled="record.editable"
        class="delete-btn"
      >
        <template #icon>
          <DeleteOutlined />
        </template>
        删除
      </a-button>
    </a-popconfirm>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue';
import { EditOutlined, CheckOutlined, CloseOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import type { EditRecordRow } from '/@/components/Table/src/components/editable';

interface Props {
  record: EditRecordRow;
  loading?: boolean;
  saving?: boolean;
}

interface Emits {
  (e: 'edit', record: EditRecordRow): void;
  (e: 'save', record: EditRecordRow): void;
  (e: 'cancel', record: EditRecordRow): void;
  (e: 'delete', record: EditRecordRow): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  saving: false,
});

const emit = defineEmits<Emits>();

const handleEdit = () => {
  emit('edit', props.record);
};

const handleSave = async () => {
  if (props.record.onEdit) {
    const success = await props.record.onEdit(false, true);
    if (success) {
      emit('save', props.record);
    }
  }
};

const handleCancel = () => {
  if (props.record.onEdit) {
    props.record.onEdit(false, false);
  }
  emit('cancel', props.record);
};

const handleDelete = () => {
  emit('delete', props.record);
};
</script>

<style lang="less" scoped>
.editable-row-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  
  .edit-btn {
    color: #1890ff;
    
    &:hover {
      color: #40a9ff;
    }
  }
  
  .save-btn {
    color: #52c41a;
    
    &:hover {
      color: #73d13d;
    }
  }
  
  .cancel-btn {
    color: #8c8c8c;
    
    &:hover {
      color: #bfbfbf;
    }
  }
  
  .delete-btn {
    color: #ff4d4f;
    
    &:hover {
      color: #ff7875;
    }
    
    &:disabled {
      color: #d9d9d9;
    }
  }
}
</style>
