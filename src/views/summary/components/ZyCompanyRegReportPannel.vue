<template>
  <a-card :title="title">
    <template #extra>
      <a-space>
        <a-select
          placeholder="请选择报告模版"
          v-model:value="currentReportTemplateId"
          size="small"
          style="width: 180px"
          :options="reportTemplateList"
          @change="previewReportByTemplate"
        />
        <j-dict-select-tag
          placeholder="请选择岗位类别"
          v-model:value="currentJobStatus"
          allow-clear
          size="small"
          style="width: 160px"
          dictCode="job_status"
        />
        <a-select
          placeholder="请选择科室"
          v-model:value="currentCompanyDeptId"
          allow-clear
          size="small"
          style="width: 160px"
          :options="companyDeptList"
          @change="changeDept"
        />
      </a-space>
    </template>
    <a-spin :spinning="summaryAdviceLoading">
      <div class="viewer-host">
        <div ref="viewerEl" class="viewer-el"></div>
      </div>
    </a-spin>
  </a-card>
</template>

<script lang="ts" setup>
  import { defineExpose, nextTick, ref, watch } from 'vue'; // 添加 watch 导入
  import { message, SelectProps } from 'ant-design-vue';
  import { getZyTeamReport } from '@/views/summary/Summary.api';
  import { getCompanyDeptListByPid } from '@/views/reg/CompanyReg.api';
  import { ensureArjsFonts } from '@/utils/arjs';
  import {JDictSelectTag} from "@/components/Form";
  import {getTemplateById, listByType} from "@/views/basicinfo/Template.api";

  const title = ref<string>('职业检团体报告');
  const viewerEl = ref<HTMLElement | null>(null);
  let viewer: any;

  const summaryAdviceLoading = ref<boolean>(false);
  const currentCompanyReg = ref<any>({});
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);
  const originCompanyDeptList = ref([]);
  const companyDeptList = ref([]);
  const currentReportTemplateId = ref<any>(null);
  const currentCompanyDeptId = ref<any>(null);
  const currentJobStatus = ref<any>(null);

// 添加 watch 监听 currentJobStatus 的变化
watch(currentJobStatus, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    // 延迟执行，确保值已经更新
    setTimeout(() => {
      previewReport(currentReportTemplateId.value, currentCompanyDeptId.value, currentJobStatus.value);
    }, 100);
  }
});

  async function open(reg) {
    currentCompanyReg.value = reg;
    title.value = `职业检团体报告 - ${reg.regName}`;
    currentJobStatus.value = null;
    await preloadDeptsAndTemplates();
    if (currentReportTemplateId.value) {
      // await previewReport(currentReportTemplateId.value, currentCompanyDeptId.value);
    } else {
      message.error('没有可用的团体报告模版！');
    }
  }

  async function preloadDeptsAndTemplates() {
    // 科室
    const companyDeptRes = await getCompanyDeptListByPid({
      pid: currentCompanyReg.value.companyId,
      pageSize: 10000,
    });
    if (companyDeptRes?.length > 0) {
      originCompanyDeptList.value = companyDeptRes;
      currentCompanyDeptId.value = companyDeptRes[0].id;
      companyDeptList.value = companyDeptRes.map((item) => ({ value: item.id, label: item.name }));
    }
    // 模板
    const reportTemplateRes = await listByType({ type: '报告', regType: '团体' });
    if (reportTemplateRes?.success) {
      // 优先筛选职业病体检模板
      const zyTemplates = (reportTemplateRes.result || []).filter((t: any) => t.examCategory === '职业病体检');
      const finalList = zyTemplates.length > 0 ? zyTemplates : reportTemplateRes.result;
      originReportTemplateList.value = finalList;
      if (finalList?.length > 0) {
        currentReportTemplateId.value = finalList[0].id;
        reportTemplateList.value = finalList.map((item: any) => ({ value: item.id, label: item.name }));
      }
    }
  }

  async function previewReport(templateId, deptId, jobStatus) {
    // 添加必要的验证
    if (!jobStatus) {
      message.warn('请选择岗位类别！');
      return;
  }
    if (!currentCompanyReg.value?.id) {
      message.warn('请选择单位！');
      return;
    }
    if (!templateId) {
      message.warn('请选择报告模板！');
      return;
    }
    try {
      summaryAdviceLoading.value = true;
      const templateRes = await getTemplateById({ id: templateId });
      const template = JSON.parse(templateRes.content);
      const reportDataRes = await getZyTeamReport({
      companyRegId: currentCompanyReg.value.id,
      companyDeptId: deptId,
        jobStatus: jobStatus
      });
      summaryAdviceLoading.value = false;
      if (reportDataRes.success) {
        const reportData = reportDataRes.result;
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);

        const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
        if (!ARJS) return;
        await ensureArjsFonts();
        await nextTick();
        const host = viewerEl.value as HTMLElement;
        if (!host) return;
        if (!viewer) {
          viewer = new ARJS.ReportViewer.Viewer(host, {
            language: 'zh',
            viewMode: 'Continuous',
            ErrorHandler: (error) => {
              console.error(error?.message || error);
              return true;
            },
          });
          viewer.viewMode = 'Continuous';
        }
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];
        viewer.open(template);
        viewer.viewMode = 'Continuous';
      } else {
        message.error('获取职业团体报告数据失败!');
      }
    } catch (e) {
      console.error(e);
      summaryAdviceLoading.value = false;
      message.error('生成报告时发生错误！');
    }
  }

  function previewReportByTemplate() {
    previewReport(currentReportTemplateId.value, currentCompanyDeptId.value, currentJobStatus.value);
  }
  function changeDept() {
    previewReport(currentReportTemplateId.value, currentCompanyDeptId.value, currentJobStatus.value);
  }

  defineExpose({ open });
</script>

<style lang="less" scoped>
  .viewer-host {
    width: 100%;
    height: calc(100vh - 200px);
  }
  .viewer-el {
    width: 100%;
    height: 100%;
  }
</style>
