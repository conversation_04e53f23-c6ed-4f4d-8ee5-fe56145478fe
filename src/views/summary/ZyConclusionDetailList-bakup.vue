<template>
  <div class="p-2">
    <!--查询区域-->
    <a-card size="small" title="危害因素" class="mb-2">
      <a-row :gutter="8">
        <a-col :span="5" v-for="risk in riskFactorList">
          <a-button type="dashed" v-if="risk.added" @click="message.warning('请勿重复添加')"
            ><CheckOutlined style="color: #52c41a" />已添加 {{ risk.name }}
          </a-button>
          <a-button type="dashed" danger @click="handleAddFromRisk(risk)" v-else
            ><PlusOutlined style="color: #faad14" />待添加 {{ risk.name }}
          </a-button>
        </a-col>
      </a-row>
    </a-card>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增结论</a-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined" />
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
      <template #bodyCell="{ column, record, index, text }"> </template>
    </BasicTable>
    <!-- 表单区域 -->
    <ZyConclusionDetailModal ref="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="occu-zyConclusionDetail" setup>
  import { computed, inject, reactive, ref, watch } from 'vue';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns } from './ZyConclusionDetail.data';
  import { batchDelete, deleteOne, getExportUrl, getImportUrl, list } from './ZyConclusionDetail.api';
  import ZyConclusionDetailModal from './components/ZyConclusionDetailModal.vue';
  import { useUserStore } from '/@/store/modules/user';
  import { CheckOutlined, PlusOutlined } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { message } from 'ant-design-vue';

  const customerReg4Summary = inject('customerReg4Summary');
  const customerSummary = inject('customerSummary');
  const riskFactorList = computed(() => {
    return customerReg4Summary.value?.riskFactorList || [];
  });
  const { createErrorModal } = useMessage();

  const queryParam = reactive<any>({});
  const registerModal = ref();

  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      api: list,
      columns,
      canResize: false,
      useSearchForm: false,
      size: 'small',
      actionColumn: {
        width: 100,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        let param = {
          customerRegId: customerReg4Summary.value.id,
        };
        return Object.assign(params, queryParam, param);
      },
      afterFetch: (dataSource) => {
        riskFactorList.value.forEach((risk) => {
          let record = dataSource.find((item) => item.riskFactorId === risk.id);
          if (record) {
            risk.added = true;
          } else {
            risk.added = false;
          }
        });
        return dataSource;
      },
    },
    exportConfig: {
      name: '职业检总检详细',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] =
    tableContext;

  function checkSummary() {
    return new Promise((resolve, reject) => {
      if (!customerSummary.value.id) {
        createErrorModal({ title: '提示', content: '<span style="font-weight: bold">请先保存汇总和建议！</span>' });
        reject();
      } else {
        resolve();
      }
    });
  }

  function handleAddFromRisk(risk) {
    checkSummary().then(() => {
      registerModal.value.disableSubmit = false;
      registerModal.value.add({ riskFactorId: risk.id, riskCode: risk.code, riskFactor: risk.name, mainFlag: '1' });
    });
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    checkSummary().then(() => {
      registerModal.value.disableSubmit = false;
      registerModal.value.add({ mainFlag: '1' });
    });
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }

  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }

  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }

  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        },
      },
    ];
  }

  watch(
    () => customerReg4Summary.value?.id,
    (newValue, oldValue) => {
      if (newValue) {
        reload();
      } else {
        getDataSource.value = [];
      }
    }
  );
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust {
      min-width: 100px !important;
    }
    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }
</style>
