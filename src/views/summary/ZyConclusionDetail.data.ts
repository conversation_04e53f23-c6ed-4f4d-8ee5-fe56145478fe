import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

// 可编辑列配置
export const columns: BasicColumn[] = [
    {
      title: '主要',
      align: 'center',
      dataIndex: 'mainFlag',
      width: 80,
      editRow: true,
      editComponent: 'Select',
      editComponentProps: {
        options: [
          { label: '主要', value: '1' },
          { label: '次要', value: '0' },
        ],
      },
      customRender: ({ text }) => {
        return text == '1' ? '主要' : '次要';
      },
    },
    {
      title: '危害因素',
      align: 'center',
      dataIndex: 'riskFactor',
      width: 120,
      editRow: true,
      editComponent: 'JAsyncSearchSelect',
      editComponentProps: {
        dict: 'zy_risk_factor,name,code',
        placeholder: '请选择危害因素',
      },
    },
    {
      title: '工种',
      align: 'center',
      dataIndex: 'workType',
      width: 100,
      editRow: true,
      editComponent: 'Input',
      editComponentProps: {
        placeholder: '请输入工种',
      },
    },
    {
      title: '职业检结论',
      align: 'center',
      dataIndex: 'conclusion',
      width: 180,
      editRow: true,
      editComponent: 'JDictSelectTag',
      editComponentProps: {
        dictCode: 'zy_conclusion_dict,dict_text,code',
        placeholder: '请选择职业检结论',
      },
    },
    {
      title: '处理意见',
      align: 'center',
      dataIndex: 'advice',
      width: 200,
      editRow: true,
      editComponent: 'Input',
      editComponentProps: {
        type: 'textarea',
        rows: 2,
        placeholder: '请输入处理意见',
      },
    },
    {
      title: '职业病',
      align: 'center',
      dataIndex: 'zyDisease',
      width: 150,
      editRow: true,
      editComponent: 'JSelectMultiple',
      editComponentProps: {
        type: 'list_multi',
        dictCode: 'zy_disease_dict,dict_text,code',
        placeholder: '请选择职业病',
        triggerChange: false,
      },
    },
    {
      title: '职业禁忌证',
      align: 'center',
      dataIndex: 'zySymptom',
      width: 150,
      editRow: true,
      editComponent: 'JSelectMultiple',
      editComponentProps: {
        type: 'list_multi',
        dictCode: 'zy_taboo_symptom,name,code',
        placeholder: '请选择职业禁忌证',
        triggerChange: false,
      },
    },
    {
      title: '结论依据',
      align: 'center',
      dataIndex: 'according',
      width: 180,
      editRow: true,
      editComponent: 'JDictSelectTag',
      editComponentProps: {
        dictCode: 'zy_conclusion_according,content,content',
        placeholder: '请选择结论依据',
      },
    },
  ];

//查询数据
export const searchFormSchema: FormSchema[] = [];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '登记ID',
    field: 'customerRegId',
    component: 'Input',
  },
  {
    label: '危害因素',
    field: 'riskName',
    component: 'Input',
  },
  {
    label: '工种',
    field: 'workType',
    component: 'Input',
  },
  {
    label: '职业检结论',
    field: 'examSummary',
    component: 'Input',
  },
  {
    label: '处理意见',
    field: 'treatmentAdvice',
    component: 'Input',
  },
  {
    label: '职业病',
    field: 'zyDisease',
    component: 'Input',
  },
  {
    label: '职业禁忌证',
    field: 'zySymptom',
    component: 'Input',
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

// 高级查询数据
export const superQuerySchema = {
  customerRegId: { title: '登记ID', order: 0, view: 'text', type: 'string' },
  riskName: { title: '危害因素', order: 1, view: 'text', type: 'string' },
  workType: { title: '工种', order: 2, view: 'text', type: 'string' },
  examSummary: { title: '职业检结论', order: 3, view: 'text', type: 'string' },
  treatmentAdvice: { title: '处理意见', order: 4, view: 'text', type: 'string' },
  zyDisease: { title: '职业病', order: 5, view: 'text', type: 'string' },
  zySymptom: { title: '职业禁忌证', order: 6, view: 'text', type: 'string' },
};
