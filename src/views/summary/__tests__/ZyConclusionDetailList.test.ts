import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createPinia, setActivePinia } from 'pinia';
import ZyConclusionDetailList from '../ZyConclusionDetailList.vue';

// Mock dependencies
vi.mock('/@/hooks/system/useListPage', () => ({
  useListPage: () => ({
    prefixCls: 'test',
    tableContext: [
      () => {},
      {
        reload: vi.fn(),
        collapseAll: vi.fn(),
        updateTableDataRecord: vi.fn(),
        findTableDataRecord: vi.fn(),
        getDataSource: { value: [] },
      },
      {
        rowSelection: {},
        selectedRowKeys: { value: [] },
      },
    ],
    onExportXls: vi.fn(),
    onImportXls: vi.fn(),
  }),
}));

vi.mock('/@/hooks/web/useMessage', () => ({
  useMessage: () => ({
    createErrorModal: vi.fn(),
  }),
}));

vi.mock('ant-design-vue', () => ({
  message: {
    warning: vi.fn(),
    success: vi.fn(),
    error: vi.fn(),
  },
}));

describe('ZyConclusionDetailList', () => {
  beforeEach(() => {
    setActivePinia(createPinia());
  });

  it('应该正确渲染组件', () => {
    const wrapper = mount(ZyConclusionDetailList, {
      global: {
        provide: {
          customerReg4Summary: { value: { riskFactorList: [] } },
          customerSummary: { value: { id: '1' } },
        },
        stubs: {
          'BasicTable': true,
          'EditableTableToolbar': true,
          'EditableRowActions': true,
          'a-card': true,
          'a-row': true,
          'a-col': true,
          'a-button': true,
          'CheckOutlined': true,
          'PlusOutlined': true,
        },
      },
    });

    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.p-2').exists()).toBe(true);
  });

  it('应该显示危害因素卡片', () => {
    const riskFactorList = [
      { id: '1', name: '噪声', added: false },
      { id: '2', name: '粉尘', added: true },
    ];

    const wrapper = mount(ZyConclusionDetailList, {
      global: {
        provide: {
          customerReg4Summary: { value: { riskFactorList } },
          customerSummary: { value: { id: '1' } },
        },
        stubs: {
          'BasicTable': true,
          'EditableTableToolbar': true,
          'EditableRowActions': true,
          'a-card': true,
          'a-row': true,
          'a-col': true,
          'a-button': true,
          'CheckOutlined': true,
          'PlusOutlined': true,
        },
      },
    });

    const card = wrapper.find('a-card-stub');
    expect(card.exists()).toBe(true);
    expect(card.attributes('title')).toBe('危害因素');
  });

  it('应该正确处理编辑状态', async () => {
    const wrapper = mount(ZyConclusionDetailList, {
      global: {
        provide: {
          customerReg4Summary: { value: { riskFactorList: [] } },
          customerSummary: { value: { id: '1' } },
        },
        stubs: {
          'BasicTable': true,
          'EditableTableToolbar': true,
          'EditableRowActions': true,
          'a-card': true,
          'a-row': true,
          'a-col': true,
          'a-button': true,
          'CheckOutlined': true,
          'PlusOutlined': true,
        },
      },
    });

    const vm = wrapper.vm as any;
    
    // 测试编辑状态管理
    expect(vm.hasUnsavedChanges).toBe(false);
    expect(vm.editingRows.size).toBe(0);
  });

  it('应该正确处理键盘快捷键', async () => {
    const wrapper = mount(ZyConclusionDetailList, {
      global: {
        provide: {
          customerReg4Summary: { value: { riskFactorList: [] } },
          customerSummary: { value: { id: '1' } },
        },
        stubs: {
          'BasicTable': true,
          'EditableTableToolbar': true,
          'EditableRowActions': true,
          'a-card': true,
          'a-row': true,
          'a-col': true,
          'a-button': true,
          'CheckOutlined': true,
          'PlusOutlined': true,
        },
      },
    });

    const vm = wrapper.vm as any;
    
    // 模拟Ctrl+S快捷键
    const event = new KeyboardEvent('keydown', {
      key: 's',
      ctrlKey: true,
    });
    
    vm.handleKeydown(event);
    
    // 验证事件被正确处理
    expect(event.defaultPrevented).toBe(true);
  });

  it('应该正确验证数据', () => {
    const wrapper = mount(ZyConclusionDetailList, {
      global: {
        provide: {
          customerReg4Summary: { value: { riskFactorList: [] } },
          customerSummary: { value: { id: '1' } },
        },
        stubs: {
          'BasicTable': true,
          'EditableTableToolbar': true,
          'EditableRowActions': true,
          'a-card': true,
          'a-row': true,
          'a-col': true,
          'a-button': true,
          'CheckOutlined': true,
          'PlusOutlined': true,
        },
      },
    });

    const vm = wrapper.vm as any;
    
    // 测试数据验证
    const validRecord = {
      conclusion: '正常',
      according: '体检标准',
      advice: '建议定期复查',
    };
    
    const invalidRecord = {
      conclusion: '',
      according: '',
      advice: '',
    };
    
    expect(vm.validateRecord(validRecord)).toBe(true);
    expect(vm.validateRecord(invalidRecord)).toBe(false);
  });
});
