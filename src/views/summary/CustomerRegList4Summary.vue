<template>
  <div>
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="4">
          <a-col :span="4">
            <a-form-item name="examNo" label="体检号">
              <a-input allow-clear size="middle" placeholder="请输入体检号" v-model:value="queryParam.examNo" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item name="name" label="姓名">
              <a-input allow-clear size="middle" placeholder="请输入姓名" v-model:value="queryParam.name" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item name="idCard" label="证件号">
              <a-input allow-clear size="middle" placeholder="请输入证件号" v-model:value="queryParam.idCard" />
            </a-form-item>
          </a-col>
          <a-col :span="4">
            <a-form-item label="总检状态">
              <j-dict-select-tag dict-code="summary_status" size="middle" placeholder="总检状态" v-model:value="queryParam.summaryStatus" />
            </a-form-item>
          </a-col>

          <!--          <a-col :lg="12">
            <a-form-item name="status">
              <j-dict-select-tag dict-code="checkStatus" size="middle" placeholder="检查状态" v-model:value="queryParam.checkState" />
            </a-form-item>
          </a-col>-->

          <template v-if="toggleSearchStatus">
            <a-col :span="4">
              <a-form-item label="报告状态">
                <j-dict-select-tag dict-code="report_print_status" size="middle" placeholder="报告状态" v-model:value="queryParam.printStatus" />
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item label="医生">
                <a-input-group compact>
                  <j-async-search-select
                    placeholder="请选择医生"
                    dict="sys_user where del_flag=0,realname,username"
                    v-model:value="queryParam.doctor"
                    :allow-clear="true"
                    style="width: 60%"
                  />
                  <a-select v-model:value="queryParam.doctorType" placeholder="医生类型" style="width: 40%">
                    <a-select-option value="">无</a-select-option>
                    <a-select-option value="初检">初检</a-select-option>
                    <a-select-option value="主检">主检</a-select-option>
                    <a-select-option value="审核">审核</a-select-option>
                    <a-select-option value="指定的主检">指定的主检</a-select-option>
                  </a-select>
                </a-input-group>
              </a-form-item>
            </a-col>

            <a-col :span="4">
              <a-form-item label="日期类型">
                <a-select v-model:value="queryParam.dateType" placeholder="日期类型" style="width: 100%">
                  <a-select-option value="登记日期">登记日期</a-select-option>
                  <a-select-option value="初检日期">初检日期</a-select-option>
                  <a-select-option value="主检日期">主检日期</a-select-option>
                  <a-select-option value="审核日期">审核日期</a-select-option>
                  <a-select-option value="打印日期">打印日期</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item name="itemId" label="排序">
                <a-select v-model:value="queryParam.sortOrder" placeholder="择排序方式">
                  <template #suffixIcon>
                    <SortAscendingOutlined />
                  </template>
                  <a-select-option value="降序">降序</a-select-option>
                  <a-select-option value="升序">升序</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>

            <a-col :span="6">
              <a-form-item label="日期范围">
                <a-range-picker
                  v-model:value="regDateRange"
                  placement="日期范围"
                  @change="searchQuery"
                  :presets="rangePresets"
                  :allow-clear="false"
                />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item name="itemId" label="积案查询">
                <a-input-number v-model:value="queryParam.daysFromReg" placeholder="积案天数" />
              </a-form-item>
            </a-col>

            <a-col :span="4">
              <a-form-item name="itemId" label="过滤">
                <a-select v-model:value="queryParam.filterStatus" placeholder="数据过滤">
                  <a-select-option value="">全部</a-select-option>
                  <a-select-option value="已交表">已交表</a-select-option>
                  <a-select-option value="已预检">已预检</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <!--<a-row :gutter="4">
            <a-col :span="24">
              <a-form-item label="日期范围">
                <a-range-picker
                  v-model:value="regDateRange"
                  placement="日期类型"
                  @change="searchQuery"
                  :presets="rangePresets"
                  :allow-clear="false"
                />
              </a-form-item>
            </a-col>
          </a-row>-->
            <a-col :span="4">
              <a-form-item label="预检方式">
                <j-dict-select-tag dict-code="pre_summary_method" size="middle" placeholder="预检方式" v-model:value="queryParam.preSummaryMethod" />
              </a-form-item>
            </a-col>
            <a-col :span="4">
              <a-form-item label="初检方式">
                <j-dict-select-tag
                  dict-code="initail_summary_method"
                  size="middle"
                  placeholder="初检方式"
                  v-model:value="queryParam.initailSummaryMethod"
                />
              </a-form-item>
            </a-col>
            <a-col :lg="4">
              <a-form-item label="所属预约">
                <j-async-search-select
                  size="middle"
                  placeholder="所属预约"
                  @change="getTeamList"
                  v-model:value="queryParam.companyRegId"
                  dict="company_reg,reg_name,id"
                  :allow-clear="true"
                />
              </a-form-item>
            </a-col>
            <a-col :lg="4">
              <a-form-item label="体检类型">
                <j-dict-select-tag dict-code="examination_type" size="middle" placeholder="体检类型" v-model:value="queryParam.examCatory" />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="6" :md="6" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-button size="middle" type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
              <a-button size="middle" type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px"
                >重置</a-button
              >
              <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                {{ toggleSearchStatus ? '收起' : '展开' }}
                <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
              </a>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="null">
      <!--插槽:table标题-->
      <template #bodyCell="{ column, text, record, index }">
        <template v-if="column.dataIndex == 'initailSummaryMethod'">
          <a-tag :bordered="false" color="green" v-if="text == 'AI'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '手动'"> {{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'preSummaryMethod'">
          <a-tag :bordered="false" color="green" v-if="text == '自动'"> {{ text }}</a-tag>
          <a-tag :bordered="false" color="blue" v-if="text == '手动'"> {{ text }}</a-tag>
        </template>
        <template v-if="column.dataIndex == 'summaryStatus'">
          <a-tag :bordered="false" :color="record.summaryStatusColor"> {{ record.summaryStatus }}</a-tag>
        </template>
        <template v-else-if="column.dataIndex == 'checkStatus'">
          <a-space>
            <template v-for="status in record.statusStatList">
              <a-popover :title="status.status + ':' + status.count + '项'" trigger="click">
                <template #content>
                  <div style="width: 20vw">
                    <a-table
                      :rowKey="(_, index) => index"
                      :dataSource="status.items"
                      :showHeader="false"
                      size="small"
                      :pagination="false"
                      :scroll="{ y: 200 }"
                    >
                      <a-table-column key="item">
                        <!-- Use the default slot to render each string -->
                        <template #default="{ record, index }"> {{ index + 1 }}、 {{ record }}</template>
                      </a-table-column>
                    </a-table>
                  </div>
                </template>
                <a-tag :bordered="false" :color="status.color" style="cursor: pointer">{{ status.status }}({{ status.count }}项) </a-tag>
              </a-popover>
            </template>
          </a-space>
        </template>
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="customer-reg-list4-summary" setup>
  import { onMounted, reactive, ref, watch } from 'vue';
  import type { RangeValue } from '#/types';
  import { BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns4Summary } from '/@/views/reg/CustomerReg.data';
  import { listReg, updateReportEditLockFlag } from '/@/views/summary/CustomerRegSummary.api.ts';
  import { companyTeamList } from '/@/views/reg/CompanyReg.api';
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { message, theme } from 'ant-design-vue';
  import { useMessage } from '@/hooks/web/useMessage';
  import { JAsyncSearchSelect } from '@/components/Form';
  import dayjs from 'dayjs';
  import { SortAscendingOutlined } from '@ant-design/icons-vue';
  import { getRegById } from '@/views/summary/Summary.api';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';

  const toggleSearchStatus = ref<boolean>(false);
  const userStore = useUserStore();
  const { token } = theme.useToken();
  const { createConfirm, notification } = useMessage();
  const addCurrentUser2QueryParma = ref<string>('0');
  const emit = defineEmits(['rowClick', 'readIdcard', 'add', 'batchRegOk', 'reloadRecord']);
  const formRef = ref();
  const queryParam = reactive<any>({
    status: '已登记',
    sortOrder: '降序',
    dateType: '登记日期',
    doctorType: '指定的主检',
    //doctor: userStore.getUserInfo.username ?? null,
  });
  //根据addCurrentUser2QueryParma判断是否添加当前用户到查询条件
  watch(
    () => addCurrentUser2QueryParma.value,
    (newVal) => {
      if (newVal == '1') {
        queryParam.doctor = userStore.getUserInfo.username ?? null;
      } else {
        queryParam.doctor = null;
      }
    }
  );

  const regDateRange = ref<RangeValue>();
  const rangePresets = ref([
    { label: '今天', value: [dayjs(), dayjs().add(1, 'd')] },
    { label: '过去一周', value: [dayjs().add(-7, 'd'), dayjs()] },
    { label: '过去二周', value: [dayjs().add(-14, 'd'), dayjs()] },
    { label: '过去30天', value: [dayjs().add(-30, 'd'), dayjs()] },
    { label: '过去90天', value: [dayjs().add(-90, 'd'), dayjs()] },
    { label: '过去一年', value: [dayjs().add(-1, 'y'), dayjs()] },
    { label: '过去两年', value: [dayjs().add(-2, 'y'), dayjs()] },
    { label: '过去二十年', value: [dayjs().add(-20, 'y'), dayjs()] },
  ]);

  /**表格相关操作*/
  const currentRow = ref<any>({});
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      showTableSetting: false,
      showIndexColumn: true,
      api: listReg,
      columns: columns4Summary,
      canResize: true,
      canColDrag: true,
      useSearchForm: false,
      clickToRowSelect: false,
      size: 'small',
      striped: true,
      actionColumn: {
        width: 80,
        fixed: 'right',
      },
      pagination: {
        pageSize: 15,
      },
      customRow: (record) => {
        return {
          onDblclick: () => {
            currentRow.value = record;
            emit('rowClick', record);
          },
        };
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
      afterFetch: (dataSource) => {
        if (currentRow.value && dataSource.length > 0) {
          let record = dataSource.find((item) => item.id === currentRow.value.id);
          if (record) {
            currentRow.value = record;
            emit('reloadRecord', record);
          }
        }
        return dataSource;
      },
      rowClassName: (record) => {
        return currentRow.value && currentRow.value.id === record.id ? 'row-selected' : '';
      },
    },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }] = tableContext;

  const teamList = ref<any[]>([]);
  const labelCol = reactive({
    span: 7,
  });
  const wrapperCol = reactive({
    span: 17,
  });

  function getTeamList(companyRegId) {
    if (!companyRegId) {
      teamList.value = [];
      queryParam.teamId = '';
      return;
    }
    teamList.value = [];
    queryParam.teamId = '';
    companyTeamList({ companyRegId: companyRegId, pageSize: 10000 }).then((res) => {
      teamList.value = res.records;
    });
  }

  /**
   * 查询
   */
  async function searchQuery() {
    if (regDateRange.value) {
      queryParam.dateStart = regDateRange.value[0].format('YYYY-MM-DD') + ' 00:00:00';
      queryParam.dateEnd = regDateRange.value[1].format('YYYY-MM-DD') + ' 23:59:59';
    }
    await reload({ page: 1 });
  }

  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    //刷新数据
    reload();
  }

  function reloadPage() {
    reload();
  }

  function reloadCurrent() {
    if (currentRow.value.id) {
      getRegById({ regId: currentRow.value.id }).then((record) => {
        //console.log('reloadCurrent', record);
        Object.assign(currentRow.value, record);
        //currentRow.value = record;
        emit('rowClick', record);
      });
    }
  }

  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    if (record.reportEditLockFlag == '0') {
      updateReportEditLockFlag({ customerRegId: record.id, reportEditLockFlag: '1' }).then((res) => {
        if (res.success) {
          record.reportEditLockFlag = '1';
          let user = userStore.getUserInfo;
          record.reportEditLockBy = user.username;
          record.reportEditLocker = user.realname;
          record.reportEditLockTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
          emit('rowClick', record);
        } else {
          message.error(res.message);
        }
      });
    } else {
      message.error('该记录已被锁定，无法编辑！');
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record) {
    let action = [];
    if (record.reportEditLockFlag == '1') {
      action.push({
        label: '已锁',
        onClick: null,
      });
    } else {
      if (!(record.summaryStatus == '审核通过' || record.summaryStatus == '驳回')) {
        action.push({
          label: '锁定',
          onClick: handleEdit.bind(null, record),
        });
      }
    }
    return action;
  }

  onMounted(() => {
    const savedSortOrder = localStorage.getItem('summaryRegDateType');
    queryParam.dateType = savedSortOrder || '降序';

    querySysParamByCode({ code: 'summary_list_condition_current_user' }).then((res) => {
      addCurrentUser2QueryParma.value = res.result;
    });
  });

  watch(
    () => queryParam.dateType,
    (newSortOrder) => {
      localStorage.setItem('summaryRegDateType', newSortOrder);
    }
  );

  defineExpose({
    searchQuery,
    reloadPage,
    reloadCurrent,
  });
</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;

    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 8px;
      white-space: nowrap;
    }

    .query-group-cust {
      min-width: 100px !important;
    }

    .query-group-split-cust {
      width: 30px;
      display: inline-block;
      text-align: center;
    }
  }

  :deep(.row-selected td:first-child) {
    border-left: solid 5px v-bind('token.colorPrimary');
  }
</style>
