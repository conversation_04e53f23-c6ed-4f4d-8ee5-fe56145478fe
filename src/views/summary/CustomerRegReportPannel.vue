<template>
  <a-card>
    <template #title>
      <a-space>
        <a-typography-title :level="5">{{ title }}</a-typography-title>
        <a-select
          @change="previewReportByTemplate"
          placeholder="请选择报告模版"
          v-model:value="selectedTemplateId"
          size="small"
          style="width: 150px; margin-left: 20px"
          :options="reportTemplateList"
        />
      </a-space>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="printReport" :disabled="!customerReg?.id">打印</a-button>
        <a-button type="primary" @click="handleVerified" :loading="btnLoading" :disabled="btnLoading || customerReg.ereportStatus != '待审阅'"
          >审阅通过</a-button
        >
      </a-space>
    </template>
    <a-spin :spinning="summaryAdviceLoading">
      <div class="viewer-host">
        <div ref="viewerEl" class="viewer-el"></div>
      </div>
    </a-spin>
  </a-card>
</template>

<script lang="ts" setup>
  import { computed, defineExpose, onMounted, ref, nextTick } from 'vue';
  import { message, SelectProps } from 'ant-design-vue';
  import { getTemplateById, listByType } from '@/views/basicinfo/Template.api';
  import { getReportData } from '@/views/summary/CustomerRegSummary.api';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';
  import { uploadFile } from '@/utils/upload';
  import { updateReportPrintStatusByIds, verifyAndSaveReport, verifyBatch } from '@/views/reg/CustomerReg.api';
  import { ensureArjsFonts } from '@/utils/arjs';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';

  const title = ref<string>('');
  const visible = ref<boolean>(false);
  const viewerEl = ref<HTMLElement | null>(null);
  const customerReg = ref<any>({});
  const selectedTemplateId = ref(null);
  const btnLoading = ref<boolean>(false);
  const matchedTemplateId = computed(() => {
    let reg = customerReg.value;
    if (!reg.id) {
      return null;
    }
    let regTemplateList = [];
    let regReportTemplateId = reg.reportTemplateId;
    if (regReportTemplateId) {
      regTemplateList = originReportTemplateList.value.filter((item) => item.id == regReportTemplateId);
    } else {
      regTemplateList = originReportTemplateList.value.filter((item) => item.examCategory == reg.examCategory);
    }

    return regTemplateList[0]?.id || null;
  });

  const summaryAdviceLoading = ref<boolean>(false);
  const generatePdfBackendsFlag = ref<string>('');

  let currentReport = {};
  let preloadPdfData: Blob | null = null;

  let viewer: any;
  // 保留导出 PDF 能力用于“打印”直打
  const pageReport = (window as any).MESCIUS?.ActiveReportsJS?.Core ? new (window as any).MESCIUS.ActiveReportsJS.Core.PageReport() : null;
  const PDF = (window as any).MESCIUS?.ActiveReportsJS?.PdfExport;

  function exportPdf(report) {
    return new Promise((resolve, reject) => {
      const settings = {
        info: {
          title: `${customerReg.value.name}-${customerReg.value.examNo}体检报告`,
          author: '',
        },
        pdfVersion: '1.7',
      };
      const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
      if (!ARJS || !pageReport || !PDF) {
        reject(new Error('ActiveReportsJS 未加载'));
        return;
      }
      ensureArjsFonts()
        .then(() => pageReport.load(report))
        .then(() => pageReport.run())
        .then((pageDocument) => PDF.exportDocument(pageDocument, settings))
        .then((result) => resolve(result.data))
        .catch((error) => reject(error));
    });
  }

  /**报告预览打印*/
  const reportTemplateList = ref<SelectProps['options']>([]);
  const originReportTemplateList = ref([]);

  const emit = defineEmits(['register', 'success']);

  async function open(reg) {
    visible.value = true;
    customerReg.value = reg;
    title.value = `体检报告-${reg.name}`;
    await ensureTemplatesLoaded();
    if (matchedTemplateId.value) {
      selectedTemplateId.value = matchedTemplateId.value;
      await previewReport(selectedTemplateId.value);
    } else {
      message.warn('未找到对应的报告模板！');
    }
  }

  async function previewReport(templateId) {
    if (!customerReg.value.id) {
      message.warn('请选择体检记录！');
      return;
    }
    if (!matchedTemplateId.value) {
      message.warn('请选择报告模板！');
      return;
    }
    //获取报告模版内容
    try {
      summaryAdviceLoading.value = true;
      const templateRes = await getTemplateById({ id: templateId });
      let template = JSON.parse(templateRes.content);

      const reportDataRes = await getReportData({ customerRegId: customerReg.value.id });
      summaryAdviceLoading.value = false;
      if (reportDataRes.success) {
        let reportData = reportDataRes.result;

        //需要处理报告中的图片
        //console.log(reportData);
        if (reportData.customerReg.customerAvatar) {
          reportData.customerReg.customerAvatar = getFileAccessHttpUrl(reportData.customerReg.customerAvatar);
        }
        if (reportData.reportImgList?.length > 0) {
          reportData.reportImgList?.forEach((item) => {
            item.text = getFileAccessHttpUrl(item.text);
          });
        }
        //console.log(reportData.groupByFunctionMap?.lab_exam);

        if (reportData.groupByFunctionMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap?.lab_exam?.length > 0) {
          reportData.groupByFunctionMap.lab_exam.forEach((group) => {
            if (group.reportPicBeanList?.length > 0) {
              group.reportPicBeanList.forEach((item) => {
                item.text = getFileAccessHttpUrl(item.text);
              });
            }
          });
        }

        if (reportData.groupByFunctionPicMap) {
          Object.keys(reportData.groupByFunctionPicMap).forEach((key) => {
            reportData.groupByFunctionPicMap[key].forEach((item) => {
              item.text = getFileAccessHttpUrl(item.text);
            });
          });
        }

        //console.log(reportData.groupByFunctionMap.lab_exam);
        if (reportData.summaryAdvice?.auditorSignPic) {
          reportData.summaryAdvice.auditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.auditorSignPic);
        }
        if (reportData.summaryAdvice?.preAuditorSignPic) {
          reportData.summaryAdvice.preAuditorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.preAuditorSignPic);
        }
        if (reportData.summaryAdvice?.creatorSignPic) {
          reportData.summaryAdvice.creatorSignPic = getFileAccessHttpUrl(reportData.summaryAdvice.creatorSignPic);
        }
        //console.log('=======reportData==========', reportData);
        template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reportData);
        currentReport = template;
        //开启异步预载pdfData

        /* preloading.value = true;
        exportPdf(template).then((pdfData) => {
          preloadPdfData = pdfData;
          preloading.value = false;
          //message.warn('报告预载成功！');
        });*/

        // 使用全局 Viewer 内嵌展示
        const ARJS = (window as any).MESCIUS?.ActiveReportsJS;
        if (!ARJS) return;
        await ensureArjsFonts();
        await nextTick();
        // 确保 viewer 容器有尺寸
        const host = viewerEl.value as HTMLElement;
        if (!host) return;
        if (!viewer) {
          viewer = new ARJS.ReportViewer.Viewer(host, {
            language: 'zh',
            viewMode: 'Continuous',
            ErrorHandler: (error) => {
              console.error(error?.message || error);
              return true;
            },
          });
          viewer.viewMode = 'Continuous';
        }
        // 强制重建文档
        viewer.resetDocument();
        viewer.availableExports = ['pdf'];
        viewer.open(template);
        viewer.viewMode = 'Continuous';
      } else {
        message.error('获取报告数据失败');
      }
    } catch (error) {
      console.error(error);
    }
  }

  function printReport() {
    if (!currentReport) {
      message.warn('请先预览报告！');
      return;
    }
    if (viewer) viewer.print();
    updateReportPrintStatusByIds({ id: customerReg.value.id }).then((res) => {
      if (res.success) {
        emit('success');
      } else {
        message.error('操作失败！');
      }
    });
  }

  async function handleVerified() {
    if (!customerReg.value.id) {
      message.warn('请选择体检记录！');
      return;
    }
    //如果已审阅过，给出提示
    if (customerReg.value.ereportStatus == '待发送') {
      message.warn('该体检记录已审阅！');
      return;
    }

    btnLoading.value = true;
    verifyBatch({ ids: [{ id: customerReg.value.id, templateId: selectedTemplateId.value }] })
      .then((res) => {
        if (res.success) {
          message.success('操作成功！');
          emit('success');
        } else {
          message.error('操作失败！');
        }
      })
      .finally(() => {
        btnLoading.value = false;
      });
  }

  async function handleVerifiedAndUploadPdf() {
    if (!currentReport) {
      message.warn('请先预览报告！');
      return;
    }

    try {
      btnLoading.value = true;
      if (preloadPdfData) {
        uploadFile(preloadPdfData, 'pdf')
          .then((res) => {
            verifyAndSaveReport({ customerRegId: customerReg.value.id, pdfUrl: res.message });
            message.success('操作成功！');
            emit('success');
          })
          .finally(() => {
            btnLoading.value = false;
          });
      } else {
        btnLoading.value = true;
        exportPdf(currentReport).then((pdfData) => {
          uploadFile(pdfData, 'pdf')
            .then((res) => {
              verifyAndSaveReport({ customerRegId: customerReg.value.id, pdfUrl: res.message });
              message.success('操作成功！');
              emit('success');
            })
            .finally(() => {
              btnLoading.value = false;
            });
        });
      }
    } catch (error) {
      console.error('报告上传失败:', error);
      message.error('报告上传失败');
    }
  }

  function fetchReportTemplateList() {
    listByType({ type: '报告', regType: '个人' }).then((res) => {
      if (res.success) {
        originReportTemplateList.value = res.result;
        reportTemplateList.value = res.result.map((item) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
      }
    });
  }

  function ensureTemplatesLoaded(): Promise<void> {
    if (originReportTemplateList.value && originReportTemplateList.value.length > 0) {
      return Promise.resolve();
    }
    return new Promise((resolve) => {
      listByType({ type: '报告', regType: '个人' }).then((res) => {
        if (res.success) {
          originReportTemplateList.value = res.result;
          reportTemplateList.value = res.result.map((item) => ({ value: item.id, label: item.name }));
        }
        resolve();
      });
    });
  }

  function fetchGeneratePdfBackendsFlag() {
    querySysParamByCode({ code: 'generatePdfBackends' }).then((res) => {
      generatePdfBackendsFlag.value = res.result;
    });
  }

  onMounted(() => {
    fetchReportTemplateList();
    fetchGeneratePdfBackendsFlag();
  });

  function previewReportByTemplate() {
    previewReport(selectedTemplateId.value);
  }

  defineExpose({
    open,
  });
</script>

<style lang="less" scoped>
  .viewer-host {
    width: 100%;
    height: calc(100vh - 200px);
  }
  .viewer-el {
    width: 100%;
    height: 100%;
  }
</style>
<style>
  #reportViewer {
    margin: 0 auto;
    width: 100%;
    height: 100vh;
  }
</style>
