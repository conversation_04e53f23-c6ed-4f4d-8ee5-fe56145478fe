// 行内编辑表格样式优化
.editable-table {
  // 编辑状态的行样式
  .ant-table-tbody > tr.editable-row {
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    
    &:hover {
      background-color: #f6ffed !important;
    }
  }
  
  // 编辑中的单元格样式
  .jeecg-editable-cell {
    position: relative;
    
    &__wrapper {
      display: flex;
      align-items: center;
      min-height: 32px;
      
      .ant-input,
      .ant-select,
      .ant-input-number {
        border-radius: 4px;
        transition: all 0.3s;
        
        &:focus,
        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
      }
      
      .ant-select {
        width: 100%;
        
        .ant-select-selector {
          border-radius: 4px;
        }
      }
      
      // 多行文本框样式
      .ant-input {
        &[type="textarea"] {
          min-height: 60px;
          resize: vertical;
        }
      }
    }
    
    &__action {
      display: flex;
      align-items: center;
      margin-left: 8px;
      
      .jeecg-editable-cell__icon {
        cursor: pointer;
        padding: 2px;
        border-radius: 2px;
        transition: all 0.3s;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        &.mx-2 {
          margin: 0 4px;
        }
      }
    }
  }
  
  // 保存状态指示器
  .save-status {
    display: inline-flex;
    align-items: center;
    margin-left: 8px;
    font-size: 12px;
    
    &.saving {
      color: #1890ff;
      
      .anticon {
        animation: spin 1s linear infinite;
      }
    }
    
    &.success {
      color: #52c41a;
    }
    
    &.error {
      color: #ff4d4f;
    }
  }
  
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
  
  // 工具栏样式
  .editable-table-toolbar {
    margin-bottom: 16px;
    padding: 12px 16px;
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    
    .ant-space {
      width: 100%;
      justify-content: space-between;
    }
    
    .toolbar-status {
      margin-top: 12px;
      
      .ant-alert {
        border-radius: 4px;
      }
    }
    
    .keyboard-shortcuts {
      margin-top: 8px;
      text-align: center;
      
      .shortcuts-text {
        font-size: 12px;
        color: #8c8c8c;
      }
    }
  }
  
  // 操作按钮样式
  .editable-row-actions {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .ant-btn-link {
      padding: 0 4px;
      height: auto;
      line-height: 1.5;
      font-size: 12px;
      
      &:not(:last-child) {
        margin-right: 4px;
      }
      
      .anticon {
        font-size: 12px;
        margin-right: 2px;
      }
    }
    
    .edit-btn {
      color: #1890ff;
      
      &:hover {
        color: #40a9ff;
        background-color: #e6f7ff;
      }
    }
    
    .save-btn {
      color: #52c41a;
      
      &:hover {
        color: #73d13d;
        background-color: #f6ffed;
      }
    }
    
    .cancel-btn {
      color: #8c8c8c;
      
      &:hover {
        color: #bfbfbf;
        background-color: #f5f5f5;
      }
    }
    
    .delete-btn {
      color: #ff4d4f;
      
      &:hover {
        color: #ff7875;
        background-color: #fff2f0;
      }
      
      &:disabled {
        color: #d9d9d9;
        background-color: transparent;
      }
    }
  }
  
  // 危害因素按钮样式
  .risk-factor-btn {
    width: 100%;
    margin-bottom: 8px;
    height: 36px;
    border-radius: 4px;
    transition: all 0.3s;
    
    &.added {
      cursor: not-allowed;
      opacity: 0.6;
      
      &:hover {
        opacity: 0.6;
      }
    }
    
    .anticon {
      margin-right: 4px;
    }
  }
  
  // 表格行悬停效果
  .ant-table-tbody > tr:hover > td {
    background-color: #fafafa;
  }
  
  .ant-table-tbody > tr.editable-row:hover > td {
    background-color: #f6ffed !important;
  }
  
  // 响应式设计
  @media (max-width: 768px) {
    .editable-table-toolbar {
      padding: 8px 12px;
      
      .ant-space {
        flex-direction: column;
        align-items: stretch;
      }
      
      .keyboard-shortcuts {
        display: none;
      }
    }
    
    .editable-row-actions {
      flex-direction: column;
      gap: 2px;
      
      .ant-btn-link {
        font-size: 11px;
      }
    }
    
    .risk-factor-btn {
      height: 32px;
      font-size: 12px;
    }
  }
  
  // 加载状态
  .ant-spin-nested-loading {
    .ant-spin {
      max-height: none;
    }
  }
  
  // 表格紧凑模式优化
  &.ant-table-small {
    .jeecg-editable-cell__wrapper {
      min-height: 28px;
    }
    
    .ant-input,
    .ant-select-selector {
      height: 28px;
      line-height: 28px;
    }
    
    .ant-input[type="textarea"] {
      min-height: 50px;
    }
  }
}
