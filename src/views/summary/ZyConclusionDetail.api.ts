import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '/@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/occu/zyConclusionDetail/list',
  save = '/occu/zyConclusionDetail/add',
  edit = '/occu/zyConclusionDetail/edit',
  deleteOne = '/occu/zyConclusionDetail/delete',
  deleteBatch = '/occu/zyConclusionDetail/deleteBatch',
  importExcel = '/occu/zyConclusionDetail/importExcel',
  exportXls = '/occu/zyConclusionDetail/exportXls',
  // 新增行内编辑相关接口
  updateField = '/occu/zyConclusionDetail/updateField',
  batchUpdate = '/occu/zyConclusionDetail/batchUpdate',
  validateField = '/occu/zyConclusionDetail/validateField',
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({ url: Api.deleteBatch, data: params }, { joinParamsToUrl: true }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
};

/**
 * 单字段更新接口
 * @param params { id: string, field: string, value: any }
 */
export const updateField = (params) => {
  return defHttp.patch({ url: Api.updateField, params }, { isTransformResponse: false });
};

/**
 * 批量更新接口
 * @param params { records: Array<{id: string, [field]: any}> }
 */
export const batchUpdate = (params) => {
  return defHttp.put({ url: Api.batchUpdate, params }, { isTransformResponse: false });
};

/**
 * 字段验证接口
 * @param params { id: string, field: string, value: any }
 */
export const validateField = (params) => {
  return defHttp.post({ url: Api.validateField, params }, { isTransformResponse: false });
};

/**
 * 快速保存单个字段
 * @param id 记录ID
 * @param field 字段名
 * @param value 字段值
 */
export const quickSaveField = async (id: string, field: string, value: any) => {
  try {
    const result = await updateField({ id, field, value });
    return result;
  } catch (error) {
    console.error('快速保存字段失败:', error);
    throw error;
  }
};

/**
 * 批量快速保存
 * @param records 要更新的记录数组
 */
export const quickBatchSave = async (records: Array<{id: string, [key: string]: any}>) => {
  try {
    const result = await batchUpdate({ records });
    return result;
  } catch (error) {
    console.error('批量保存失败:', error);
    throw error;
  }
};
