<template>
  <div style="padding: 0">
    <splitpanes class="default-theme">
      <pane size="30" style="padding: 2px">
        <customer-reg-list-of-pannel
          ref="customerRegLiteList"
          @row-click="handleRegTableRowClick"
          @add="handelAddCustomerReg"
          @batch-reg-ok="handleBatchRegOk"
          @batch-print="handleBatchPrint"
        />
      </pane>
      <pane size="30" style="padding: 2px">
        <a-card size="small" title="登记详情" style="padding: 0; margin-bottom: 8px" :class="{ 'active-border': addFlag }">
          <template #extra>
            <a-spin :spinning="actionLoading">
              <a-space>
                <!--                <a-button size="middle" type="primary" @click="saveCustomerReg"><SaveOutlined />保存</a-button>-->
                <a-button size="middle" type="primary" style="margin-left: 20px" @click="reg">
                  <IdcardOutlined />
                  登记
                </a-button>
                <!--                <a-button size="middle" type="primary" @click="openRetrieveGuideSheetModal"><FileDoneOutlined />交表</a-button>-->
                <a-button size="middle" type="primary" @click="openFeeModal" v-if="autoCharge == '0'">
                  <PayCircleOutlined />
                  收费
                </a-button>
                <a-button size="middle" type="primary" @click="openHealthQuest">
                  <SolutionOutlined />
                  健康问卷
                </a-button>
                <a-dropdown>
                  <template #overlay>
                    <a-menu @click="handleMenuClick">
                      <a-menu-item key="1"> 预览导引单</a-menu-item>
                      <a-menu-item key="2"> 补打导引单</a-menu-item>
                      <a-menu-item key="5"> 补打申请单</a-menu-item>
                      <a-menu-item key="3"> 条码列表</a-menu-item>
                      <a-menu-item key="8"> 发送心理测评短信</a-menu-item>
                      <div style="padding: 0 12px">-------------</div>
                      <a-menu-item key="0"> 职业病问卷</a-menu-item>
                      <a-menu-item key="6"> 取消登记</a-menu-item>
                      <a-menu-item key="4"> 打印设置</a-menu-item>
                      <a-menu-item key="7" v-if="hasPermission('reg:retrieve')"> 交表</a-menu-item>
                    </a-menu>
                  </template>

                  <a-button>
                    <a-spin :spinning="printLoading">
                      更多
                      <DownOutlined />
                    </a-spin>
                  </a-button>
                </a-dropdown>
              </a-space>
            </a-spin>
          </template>
          <div style="height: 80vh; overflow-y: auto">
            <customer-reg-form-of-pannel
              ref="customerRegForm"
              :form-data="currentReg"
              @ok="setSelectedRow"
              @reg-ok="handleRegOk"
              @change-item-ok="handleChangeItemOk"
              @change-item-cancel="handleChangeItemCancel"
              :formDisabled="false"
            />
          </div>
        </a-card>
      </pane>

      <pane size="40" style="padding: 2px">
        <group-list-of-pannel
          ref="regGroupPannel"
          @add-item-done="handleAddItemDone"
          @refresh="handleRegRefresh"
          :customer-reg="currentReg"
          :show-price="autoCharge == '0'"
        />
      </pane>
    </splitpanes>

    <Report ref="reportRef" />
    <ComprehensiveInquiryQuestionnaire ref="inquiryModal" :customer-reg="currentReg" />
    <PrintModal ref="printModalRef" />
    <PrinterSetupModal ref="printerSetupModalRef" />
    <RetrieveGuideSheetModal ref="retrieveGuideSheetModal" @success="handleOk" />
    <PersonFeePannelModal ref="feeModal" :customer-reg="currentReg" @cancel="handleOk" />
    <HealthQuestAddModal ref="healthQuestAddModal" @ok="handleQuestOk" />
    <a-modal v-model:open="isPrinting" title="打印进度" @cancel="closeProgressModal" :footer="null" :closable="false" :maskClosable="false">
      <div style="padding: 20px; justify-content: center; align-items: center; min-height: 100px">
        <a-typography-text type="secondary">{{ currentPrintTip }}</a-typography-text>
        <a-progress :percent="printProgress" v-if="printProgress < 100" />
        <a-result status="success" title="打印完成!" v-else />
      </div>
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  import { defineAsyncComponent, nextTick, onMounted, provide, reactive, ref, unref } from 'vue';
  import CustomerRegListOfPannel from '@/views/reg/CustomerRegListOfPannel.vue';
  import CustomerRegFormOfPannel from '@/views/reg/components/CustomerRegFormOfPannel.vue';
  import GroupListOfPannel from '@/views/reg/GroupListOfPannel.vue';
  import {
    checkIsSummary,
    getGuidanceSheet,
    getItemGroupsByRegIdAndTemplateId,
    sendPsyNotify,
    unReg,
    updateApplyPrintTimes,
    updateGuidancePrintTimes,
    updateHealthQuestId,
  } from '@/views/reg/CustomerReg.api';
  import { DownOutlined, IdcardOutlined, PayCircleOutlined, SolutionOutlined } from '@ant-design/icons-vue';
  import { selectedCustomerRegKey } from '@/providekey/provideKeys';
  import { message } from 'ant-design-vue';
  import { PrinterType, printReportDirect } from '@/utils/print';
  //import ZyInquiryModal from '@/views/occu/components/ZyInquiryModal.vue';
  import { listCustomerRegBarcode, updatePrintInfo } from '@/views/reg/CustomerRegBarcode.api';
  import { getBarcodeTemplateById, getBarcodeTemplateIdByCategory } from '@/views/basicinfo/BarcodeTemplate.api';
  //import PrintModal from '@/views/reg/PrintModal.vue';
  import { ICustomerReg, ICustomerRegBarcode } from '#/types';
  //import PrinterSetupModal from '@/views/reg/PrinterSetupModal.vue';
  import { getApplyTemplateIdsByReg, getDefaultIdOfType, getTemplateById } from '@/views/basicinfo/Template.api';
  //import RetrieveGuideSheetModal from '@/views/reg/components/RetrieveGuideSheetModal.vue';
  //import PersonFeePannelModal from '@/views/fee/PersonFeePannelModal.vue';
  //import ReportVue from '@/components/Report/ReportVue.vue';
  // import HealthQuestAddModal from '@/views/quest/components/HealthQuestAddModal.vue';
  import { querySysParamByCode } from '@/views/basicinfo/SysSetting.api';
  import { usePermission } from '@/hooks/web/usePermission';
  import { Pane, Splitpanes } from 'splitpanes';
  import 'splitpanes/dist/splitpanes.css';
  import { useMessage } from '@/hooks/web/useMessage';
  import { getFileAccessHttpUrl } from '@/utils/common/compUtils';

  // 直接导入组件避免异步加载问题
  const PrintModal = defineAsyncComponent(() => import('@/views/reg/PrintModal.vue'));
  const PrinterSetupModal = defineAsyncComponent(() => import('@/views/reg/PrinterSetupModal.vue'));
  const RetrieveGuideSheetModal = defineAsyncComponent(() => import('@/views/reg/components/RetrieveGuideSheetModal.vue'));
  const PersonFeePannelModal = defineAsyncComponent(() => import('@/views/fee/PersonFeePannelModal.vue'));
  const Report = defineAsyncComponent(() => import('@/components/Report/Report.vue'));
  const HealthQuestAddModal = defineAsyncComponent(() => import('@/views/quest/components/HealthQuestAddModal.vue'));
  // 改为同步导入，避免异步组件初始化问题
  const ComprehensiveInquiryQuestionnaire = defineAsyncComponent(() => import('@/views/occu/components/ComprehensiveInquiryQuestionnaire.vue'));
  //import ComprehensiveInquiryQuestionnaire from '@/views/occu/components/ComprehensiveInquiryQuestionnaire.vue';

  const { hasPermission } = usePermission();
  const actionLoading = ref(false);
  const addFlag = ref(false);
  const customerRegForm = ref();
  const customerRegLiteList = ref();
  const regGroupPannel = ref();
  const currentReg = ref({});
  const healthQuestAddModal = ref(null);
  const autoCharge = ref('0');
  const inquiryDisabled = ref(false); // 问卷禁用状态
  const { createConfirm } = useMessage();
  const customerReg2Provide = reactive({
    value: currentReg.value,
    setValue: (val) => (customerReg2Provide.value = val),
  });
  provide(selectedCustomerRegKey, customerReg2Provide);

  function handleAutoCharge() {
    querySysParamByCode({ code: 'autoCharge' }).then((res) => {
      autoCharge.value = res.result;
    });
  }

  function handleMenuClick(menu) {
    //console.log('handleMenuClick', menu);
    if (menu.key == '0') {
      openInquiry();
    } else if (menu.key === '1') {
      previewGuide();
    } else if (menu.key === '2') {
      printCurrentGuide();
    } else if (menu.key === '3') {
      openPrintModal();
    } else if (menu.key === '4') {
      openPrinterSetupModal();
    } else if (menu.key === '5') {
      printApply(currentReg.value, true);
    } else if (menu.key == '6') {
      doUnReg();
    } else if (menu.key == '7') {
      openRetrieveGuideSheetModal();
    } else if (menu.key == '8') {
      handleSendPsyNotify();
    }
  }

  const regSubmitLoading = ref(false);

  function saveCustomerReg() {
    regSubmitLoading.value = true;
    //actionLoading.value = true;
    customerRegForm.value?.submitForm();
  }

  function setSelectedRow(row) {
    addFlag.value = false;
    //actionLoading.value = false;
    //currentReg.value = row;
    //customerReg2Provide.setValue(row);
    customerRegLiteList.value?.reloadAndSelect(row);
    regSubmitLoading.value = false;
  }

  async function handleOk() {
    customerRegLiteList.value?.reloadAndSelect(currentReg.value);
  }

  async function handleQuestOk(personalQuestId) {
    await updateHealthQuestId({ id: currentReg.value.id, personalQuestId: personalQuestId });
    customerRegLiteList.value?.reloadAndSelect(currentReg.value);
  }

  function handleRegRefresh() {
    customerRegLiteList.value?.reloadAndSelect(currentReg.value);
  }

  async function handleRegOk() {
    customerRegLiteList.value?.reloadAndSelect(currentReg.value);
    //actionLoading.value = false;
    //currentReg.value.status = '已登记';
    //customerReg2Provide.setValue(currentReg);
    let printGuideFlag = currentReg.value?.companyReg?.printGuidance ?? '1';
    if (printGuideFlag == '1' && isAutoPrint('guide')) {
      await printGuide(currentReg.value);
    }
    let printApplyFlag = currentReg.value?.companyReg?.printApply ?? '1';
    if (printApplyFlag == '1' && isAutoPrint('apply')) {
      await printApply(currentReg.value, false);
    }
    if (isAutoPrint('barcode')) {
      await printBarcodeOfReg(currentReg.value, true);
    }
  }

  async function handleAddItemDone() {
    await printApply(currentReg.value, false);
  }
  function handleChangeItemCancel() {
    regGroupPannel.value.open();
  }

  async function handleBatchRegOk(regList: ICustomerReg[] = []) {
    customerRegLiteList.value?.reloadPage();
    //整理出来需要打印导引单、申请单、条码的登记记录，然后调用handleBatchPrint
    let guideRegList = regList.filter((reg) => reg?.companyReg?.printGuidance == '1' && isAutoPrint('guide'));
    let applyRegList = regList.filter((reg) => reg?.companyReg?.printApply == '1' && isAutoPrint('apply'));
    let barcodeRegList = regList.filter(() => isAutoPrint('barcode'));

    if (guideRegList.length > 0) {
      await printGuideBatch(guideRegList);
    }
    if (applyRegList.length > 0) {
      await printApplyBatch(applyRegList);
    }
    if (barcodeRegList.length > 0) {
      await printBarcodeBatch(barcodeRegList, false);
    }
  }

  /**打印*/
  const isPrinting = ref(false);
  const printProgress = ref(0);
  const currentPrintTip = ref('');

  function showProgressModal() {
    isPrinting.value = true;
    printProgress.value = 0; // Reset progress
  }

  function closeProgressModal() {
    isPrinting.value = false; // Close the modal
  }

  interface IPrintBatchParams {
    regList: ICustomerReg[];
    printTasks: string[];
  }

  async function printGuideBatch(regList: ICustomerReg[], reverse: boolean = false): Promise<void> {
    showProgressModal();
    const listToPrint = reverse ? [...regList].reverse() : regList; // Reverse the list if reverse is true
    for (let index = 0; index < listToPrint.length; index++) {
      const reg = listToPrint[index];
      currentPrintTip.value = `正在打印第 ${index + 1}/${listToPrint.length} 导引单:${reg.name}-${reg.examNo}`;
      await printGuide(reg);
      printProgress.value = Math.round(((index + 1) / listToPrint.length) * 100);
    }

    closeProgressModal();
  }

  async function printApplyBatch(regList: ICustomerReg[], reverse: boolean = false): Promise<void> {
    showProgressModal();
    const listToPrint = reverse ? [...regList].reverse() : regList; // Reverse the list if reverse is true
    for (let index = 0; index < listToPrint.length; index++) {
      const reg = listToPrint[index];
      currentPrintTip.value = `正在打印第 ${index + 1}/${listToPrint.length} 申请单:${reg.name}-${reg.examNo}`;
      await printApply(reg, false);
      printProgress.value = Math.round(((index + 1) / listToPrint.length) * 100);
    }

    closeProgressModal();
  }

  async function printBarcodeBatch(regList: ICustomerReg[], reverse: boolean = false): Promise<void> {
    showProgressModal();
    const listToPrint = reverse ? [...regList].reverse() : regList; // Reverse the list if reverse is true
    for (let index = 0; index < listToPrint.length; index++) {
      const reg = listToPrint[index];
      currentPrintTip.value = `正在打印第 ${index + 1}/${listToPrint.length} 条码:${reg.name}-${reg.examNo}`;
      await printBarcodeOfReg(reg, false);
      printProgress.value = Math.round(((index + 1) / listToPrint.length) * 100);
    }

    closeProgressModal();
  }

  async function printExamNoBarcodeBatch(regList: ICustomerReg[], reverse: boolean = false): Promise<void> {
    showProgressModal();
    const listToPrint = reverse ? [...regList].reverse() : regList; // Reverse the list if reverse is true
    for (let index = 0; index < listToPrint.length; index++) {
      const reg = listToPrint[index];
      currentPrintTip.value = `正在打印第 ${index + 1}/${listToPrint.length} 体检号:${reg.name}-${reg.examNo}`;
      await printExamNoBarcode(reg);
      printProgress.value = Math.round(((index + 1) / listToPrint.length) * 100);
    }

    closeProgressModal();
  }

  async function handleBatchPrint({ regList = [], printTasks = [] }: IPrintBatchParams): Promise<void> {
    showProgressModal();
    if (printTasks.includes('导引单')) {
      await printGuideBatch(regList, true);
    }
    if (printTasks.includes('申请单')) {
      await printApplyBatch(regList, true);
    }
    if (printTasks.includes('条码')) {
      await printBarcodeBatch(regList, false);
    }
    if (printTasks.includes('体检号')) {
      await printExamNoBarcodeBatch(regList, true);
    }
  }

  function isAutoPrint(type) {
    let printersStr = localStorage.getItem('printers');
    if (printersStr) {
      const storedPrinters = JSON.parse(printersStr);
      return storedPrinters[type].autoPrint ?? '1' == '1';
    }

    return true;
  }

  /* 收费*/
  const feeModal = ref(null);

  function openFeeModal() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }
    if (currentReg.value.status != '已登记') {
      message.error('未登记不可收费!');
      return;
    }
    feeModal.value?.open(currentReg.value);
  }

  /**交表*/
  const retrieveGuideSheetModal = ref(null);

  function openRetrieveGuideSheetModal() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }
    retrieveGuideSheetModal.value?.open(currentReg.value);
  }

  function handleSendPsyNotify() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }
    if (currentReg.value.psyNotifyFlag === '已发送') {
      createConfirm({
        iconType: 'warning',
        title: '心理测评短信重新发送确认',
        content: '已发送心理测评短信，确认要重新发送？',
        onOk: () => {
          try {
            sendPsyNotify({ customerRegId: currentReg.value.id }).then((res) => {
              if (res.success) {
                message.info(res.message);
              } else {
                message.error(res.message);
              }
              handleRegRefresh();
            });
          } catch (e) {
            message.error('心理测评短信发送失败！', e);
          }
        },
      });
    } else {
      try {
        sendPsyNotify({ customerRegId: currentReg.value.id }).then((res) => {
          if (res.success) {
            message.info(res.message);
          } else {
            message.error(res.message);
          }
          handleRegRefresh();
        });
      } catch (e) {
        message.error('心理测评短信发送失败！', e);
      }
    }
  }
  /**打印*/
  const printLoading = ref(false);
  const printModalRef = ref(null);
  const printerSetupModalRef = ref(null);

  function openPrintModal() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }
    printModalRef.value?.open(currentReg.value);
  }

  function openPrinterSetupModal() {
    printerSetupModalRef.value?.open();
  }

  async function printBarcodeOfReg(reg, isConsiderLocation = true) {
    if (!reg) {
      return false;
    }
    const regUnref = unref(reg);

    try {
      printLoading.value = true;
      const barcodeList = (await listCustomerRegBarcode({ customerRegId: regUnref.id })) || [];
      const validBarcodeList = barcodeList.filter((barcode) => barcode.bloodStatus == 0);

      if (validBarcodeList.length === 0) {
        message.info('没有可打印的条码');
        return false;
      }

      let printSuccessCount = 0;
      for (const barcodeInfo of validBarcodeList) {
        const barcodeSetting = barcodeInfo.barcodeSetting || {};
        const shouldPrint = !isConsiderLocation || barcodeSetting.printLocation == '前台';

        if (shouldPrint) {
          const printResult = await printBarcode(barcodeInfo, regUnref);
          if (printResult) {
            printSuccessCount++;
          }
        }
      }

      // 返回打印结果
      return printSuccessCount > 0;
    } catch (e) {
      console.error('打印条码异常:', e);
      message.error('打印条码异常：' + (e.message || '未知错误'));
      return false;
    } finally {
      printLoading.value = false;
    }
  }

  async function printBarcode(barcodeInfo: ICustomerRegBarcode, reg: ICustomerReg, method: string = 'print') {
    try {
      const templateRes = await getBarcodeTemplateById({ id: barcodeInfo.barTemplateId });
      if (!templateRes || !templateRes.code) {
        message.error('获取条码模板失败');
        return false;
      }

      // 准备条码数据，用于模板中使用
      const customerRegBarcode = {
        name: reg.name,
        companyName: reg.companyName,
        gender: reg.gender,
        age: reg.age + reg.ageUnit,
        idCard: reg.idCard,
        examNo: reg.examNo,
        barNo: barcodeInfo.barNo,
        barText: barcodeInfo.barText,
        barcodeSetting: barcodeInfo.barcodeSetting,
        reg: reg,
      };

      const printCode = templateRes.code;
      try {
        // 获取LODOP对象
        const LODOP = getLodop();
        if (!LODOP) {
          message.error('打印控件未加载成功');
          return false;
        }

        let finalCode = eval('`' + printCode + '`');
        eval(finalCode);

        // 设置打印机
        const barcodePrinter = getPrinterName(PrinterType.Barcode);
        if (barcodePrinter) {
          LODOP.SET_PRINTER_INDEX(barcodePrinter);
        }

        // 根据方法执行打印或打印设置
        if (method === 'print') {
          const printCount = barcodeInfo.barcodeSetting?.barPage || 1;
          LODOP.SET_PRINT_COPIES(printCount);
          const printResult = LODOP.PRINT();

          if (printResult) {
            // 更新打印次数
            await updatePrintInfo({ id: barcodeInfo.id });
            return true;
          } else {
            message.error('打印失败');
            return false;
          }
        } else {
          LODOP.PRINT_SETUP();
          return true;
        }
      } catch (e) {
        console.error('打印异常:', e);
        message.error('打印异常：' + (e.message || '未知错误'));
        return false;
      }
    } catch (e) {
      console.error('获取条码模板异常:', e);
      message.error('获取条码模板异常：' + (e.message || '未知错误'));
      return false;
    }
  }

  function getPrinterName(type) {
    //从localStorage中获取打印机名称
    let printersStr = localStorage.getItem('printers');
    if (printersStr) {
      let printers = JSON.parse(printersStr);
      return printers[type].name;
    }
    return '';
  }

  async function getGuideTemplate() {
    try {
      const res = await getDefaultIdOfType({ type: '导引单' });
      if (!res.success || !res.result) {
        console.error('获取导引单模板ID失败');
        return null;
      }

      const templateId = res.result;
      const templateRes = await getTemplateById({ id: templateId });
      if (!templateRes || !templateRes.content) {
        console.error('获取导引单模板内容失败');
        return null;
      }

      return JSON.parse(templateRes.content);
    } catch (e) {
      console.error('获取导引单模板异常:', e);
      message.error('获取导引单模板异常：' + (e.message || '未知错误'));
      return null;
    }
  }

  /**登记记录列表*/
  function handleRegTableRowClick(selectedRow) {
    //console.log('--------------------------------------------', selectedRows);
    addFlag.value = false;
    currentReg.value = {};
    currentReg.value = selectedRow;
    customerReg2Provide.setValue({});
    customerReg2Provide.setValue(selectedRow);
    customerRegForm.value?.edit(selectedRow);
  }

  function reg() {
    //actionLoading.value = true;
    customerRegForm.value?.reg();
  }

  /**职业病问诊*/
  const inquiryModal = ref(null);

  async function openInquiry() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }

    // 确保DOM更新完成
    await nextTick();
    inquiryModal.value.open(currentReg.value, false);
  }

  function openHealthQuest() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }
    healthQuestAddModal.value?.open(currentReg.value);
  }

  /**打印导引单*/
  const reportRef = ref(null);

  async function printCurrentGuide() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录!');
      return;
    }
    printGuide(currentReg.value);
    //printGuide(currentReg.value);
  }

  async function printGuide(reg) {
    let regUnref = unref(reg);
    if (!regUnref) {
      return;
    }
    try {
      printLoading.value = true;
      const customerRegDetail = await getGuidanceSheet({ id: regUnref.id });
      if (customerRegDetail.reg.avatar) {
        customerRegDetail.reg.avatar = getFileAccessHttpUrl(customerRegDetail.reg.avatar);
      }
      let template = await getGuideTemplate();
      if (!template) {
        message.error('未找到导引单模板');
        return;
      }
      template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(customerRegDetail);
      await printReportDirect(template, PrinterType.Guide);
      await updateGuidancePrintTimes({ regId: regUnref.id });
    } catch (e) {
      console.log(e);
    } finally {
      printLoading.value = false;
    }
  }

  async function previewGuide() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录');
      return;
    }
    try {
      printLoading.value = true;
      const customerRegDetail = await getGuidanceSheet({ id: currentReg.value.id });
      if (customerRegDetail.reg.avatar) {
        customerRegDetail.reg.avatar = getFileAccessHttpUrl(customerRegDetail.reg.avatar);
      }
      let template = await getGuideTemplate();
      if (!template) {
        message.error('未找到导引单模板');
        return;
      }
      template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(customerRegDetail);
      reportRef.value.open({
        filename: `${customerRegDetail.reg.name}的指引单`,
        template: template,
      });
    } catch (e) {
      console.log(e);
    } finally {
      printLoading.value = false;
    }
  }

  async function doUnReg() {
    if (!currentReg.value.id) {
      message.error('请选择登记记录');
      return;
    }
    unReg({ id: currentReg.value.id }).then(() => {
      customerRegLiteList.value?.reloadAndSelect(currentReg.value);
    });
  }

  async function printApply(reg, reprintFlag) {
    if (!reg.id) {
      message.error('请选择登记记录');
      return;
    }
    try {
      printLoading.value = true;

      let applyTemplateIdsRes = await getApplyTemplateIdsByReg({
        customerRegId: reg.id,
        reprintFlag: reprintFlag,
      });
      if (applyTemplateIdsRes.success) {
        let applyTemplateIds = applyTemplateIdsRes.result;
        if (applyTemplateIds.length == 0) {
          //message.info('没有需要打印的申请单！');
          return;
        }

        for (let templateId of applyTemplateIds) {
          let templateRes = await getTemplateById({ id: templateId });
          let template = JSON.parse(templateRes.content);
          let itemGroupRes = await getItemGroupsByRegIdAndTemplateId({
            customerRegId: reg.id,
            templateId: templateId,
            reprintFlag: reprintFlag,
          });
          reg.itemGroups = itemGroupRes.result || [];
          if (reg.itemGroups.length == 0) {
            message.error('没有需要打印的申请单！');
            return;
          }
          reg.itemGroupNames = itemGroupRes.result.map((itemGroup) => itemGroup.itemGroupName).join('、');
          reg.itemGroupNameWithQuantity = itemGroupRes.result
            .map((itemGroup, index) => `${index + 1}、${itemGroup.groupNameWithQuantity}`)
            .join('\n');
          let regGroupIds = itemGroupRes.result.map((itemGroup) => itemGroup.id);
          //查看是否总检
          let checkIsSummaryRes = await checkIsSummary({
            customerRegId: reg.id,
          });
          reg.isSummary = checkIsSummaryRes.result || false;

          // 尽可能将图片类字段转换为可访问路径
          transformImageUrls(reg);
          template.DataSources[0].ConnectionProperties.ConnectString = 'jsondata=' + JSON.stringify(reg);
          await printReportDirect(template, PrinterType.Apply);
          await updateApplyPrintTimes({ regGroupIds: regGroupIds });
        }
      }
    } catch (e) {
      console.log(e);
      message.error('打印申请单失败！', e);
    } finally {
      printLoading.value = false;
    }
  }

  function handelAddCustomerReg() {
    currentReg.value = {};
    customerReg2Provide.setValue({});
    customerRegForm.value?.add({});
    addFlag.value = true;
    customerRegLiteList.value.reset();
  }

  async function printExamNoBarcode(reg) {
    let regUnref = unref(reg);
    if (!regUnref) {
      return;
    }
    try {
      const res = await getBarcodeTemplateIdByCategory({ category: '体检号' });
      if (!res.success) {
        message.error(res.message);
        return;
      }
      let barcodeInfo = {
        barNo: reg.examNo,
        barText: reg.examNo,
        barTemplateId: res.result,
      };
      await printBarcode(barcodeInfo, regUnref);
    } catch (e) {
      console.log(e);
      message.error('获取模板失败');
    }
  }

  onMounted(() => {
    handleAutoCharge();
  });

  function transformImageUrls(obj: any) {
    if (!obj) return;
    const stack = [obj];
    while (stack.length) {
      const cur = stack.pop();
      if (cur && typeof cur === 'object') {
        for (const key of Object.keys(cur)) {
          const val = cur[key];
          if (val && typeof val === 'object') {
            stack.push(val);
          } else if (typeof val === 'string') {
            if (/\.(png|jpg|jpeg|gif|bmp|webp)$/i.test(val)) {
              cur[key] = getFileAccessHttpUrl(val);
            }
          }
        }
      }
    }
  }
</script>
<style scoped>
  /* .active-border {
  border: #0a8fe9 1px solid;
}*/
  .active-border {
    animation: glow 800ms ease-out infinite alternate;
  }

  @keyframes glow {
    0% {
      border-color: #0a8fe9;
      box-shadow: 0 0 5px rgba(10, 143, 233, 0.2);
    }
    100% {
      border-color: #0a8fe9;
      box-shadow: 0 0 20px rgba(10, 143, 233, 0.6);
    }
  }
</style>
