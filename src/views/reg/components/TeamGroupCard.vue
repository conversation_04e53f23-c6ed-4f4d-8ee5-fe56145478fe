<template>
  <div>
    <a-card size="small" title="组内体检项目组合">
      <template #extra>
        <div>
          <span>总价：</span><span style="color: #f5222d; font-weight: bold; font-size: 16px">{{ totalItemPrice.toFixed(2) }}</span>
          <span style="margin-left: 20px">折后总价：</span>
          <span style="color: #f5222d; font-weight: bold; font-size: 16px">{{ totalPriceAfterDis.toFixed(2) }}</span>
        </div>
      </template>
      <a-alert
        v-if="!mainTeamId || !mainTeamId.value"
        message="请在左侧选择单位分组"
        type="error"
        :show-icon="true"
        :closable="false"
        style="margin-bottom: 10px"
      />

      <a-space v-if="mainTeamId.value">
        <a-button type="primary" size="middle" @click="openSuitModal">使用套餐</a-button>
        <a-button type="primary" size="middle" @click="openGroupModal">添加项目</a-button>
        <a-button type="primary" danger size="middle" @click="batchDelete" v-if="tableState.selectedRowKeys.length > 0">批量删除</a-button>
      </a-space>
      <a-alert
        :message="mustCheckItemTip"
        :type="mustCheckItemGroupFiltered.length > 0 ? 'warning' : 'success'"
        v-if="mainTeam?.value?.risks"
        style="margin-top: 5px"
      >
        <template #action>
          <a-space
            ><a-button size="small" type="text" @click="addMustCheckItem">添加</a-button>
            <a-button size="small" type="text" @click="openMustCheckItemModal">查看</a-button></a-space
          >
        </template>
      </a-alert>

      <!-- 依赖项目提示区域 -->
      <div v-if="missingDependencies.length > 0" class="missing-dependencies-alert" style="margin-bottom: 16px">
        <a-alert type="warning" show-icon :closable="false">
          <template #message>
            <div class="missing-dependencies-content">
              <span class="alert-title">检测到缺失的依赖项目</span>
              <div class="missing-projects-list">
                <a-tag
                  v-for="dependency in missingDependencies"
                  :key="dependency.dependentId"
                  color="orange"
                  style="margin: 2px 4px 2px 0"
                  :title="`依赖此项目的检查项目: ${dependency.relatedItemsText}`"
                >
                  {{ dependency.dependentName }}
                  <template v-if="dependency.dependentItemDetails"> ({{ dependency.dependentItemDetails }}) </template>
                </a-tag>
              </div>
            </div>
          </template>
          <template #action>
            <a-button type="primary" size="small" @click="handleQuickAddAllDependencies" :loading="addingDependencies"> 一键添加 </a-button>
          </template>
        </a-alert>
      </div>

      <a-form layout="inline" :model="batchFormState" style="margin-bottom: 10px" v-if="mainTeamId.value">
        <a-form-item labelAlign="left" tooltip="折扣率与折后总价只能设置一个，同时设置时以折扣率为准">
          <a-input-number v-model:value="batchFormState.disRate" size="small" placeholder="折扣率" :disabled="!hasPermission('teamreg:discount')" />
        </a-form-item>
        <a-form-item labelAlign="left">
          <a-input-number
            v-model:value="batchFormState.priceAfterDis"
            size="small"
            placeholder="折后总价"
            :disabled="!hasPermission('teamreg:discount')"
          />
        </a-form-item>
        <!--        <a-form-item labelAlign="left">
          <a-select
            size="small"
            v-model:value="batchFormState.type"
            placeholder="项目类型"
            style="width: 100px"
            :options="[
              { label: '健康项目', value: '健康项目' },
              { label: '职业项目', value: '职业项目' },
            ]"
          />
        </a-form-item>-->
        <a-form-item>
          <a-popconfirm :title="batchTip" ok-text="确定" cancel-text="取消" @confirm="updateByBatch">
            <a-button type="primary" size="small" html-type="submit">整体设置</a-button>
          </a-popconfirm>
        </a-form-item>
      </a-form>
      <a-table
        :loading="loading"
        :bordered="false"
        :scroll="{ y: 450 }"
        :pagination="false"
        :row-key="getRowKey"
        :columns="column"
        :data-source="dataSource"
        :row-selection="rowSelection"
        size="small"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="'itemGroupName' == column.dataIndex">
            <div style="display: flex; align-items: center; gap: 4px">
              <a-tag color="red" v-if="record.mustCheckFlag == '1'">必检</a-tag>
              <!-- 项目类型标签：赠送、依赖、附属 -->
              <a-tag
                v-if="record.sourceType && record.sourceType !== 'main'"
                :color="getSourceTypeColor(record.sourceType)"
                style="cursor: pointer"
                size="small"
                @click="showRelationDetail(record)"
              >
                {{ getSourceTypeText(record.sourceType) }}
              </a-tag>
              <span>{{ getDisplayItemName(record) }}</span>
            </div>
          </template>
          <template v-else-if="'itemGroupCategory' == column.dataIndex">
            <a-select
              @change="updateType(record)"
              size="small"
              v-model:value="record.itemGroupCategory"
              style="width: 90px"
              :options="[
                { label: '健康项目', value: '健康项目' },
                { label: '职业项目', value: '职业项目' },
              ]"
            />
          </template>
          <template v-else-if="'disRate' == column.dataIndex">
            <input
              v-if="hasPermission('teamreg:discount')"
              type="number"
              min="0"
              @change="handleDisRateChange(record, $event)"
              :value="record.disRate"
              style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 80px"
            />
            <span v-else>
              {{ record.disRate }}
            </span>
          </template>
          <template v-else-if="'priceAfterDis' == column.dataIndex">
            <input
              v-if="hasPermission('teamreg:discount')"
              type="number"
              min="0"
              @change="handlePriceChange(record, $event)"
              :value="record.priceAfterDis"
              style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 80px"
            />
            <span v-else>
              {{ record.priceAfterDis }}
            </span>
          </template>
          <template v-else-if="'minDiscountRate' == column.dataIndex">
            <input
              v-if="hasPermission('suit:minDiscountRate')"
              type="number"
              min="0"
              step="0.1"
              @change="updateMinDiscountRate(record, $event)"
              :value="record.minDiscountRate"
              style="margin: -5px 0; border: 1px solid #d9d9d9; border-radius: 2px; width: 60px"
            />
            <span v-else>
              {{ record.minDiscountRate }}
            </span>
          </template>
          <template v-else-if="column.dataIndex == 'itemGroupCategory'">
            <a-select
              size="small"
              @change="updateType(record)"
              v-model:value="record.itemGroupCategory"
              style="width: 100px"
              :options="[
                { label: '健康项目', value: '健康项目' },
                { label: '职业项目', value: '职业项目' },
              ]"
            />
          </template>
          <template v-else>
            {{ text }}
          </template>
        </template>
      </a-table>
    </a-card>

    <group-item-modal ref="groupItemModal" />
    <group-modal ref="groupModal" @success="handleGroupModalSuccess" />
    <suit-modal ref="suitModal" @success="handleSuitModalSuccess" />
    <must-check-item-group-modal ref="mustCheckItemGroupModal" />

    <!-- 批量部位选择模态框 -->
    <a-modal
      :title="batchPartState.itemGroups.length === 1 ? '选择检查部位' : '批量选择检查部位'"
      v-model:open="batchPartState.visible"
      width="800px"
      @ok="confirmBatchAddItemsWithParts"
      @cancel="closeBatchPartSelector"
      :confirmLoading="batchPartState.loading"
      :okButtonProps="{ disabled: !hasAnyPartSelected() }"
      okText="确认添加"
      cancelText="取消"
    >
      <div style="padding: 10px; height: 60vh; overflow-y: auto">
        <div style="margin-bottom: 16px">
          <a-alert :message="`共 ${batchPartState.itemGroups.length} 个项目需要选择部位`" type="info" show-icon style="margin-bottom: 16px" />
        </div>

        <!-- 项目列表 -->
        <div
          v-for="(itemGroup, index) in batchPartState.itemGroups"
          :key="itemGroup.id"
          style="margin-bottom: 24px; padding: 16px; border: 1px solid #d9d9d9; border-radius: 6px"
        >
          <div style="margin-bottom: 12px">
            <strong style="font-size: 14px">{{ index + 1 }}. {{ itemGroup.name }}</strong>
            <a-tag v-if="itemGroup.departmentName" color="blue" style="margin-left: 8px">
              {{ itemGroup.departmentName }}
            </a-tag>
          </div>

          <a-form-item :label="`选择部位`" required style="margin-bottom: 8px">
            <a-select
              v-model:value="getItemPartSelection(itemGroup.id).selectedParts"
              mode="multiple"
              placeholder="请选择检查部位，支持多选，可输入关键字搜索（支持拼音缩写）"
              :filter-option="false"
              :loading="getItemPartSelection(itemGroup.id).loading"
              :options="getItemPartSelection(itemGroup.id).options"
              @search="(keyword) => searchItemParts(itemGroup.id, keyword)"
              @focus="() => loadItemParts(itemGroup.id)"
              style="width: 100%"
              show-search
              allow-clear
              :not-found-content="getItemPartSelection(itemGroup.id).loading ? '搜索中...' : '暂无数据'"
              :max-tag-count="3"
              :max-tag-text-length="8"
            >
              <template v-if="getItemPartSelection(itemGroup.id).loading" #suffixIcon>
                <a-spin size="small" />
              </template>
            </a-select>
          </a-form-item>

          <!-- 已选择的部位预览 -->
          <div
            v-if="getItemPartSelection(itemGroup.id).selectedParts.length > 0"
            style="margin-top: 8px; padding: 8px; background-color: #f6ffed; border: 1px solid #b7eb8f; border-radius: 4px"
          >
            <div style="font-size: 12px; color: #52c41a; margin-bottom: 4px">已选择部位：</div>
            <a-tag v-for="partId in getItemPartSelection(itemGroup.id).selectedParts" :key="partId" color="green" style="margin-bottom: 4px">
              {{ getBatchPartNameById(itemGroup.id, partId) }}
            </a-tag>
          </div>
        </div>

        <!-- 操作提示 -->
        <div style="margin-top: 16px; padding: 12px; background-color: #f5f5f5; border-radius: 6px">
          <div style="color: #666; font-size: 12px; margin-bottom: 8px">
            <a-icon type="info-circle" style="margin-right: 4px" />
            操作提示
          </div>
          <ul style="margin: 0; padding-left: 16px; color: #666; font-size: 12px">
            <li>每个项目都需要至少选择一个检查部位</li>
            <li>支持多选，可以同时选择多个检查部位</li>
            <li>可以输入关键字快速搜索部位</li>
            <li>括号内的数字表示该部位的使用频次</li>
          </ul>
        </div>
      </div>
    </a-modal>

    <!-- 依赖项目快捷添加模态框 -->
    <!-- <DependencyQuickAddModal
      ref="dependencyQuickAddModalRef"
      @success="handleDependencyQuickAddSuccess"
    /> -->
  </div>
</template>
<script lang="ts" setup>
  import { computed, inject, nextTick, reactive, ref, watch } from 'vue';
  import { CompanyTeamItemGroup, ItemGroup, Key, CheckPartDict } from '#/types';
  import { companyRegIdKey, companyTeamIdKey, companyTeamKey } from '/@/providekey/provideKeys';
  import { getItemGroupByTeam, saveItemGroupOfTeam, saveItemGroupOfTeamWithRelations } from '@/views/reg/CompanyReg.api';
  import { getGroupOfSuit } from '@/views/basicinfo/ItemSuit.api';
  import { list as listItemGroup } from '@/views/basicinfo/ItemGroup.api';
  import { message, Modal } from 'ant-design-vue';
  import groupItemModal from '@/views/basicinfo/components/GroupItemModal.vue';
  import GroupModal from '@/views/basicinfo/components/GroupModal.vue';
  import SuitModal from '@/views/basicinfo/components/SuitModal.vue';
  // import DependencyQuickAddModal from '@/components/DependencyQuickAddModal.vue';
  import {
    checkItemDependencies,
    checkAllItemsDependencies,
    analyzeItemSources,
    formatDependencyMessage,
    getMissingDependencyDetails,
  } from '@/utils/itemGroupRelationManager.js';
  import { useMessage } from '@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { listByRiskFactor } from '@/views/basicinfo/ItemGroup.api';
  import MustCheckItemGroupModal from '@/views/reg/components/MustCheckItemGroupModal.vue';
  import { listByItemGroup } from '@/views/basicinfo/CheckPartDict.api';
  import { debounce } from 'lodash-es';
  const { hasPermission } = usePermission();

  const { createErrorModal } = useMessage();

  const groupModal = ref();
  const suitModal = ref();

  // 批量部位选择相关状态
  const batchPartState = reactive({
    visible: false,
    loading: false,
    itemGroups: [] as ItemGroup[], // 需要选择部位的项目列表
    itemPartSelections: new Map<
      string,
      {
        options: Array<{ label: string; value: string; frequency: number }>;
        selectedParts: string[];
        loading: boolean;
      }
    >(), // 每个项目的部位选择状态
    source: '' as 'manual' | 'suit', // 来源：手动添加 或 套餐添加
  });

  const checkPartSelectRef = ref();

  // 依赖项目快捷添加模态框引用
  const dependencyQuickAddModalRef = ref(null);

  // 缺失的依赖项目列表
  const missingDependencies = ref([]);
  // 添加依赖项目的loading状态
  const addingDependencies = ref(false);
  // 依赖检查缓存时间戳
  const lastDependencyCheckTime = ref(0);
  // 依赖检查缓存时长（5分钟）
  const DEPENDENCY_CHECK_CACHE_DURATION = 5 * 60 * 1000;

  // 项目来源分析结果
  const itemSourceMap = ref(new Map());
  // 项目来源分析的loading状态
  const analyzingItemSources = ref(false);

  const mainTeamId = inject(companyTeamIdKey, {
    value: '',
    setValue: () => {},
  });
  const mainTeam = inject(companyTeamKey, {
    value: {},
    setValue: () => {},
  });
  const companyRegId = inject(companyRegIdKey, {
    value: '',
    setValue: () => {},
  });

  /**职业病体检必检项目*/
  const mustCheckItemGroupModal = ref(null);
  const mustCheckItemGroup = ref<ItemGroup[]>([]);
  //过滤出dataSource中没有的必检项目
  const mustCheckItemGroupFiltered = computed(() => {
    return mustCheckItemGroup.value.filter((item) => !dataSource.value.some((row) => row.itemGroupId === item.id));
  });
  const mustCheckItemTip = computed(() => {
    return `必检项目:${mustCheckItemGroup.value.length}个，${mustCheckItemGroupFiltered.value.length}个待添加！`;
  });
  function fetchMustCheckItemGorup() {
    listByRiskFactor({ riskFactorIds: mainTeam.value.risks, post: mainTeam.value.post }).then((res) => {
      if (res.success) {
        mustCheckItemGroup.value = res.result;
        //根据dataSource中是否包含必检项目，修改mustCheckAdded状态
        mustCheckItemGroup.value.forEach((item) => {
          item.mustCheckAdded = dataSource.value.some((row) => row.itemGroupId === item.id);
        });
      }
    });
  }
  function openMustCheckItemModal() {
    mustCheckItemGroupModal.value?.open(mustCheckItemGroup.value);
  }

  function addMustCheckItem() {
    let needPartSelectionItems: any[] = [];
    let addableItems: any[] = [];

    mustCheckItemGroupFiltered.value.forEach((item) => {
      // 检查是否需要部位选择
      if (item.hasCheckPart === '1') {
        needPartSelectionItems.push(item);
      } else {
        addableItems.push({
          companyRegId: companyRegId.value as string,
          teamId: mainTeamId.value as string,
          itemGroupId: item.id,
          itemGroupName: item.name,
          hisCode: item.hisCode,
          hisName: item.hisName,
          classCode: item.classCode,
          platCode: item.platCode,
          platName: item.platName,
          departmentId: item.departmentId,
          departmentName: item.departmentName,
          itemGroupCategory: '职业项目',
          addMinusFlag: 0,
          price: item.price,
          priceDisDiffAmount: 0,
          disRate: 1,
          priceAfterDis: item.price,
          payerType: mainTeam.value.payerType as string,
          minDiscountRate: item.minDiscountRate,
          departmentCode: item.departmentCode,
          mustCheckFlag: '1',
          sourceType: 'main', // 标记为主项目（必检项目）
        });
      }
    });

    // 添加不需要部位选择的项目
    if (addableItems.length > 0) {
      dataSource.value.push(...addableItems);
    }

    // 处理需要部位选择的项目
    if (needPartSelectionItems.length > 0) {
      // 使用批量部位选择器
      showBatchPartSelector(needPartSelectionItems, 'manual');
    } else {
      // 如果没有需要部位选择的项目，直接保存并处理附属和赠送项目
      updateTeamGroupInternal(false); // 处理附属和赠送项目
    }
  }

  /**套餐和组合弹窗*/
  function openSuitModal() {
    suitModal.value.open();
  }

  function openGroupModal() {
    groupModal.value.open();
  }

  async function handleSuitModalSuccess(suitList: any[]) {
    let needPartSelectionItems: any[] = [];
    let addedCount = 0;
    let skippedCount = 0;

    // 先刷新数据，确保dataSource是最新的
    await fetchData();

    for (const suit of suitList) {
      const res = await getGroupOfSuit({ suitId: suit.id });

      res.forEach((group) => {
        // 检查是否需要部位选择
        if (group.hasCheckPart === '1') {
          // 如果套餐中已经预设了部位信息，则可以直接添加
          if (group.checkPartId && group.checkPartName) {
            // 使用统一的重复检查逻辑
            const exists = isItemExists(group.id, group.checkPartId);

            if (!exists) {
              dataSource.value.push({
                companyRegId: companyRegId.value as string,
                teamId: mainTeamId.value as string,
                itemGroupId: group.id,
                itemGroupName: `${group.name}`,
                hisCode: group.hisCode,
                hisName: group.hisName,
                platCode: group.platCode,
                platName: group.platName,
                departmentId: group.departmentId,
                departmentName: group.departmentName,
                itemGroupCategory: '健康项目',
                itemSuitId: suit.id,
                itemSuitName: suit.name,
                classCode: group.classCode,
                addMinusFlag: 0,
                price: group.price,
                priceDisDiffAmount: group.priceDisDiffAmountOfSuit,
                disRate: group.disRateOfSuit,
                priceAfterDis: group.priceAfterDisOfSuit,
                payerType: mainTeam.value.payerType as string,
                minDiscountRate: group.minDiscountRateOfSuit,
                departmentCode: group.departmentCode,
                checkPartId: group.checkPartId,
                checkPartName: group.checkPartName,
                checkPartCode: group.checkPartCode,
                sourceType: 'main', // 标记为主项目（套餐项目）
              });
              addedCount++;
            } else {
              skippedCount++;
            }
          } else {
            needPartSelectionItems.push({ ...group, itemSuitId: suit.id, itemSuitName: suit.name });
          }
          return;
        }

        // 不需要部位选择的项目，使用统一的重复检查逻辑
        const exists = isItemExists(group.id);

        if (!exists) {
          dataSource.value.push({
            companyRegId: companyRegId.value as string,
            teamId: mainTeamId.value as string,
            itemGroupId: group.id,
            itemGroupName: group.name,
            hisCode: group.hisCode,
            hisName: group.hisName,
            platCode: group.platCode,
            platName: group.platName,
            departmentId: group.departmentId,
            departmentName: group.departmentName,
            itemGroupCategory: '健康项目',
            itemSuitId: suit.id,
            itemSuitName: suit.name,
            classCode: group.classCode,
            addMinusFlag: 0,
            price: group.price,
            priceDisDiffAmount: group.priceDisDiffAmountOfSuit,
            disRate: group.disRateOfSuit,
            priceAfterDis: group.priceAfterDisOfSuit,
            payerType: mainTeam.value.payerType as string,
            minDiscountRate: group.minDiscountRateOfSuit,
            departmentCode: group.departmentCode,
            sourceType: 'main', // 标记为主项目（套餐项目）
          });
          addedCount++;
        } else {
          skippedCount++;
        }
      });
    }

    // 处理需要部位选择的项目
    if (needPartSelectionItems.length > 0) {
      // 使用批量部位选择器
      showBatchPartSelector(needPartSelectionItems, 'suit');
    } else if (addedCount > 0) {
      // 套餐项目不处理附属和赠送项目，只保存套餐中的项目
      updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
    } else if (skippedCount > 0) {
      message.warning(`套餐中的 ${skippedCount} 个项目已存在，未添加新项目`);
    }
  }

  async function handleGroupModalSuccess(groupList: any[]) {
    // "添加项目"按钮：需要查找附属项目和赠送项目并添加
    let duplicateList: any[] = [];
    let needPartSelectionItems: any[] = [];
    let addedCount = 0;

    // 先刷新数据，确保dataSource是最新的
    await fetchData();

    groupList.forEach((group) => {
      // 检查是否需要部位选择
      if (group.hasCheckPart === '1') {
        needPartSelectionItems.push(group);
        return;
      }

      // 重复校验：teamId + itemGroupId + checkPartId 都相同才算重复
      // 对于不需要部位选择的项目，checkPartId 为 null
      const isDuplicate = dataSource.value.some((row) =>
        row.itemGroupId === group.id &&
        (!row.checkPartId || row.checkPartId === null || row.checkPartId === '') &&
        !(group.chargeItemOnlyFlag === 1) // 耗材可以重复
      );

      if (!isDuplicate) {
        dataSource.value.push({
          companyRegId: companyRegId.value as string,
          teamId: mainTeamId.value as string,
          itemGroupId: group.id,
          itemGroupName: group.name,
          hisCode: group.hisCode,
          hisName: group.hisName,
          classCode: group.classCode,
          platCode: group.platCode,
          platName: group.platName,
          departmentId: group.departmentId,
          departmentName: group.departmentName,
          itemGroupCategory: '健康项目',
          addMinusFlag: 0,
          price: group.price,
          priceDisDiffAmount: 0,
          disRate: 1,
          priceAfterDis: group.price,
          payerType: mainTeam.value.payerType as string,
          minDiscountRate: group.minDiscountRate,
          departmentCode: group.departmentCode,
          sourceType: 'main', // 标记为主项目
        });
        addedCount++;
      } else {
        duplicateList.push(group.name);
      }
    });

    // 用户提示
    const totalAdded = addedCount + needPartSelectionItems.length;
    const duplicateCount = duplicateList.length;

    if (totalAdded > 0 && duplicateCount > 0) {
      message.success(`成功添加 ${totalAdded} 个项目，跳过 ${duplicateCount} 个重复项目：${duplicateList.join('、')}`);
    } else if (totalAdded > 0) {
      message.success(`成功添加 ${totalAdded} 个项目`);
    } else if (duplicateCount > 0) {
      message.warning(`所选的 ${duplicateCount} 个项目已存在：${duplicateList.join('、')}`);
    }

    // 处理需要部位选择的项目
    if (needPartSelectionItems.length > 0) {
      // 使用批量部位选择器
      showBatchPartSelector(needPartSelectionItems, 'manual');
    } else if (totalAdded > 0) {
      // 如果没有需要部位选择的项目，直接保存并处理附属和赠送项目
      updateTeamGroupInternal(false); // 处理附属和赠送项目
    }
  }

  /**右侧组合列表相关操作*/
  const loading = ref<boolean>(false);
  const dataSource = ref<CompanyTeamItemGroup[]>([]);
  const tableState = reactive<{
    selectedRowKeys: Key[];
    selectedRows: CompanyTeamItemGroup[];
  }>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  const totalItemPrice = computed(() => dataSource.value.reduce((total, row) => total + row.price, 0));
  const totalPriceAfterDis = computed(() => dataSource.value.reduce((total, row) => total + row.priceAfterDis, 0));
  const column = [
    {
      title: '组合名称',
      dataIndex: 'itemGroupName',
      ellipsis: false,
      fixed: 'left',
      width: 180,
    },
    {
      title: '类型',
      dataIndex: 'itemGroupCategory',
      width: 100,
    },
    /*   {
      title: '科室',
      dataIndex: 'departmentName',
      width: '15%',
    },*/
    {
      title: '原价',
      dataIndex: 'price',
      width: '15%',
    },
    {
      title: '折后价',
      dataIndex: 'priceAfterDis',
      width: '15%',
    },
    {
      title: '折扣率',
      dataIndex: 'disRate',
      width: '15%',
    },
    {
      title: '最低折扣率',
      dataIndex: 'minDiscountRate',
      width: '15%',
    },
    {
      title: '所属套餐',
      dataIndex: 'itemSuitName',
      ellipsis: false,
      width: '15%',
    },
  ];

  const rowSelection = computed(() => {
    return {
      selectedRowKeys: tableState.selectedRowKeys,
      onChange: (selectedRowKeys: Key[], selectedRows: CompanyTeamItemGroup[]) => {
        tableState.selectedRowKeys = selectedRowKeys;
        tableState.selectedRows = selectedRows;
      },
    };
  });
  const customRow = (record) => {
    return {
      onClick: () => {
        selectRow(record);
      },
    };
  };
  const selectRow = (record) => {
    const selectedRowKeys = [...tableState.selectedRowKeys];
    const rowKey = getRowKey(record);
    if (selectedRowKeys.indexOf(rowKey) >= 0) {
      selectedRowKeys.splice(selectedRowKeys.indexOf(rowKey), 1);
    } else {
      selectedRowKeys.push(rowKey);
    }
    tableState.selectedRowKeys = selectedRowKeys;
  };

  /**右侧列表项目更新相关（内部方法，支持控制是否处理附属和赠送项目）*/
  function updateTeamGroupInternal(skipGiftAndAttach = false) {
    // 始终使用 saveItemGroupOfTeamWithRelations API，通过 skipGiftAndAttach 参数控制行为
    const apiMethod = saveItemGroupOfTeamWithRelations;
    const params = {
      teamId: mainTeamId.value,
      groupList: dataSource.value,
      skipGiftAndAttach: skipGiftAndAttach,
    };

    apiMethod(params)
      .then(async (res) => {
        if (res.success) {
          //message.success('操作成功');
          // 🔥 关键修复：保存成功后刷新数据，确保前端数据与数据库同步
          await fetchData();
        } else {
          message.error('保存失败：' + (res.message || '未知错误'));
          // 重新加载数据以恢复到保存前的状态
          await fetchData();
        }
      })
      .catch(async (error) => {
        console.error('保存团检分组项目失败:', error);
        message.error('保存失败：' + (error.message || '网络错误'));
        // 重新加载数据以恢复到保存前的状态
        await fetchData();
      });
  }

  /**异步版本的团检分组更新，用于需要等待结果的场景*/
  async function updateTeamGroupAsync(skipGiftAndAttach = false) {
    try {
      // 始终使用 saveItemGroupOfTeamWithRelations API，通过 skipGiftAndAttach 参数控制行为
      const apiMethod = saveItemGroupOfTeamWithRelations;
      const params = {
        teamId: mainTeamId.value,
        groupList: dataSource.value,
        skipGiftAndAttach: skipGiftAndAttach,
      };

      const res = await apiMethod(params);
      if (res.success) {
        message.success('操作成功');

        // 🔥 关键修复：保存成功后刷新数据，确保前端数据与数据库同步
        await fetchData();

        return true;
      } else {
        throw new Error(res.message || '保存失败');
      }
    } catch (error) {
      console.error('保存团检分组项目失败:', error);
      throw error;
    }
  }

  /**异步版本的团检分组更新，支持控制是否刷新数据*/
  async function updateTeamGroupAsyncWithoutRefresh(skipGiftAndAttach = false, skipRefresh = false) {
    try {
      // 始终使用 saveItemGroupOfTeamWithRelations API，通过 skipGiftAndAttach 参数控制行为
      const apiMethod = saveItemGroupOfTeamWithRelations;
      const params = {
        teamId: mainTeamId.value,
        groupList: dataSource.value,
        skipGiftAndAttach: skipGiftAndAttach,
      };

      const res = await apiMethod(params);
      if (res.success) {
        if (!skipRefresh) {
          message.success('操作成功');
          // 只有在不跳过刷新时才刷新数据
          await fetchData();
        }
        return true;
      } else {
        throw new Error(res.message || '保存失败');
      }
    } catch (error) {
      console.error('保存团检分组项目失败:', error);
      throw error;
    }
  }

  function updateMinDiscountRate(record, event) {
    if (event.target.value == record.minDiscountRate) {
      return;
    }
    if (record.minDiscountRate < 0) {
      createErrorModal({ title: '操作失败', content: '最低折扣率不能小于0' });
      fetchData();
      return;
    }
    let rate = Number(event.target.value);
    let disRate = record.disRate || 0;
    if (disRate < rate) {
      createErrorModal({ title: '操作失败', content: '最低折扣率不能大于折扣率' + disRate });
      fetchData();
      return;
    }
    record.minDiscountRate = parseFloat(rate.toFixed(2));
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
  }

  function handlePriceChange(record, event) {
    if (event.target.value == record.priceAfterDis) {
      return;
    }

    let priceAfterDis = Number(event.target.value);
    //判断折后价是否正确
    let rate = record.price != 0 ? priceAfterDis / record.price : 0;
    rate = parseFloat(rate.toFixed(2));
    let minDiscountRate = parseFloat(record.minDiscountRate) || 0;
    if (rate < minDiscountRate) {
      let minPrice = parseFloat((record.price * minDiscountRate).toFixed(2));
      createErrorModal({ title: '操作失败', content: '折后价过低，该项目可设置的最低折后价为' + minPrice + '元！' });
      fetchData();
      return;
    }

    record.priceAfterDis = priceAfterDis;
    record.disRate = rate;
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
  }

  function handleDisRateChange(record, event) {
    if (event.target.value == record.disRate) {
      return;
    }
    let rate = Number(event.target.value);
    let minDiscountRate = record.minDiscountRate || 0;
    if (rate < minDiscountRate) {
      createErrorModal({ title: '操作失败', content: '折扣率不能小于最低折扣率' + minDiscountRate });
      fetchData();
      return;
    }
    record.disRate = parseFloat(rate.toFixed(2));
    record.priceAfterDis = parseFloat((record.price * rate).toFixed(2));
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
  }

  function updateType(record) {
    if (record.itemGroupCategory == null) {
      message.error('请选择类型');
      return;
    }
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
  }

  function batchDelete() {
    if (tableState.selectedRowKeys.length === 0) {
      message.warn('请选择要删除的项目');
      return;
    }

    dataSource.value = dataSource.value.filter((row) => !tableState.selectedRowKeys.includes(getRowKey(row)));
    //fetchData();
    tableState.selectedRowKeys = [];
    tableState.selectedRows = [];
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
  }
  /**右侧组合列表批量更新操作*/
  interface BatchState {
    disRate: number | null;
    priceAfterDis: number | null;
    type: string;
  }

  const batchFormState = reactive<BatchState>({
    disRate: null,
    priceAfterDis: null,
    type: '健康项目',
  });
  const batchTip = computed(() => {
    if (batchFormState.disRate !== null) {
      return `确定要将所有项目的折扣率设置为${batchFormState.disRate}吗？`;
    } else if (batchFormState.priceAfterDis !== null) {
      return `确定要将所有项目的折后价设置为${batchFormState.priceAfterDis}元吗？`;
    }
    return '确定将所有项目的类型设置为' + batchFormState.type + '吗？';
  });

  function updateByBatch() {
    if (!dataSource.value || dataSource.value.length == 0) {
      message.warn('没有可以设置的项目！');
      return;
    }
    if (batchFormState.disRate == null && batchFormState.priceAfterDis == null && batchFormState.type == null) {
      message.warn('请设置批量调整条件！');
      return;
    }

    if (batchFormState.disRate !== null) {
      // 更新每一行的折扣率和折后价格
      const inputRate = Number(batchFormState.disRate);
      dataSource.value.forEach((row) => {
        // 确保折扣率不低于最小折扣率
        const rate = inputRate > row.minDiscountRate ? inputRate : row.minDiscountRate;
        row.disRate = rate;
        row.priceAfterDis = parseFloat((row.price * rate).toFixed(2));
      });
    } else if (batchFormState.priceAfterDis !== null) {
      //目标价格
      const targetTotalPrice = Number(batchFormState.priceAfterDis);
      //总原价
      const originalTotalPrice = dataSource.value.reduce((total, row) => total + row.price, 0);
      //总差价
      const totalDiffAmount = targetTotalPrice - originalTotalPrice;

      if (totalDiffAmount < 0) {
        //可折扣到的最低价格
        const minAvailableTotalPrice = dataSource.value.reduce((total, row) => {
          const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
          return total + minPrice;
        }, 0);
        //可以折扣的最多差价
        const totalAvailableDecrease = originalTotalPrice - minAvailableTotalPrice;

        if (-totalDiffAmount > totalAvailableDecrease) {
          createErrorModal({
            title: '操作失败',
            content: '设定的折后总价小于可设置的最小总价 ' + minAvailableTotalPrice.toFixed(2) + ' 元，无法执行该操作！',
          });
          return;
        }

        // 分配减少的金额
        dataSource.value.forEach((row) => {
          const minPrice = parseFloat((row.price * row.minDiscountRate).toFixed(2));
          const availableDecrease = row.price - minPrice;
          const decreaseAmount = (availableDecrease / totalAvailableDecrease) * -totalDiffAmount;
          row.priceAfterDis = parseFloat((row.price - decreaseAmount).toFixed(2));
          row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
        });
      } else if (totalDiffAmount > 0) {
        // 需要增加价格
        // 暂不考虑价格上限
        dataSource.value.forEach((row) => {
          const increaseAmount = (row.price / originalTotalPrice) * totalDiffAmount;
          row.priceAfterDis = parseFloat((row.price + increaseAmount).toFixed(2));
          row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
        });
      } else {
        dataSource.value.forEach((row) => {
          row.priceAfterDis = row.price;
          row.disRate = row.price !== 0 ? parseFloat((row.priceAfterDis / row.price).toFixed(2)) : 0;
        });
      }

      // 调整因舍入造成的差异
      let finalTotalAmount = dataSource.value.reduce((total, row) => total + row.priceAfterDis, 0);
      let finalDiff = parseFloat((targetTotalPrice - finalTotalAmount).toFixed(2));

      if (finalDiff !== 0) {
        dataSource.value[0].priceAfterDis = parseFloat((dataSource.value[0].priceAfterDis + finalDiff).toFixed(2));
        dataSource.value[0].disRate =
          dataSource.value[0].price !== 0 ? parseFloat((dataSource.value[0].priceAfterDis / dataSource.value[0].price).toFixed(2)) : 0;
      }
    }

    // 更新类型
    dataSource.value.forEach((row) => {
      row.itemGroupCategory = batchFormState.type;
    });
    updateTeamGroupInternal(true); // 跳过附属和赠送项目处理
  }

  const reset = () => {
    batchFormState.disRate = null;
    batchFormState.priceAfterDis = null;
    batchFormState.type = '健康项目';
  };

  const fetchData = async () => {
    if (mainTeamId.value) {
      loading.value = true;
      const res = await getItemGroupByTeam({ teamId: mainTeamId.value });
      dataSource.value = res;
      if (mainTeam.value.risks) {
        await fetchMustCheckItemGorup();
      }
      loading.value = false;

      // 数据加载完成后检查依赖关系
      setTimeout(async () => {
        console.log('数据加载完成，开始依赖检查，项目数量:', dataSource.value.length);

        // 执行依赖检查
        if (dataSource.value.length > 0) {
          await checkAllDependencies(true); // 延迟后的检查都当作强制检查
          // 分析项目来源类型
          await analyzeProjectSources();
        } else {
          // 如果项目列表为空，直接清空依赖提示
          console.log('项目列表为空，清空依赖提示');
          missingDependencies.value = [];
          itemSourceMap.value = new Map();
        }
      }, 100); // 延迟100ms确保数据完全准备好
    }
  };

  // 添加项目后的依赖检查（使用统一关系管理器）
  async function checkDependenciesAfterAdd(addedItems) {
    try {
      // 使用统一的关系管理器检查依赖
      const dependencyCheck = await checkItemDependencies(addedItems, dataSource.value);

      if (!dependencyCheck.isValid) {
        // 合并相同大项的依赖项目
        const mergedDependencies = mergeDependenciesByGroup(dependencyCheck.missing);

        // 更新缺失依赖项目列表，在项目列表上方显示
        missingDependencies.value = mergedDependencies;

        // 显示简单的提示消息
        message.info('检测到依赖项目缺失，请查看项目列表上方的提示');
      } else {
        // 如果没有依赖问题，清空缺失依赖列表
        missingDependencies.value = [];
      }
    } catch (error) {
      console.error('依赖检查失败:', error);
      message.warning('依赖检查失败，请注意项目依赖关系');
    }
  }

  // 部位选择相关方法 - 复用批量部位选择模态框
  async function showCheckPartSelector(itemGroup: ItemGroup) {
    console.log('Showing check part selector for item group:', itemGroup);

    // 使用批量部位选择器处理单个项目
    showBatchPartSelector([itemGroup], 'manual');
  }

  // 解析依赖错误消息，提取依赖项目信息
  function parseDependencyErrorMessage(errorMessage: string): Array<{ name: string; id?: string }> {
    const dependencyProjects = [];

    // 匹配大项依赖：项目【xxx】依赖项目【yyy】
    const groupDependencyRegex = /项目【([^】]+)】依赖项目【([^】]+)】/g;
    let match;
    while ((match = groupDependencyRegex.exec(errorMessage)) !== null) {
      const dependentProjectName = match[2];
      if (!dependencyProjects.some((p) => p.name === dependentProjectName)) {
        dependencyProjects.push({ name: dependentProjectName });
      }
    }

    return dependencyProjects;
  }

  // 显示依赖项目快捷添加模态框
  function showDependencyQuickAddModal(dependencyProjects: Array<{ name: string; id?: string }>, originalItems: any[]) {
    const dependencyNames = dependencyProjects.map((p) => p.name).join('、');

    Modal.confirm({
      title: '检测到依赖项目缺失',
      content: `项目依赖以下大项：${dependencyNames}。是否快捷添加这些依赖项目？`,
      okText: '快捷添加',
      cancelText: '取消',
      onOk: async () => {
        try {
          // 这里可以实现快捷添加逻辑
          // 由于需要搜索项目，暂时先提示用户手动添加
          message.info(`请手动搜索并添加以下依赖项目：${dependencyNames}`);
          closeBatchPartSelector();

          // 触发父组件的搜索事件，帮助用户快速定位依赖项目
          emit('searchDependencyProjects', dependencyProjects);
        } catch (error) {
          console.error('快捷添加依赖项目失败:', error);
          message.error('快捷添加失败，请手动添加依赖项目');
        }
      },
      onCancel: () => {
        closeBatchPartSelector();
      },
    });
  }

  // 获取项目显示名称（包含部位信息）
  function getDisplayItemName(record: CompanyTeamItemGroup): string {
    // 检查是否有有效的部位名称
    if (record.checkPartName && record.checkPartName.trim()) {
      const itemName = record.itemGroupName || '';
      const partName = record.checkPartName.trim();

      // 更精确的检查：项目名称是否以 "项目名-部位名" 格式结尾
      const expectedSuffix = `-${partName}`;
      if (itemName.endsWith(expectedSuffix)) {
        return itemName; // 已经包含部位信息
      }

      // 检查项目名称是否已经包含部位信息（但格式可能不同）
      if (itemName.includes(partName) && itemName.length > partName.length) {
        return itemName; // 可能已经包含部位信息，避免重复添加
      }

      // 添加部位信息
      return `${itemName}-${partName}`;
    }

    return record.itemGroupName || '';
  }

  // 获取表格行键，确保唯一性
  function getRowKey(record: CompanyTeamItemGroup): string {
    if (record.checkPartId) {
      return `${record.itemGroupId}-${record.checkPartId}`;
    }
    return record.itemGroupId;
  }

  // 检查项目是否已存在（直接比对项目ID和部位ID）
  function isItemExists(itemGroupId: string, checkPartId?: string): boolean {
    return dataSource.value.some((row) => {
      const itemIdMatch = String(row.itemGroupId) === String(itemGroupId);

      if (checkPartId) {
        // 有部位的情况：项目ID和部位ID都必须匹配
        const partIdMatch = String(row.checkPartId || '') === String(checkPartId);
        return itemIdMatch && partIdMatch;
      } else {
        // 无部位的情况：只比对项目ID，且现有项目也不能有部位
        const rowHasNoPart = !row.checkPartId || row.checkPartId === '' || row.checkPartId === null;
        return itemIdMatch && rowHasNoPart;
      }
    });
  }

  // 批量部位选择辅助方法
  function getItemPartSelection(itemGroupId: string) {
    if (!batchPartState.itemPartSelections.has(itemGroupId)) {
      batchPartState.itemPartSelections.set(itemGroupId, {
        options: [],
        selectedParts: [],
        loading: false,
      });
    }
    return batchPartState.itemPartSelections.get(itemGroupId)!;
  }

  function hasAnyPartSelected(): boolean {
    return batchPartState.itemGroups.some((item) => getItemPartSelection(item.id).selectedParts.length > 0);
  }

  function getBatchPartNameById(itemGroupId: string, partId: string): string {
    const selection = getItemPartSelection(itemGroupId);
    const option = selection.options.find((opt) => opt.value === partId);
    return option ? option.name : partId; // 直接返回纯净的部位名称
  }

  // 获取部位选项的完整信息（包含名称、代码等）
  function getBatchPartOptionById(itemGroupId: string, partId: string) {
    const selection = getItemPartSelection(itemGroupId);
    return selection.options.find((opt) => opt.value === partId);
  }

  // 显示批量部位选择器
  function showBatchPartSelector(itemGroups: ItemGroup[], source: 'manual' | 'suit' = 'manual') {
    // 重置状态
    batchPartState.itemGroups = [...itemGroups];
    batchPartState.source = source;
    batchPartState.itemPartSelections.clear();
    batchPartState.loading = false;

    // 为每个项目初始化选择状态
    itemGroups.forEach((item) => {
      getItemPartSelection(item.id);
    });

    // 显示模态框
    batchPartState.visible = true;
  }

  // 关闭批量部位选择器
  function closeBatchPartSelector() {
    batchPartState.visible = false;
    batchPartState.itemGroups = [];
    batchPartState.itemPartSelections.clear();
    batchPartState.source = 'manual';
  }

  // 加载指定项目的部位选项
  async function loadItemParts(itemGroupId: string, keyword?: string) {
    const selection = getItemPartSelection(itemGroupId);
    selection.loading = true;

    try {
      const params = { itemGroupId };
      if (keyword && keyword.trim()) {
        params.keyword = keyword.trim();
      }

      const res = await listByItemGroup(params);

      // 处理直接数组格式
      if (Array.isArray(res)) {
        if (res.length > 0) {
          selection.options = res.map((item: any) => ({
            label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
            value: item.id || '',
            frequency: item.frequency || 0,
            code: item.code || '', // 保存部位代码
            name: item.name || '', // 保存部位名称
          }));
        } else {
          selection.options = [];
          message.warning('没有找到可用的部位选项');
        }
      }
      // 处理包装对象格式
      else if (res && res.success && Array.isArray(res.result)) {
        if (res.result.length > 0) {
          selection.options = res.result.map((item: any) => ({
            label: `${item.name || ''}${item.frequency ? ` (${item.frequency}次)` : ''}`,
            value: item.id || '',
            frequency: item.frequency || 0,
            code: item.code || '', // 保存部位代码
            name: item.name || '', // 保存部位名称
          }));
        } else {
          selection.options = [];
          message.warning('没有找到可用的部位选项');
        }
      } else {
        console.log('⚠ 意外的响应格式:', res);
        selection.options = [];
        message.warning('响应格式异常，无法加载部位选项');
      }
    } catch (error) {
      console.error('加载部位选项失败:', error);
      message.error('加载部位选项失败: ' + error.message);
      selection.options = [];
    } finally {
      selection.loading = false;
    }
  }

  // 搜索指定项目的部位
  const searchItemParts = debounce(async (itemGroupId: string, keyword: string) => {
    if (!keyword || keyword.trim() === '') {
      await loadItemParts(itemGroupId);
    } else {
      await loadItemParts(itemGroupId, keyword.trim());
    }
  }, 300);

  // 批量确认添加项目和部位
  async function confirmBatchAddItemsWithParts() {
    // 验证每个项目都至少选择了一个部位
    const unselectedItems = batchPartState.itemGroups.filter((item) => getItemPartSelection(item.id).selectedParts.length === 0);

    if (unselectedItems.length > 0) {
      const itemNames = unselectedItems.map((item) => item.name).join('、');
      message.warn(`以下项目还未选择部位：${itemNames}`);
      return;
    }

    batchPartState.loading = true;
    let addedCount = 0;
    let skippedCount = 0;

    try {
      // 为每个项目的每个部位创建记录
      batchPartState.itemGroups.forEach((itemGroup) => {
        const selection = getItemPartSelection(itemGroup.id);

        selection.selectedParts.forEach((partId) => {
          // 获取部位选项的完整信息
          const partOption = getBatchPartOptionById(itemGroup.id, partId);

          // 直接使用纯净的部位名称和代码，无需额外处理
          const partName = partOption ? partOption.name : '';
          const partCode = partOption ? partOption.code : '';

          // 检查是否已存在
          const exists = isItemExists(itemGroup.id, partId);

          if (!exists) {
            dataSource.value.push({
              companyRegId: companyRegId.value as string,
              teamId: mainTeamId.value as string,
              itemGroupId: itemGroup.id,
              itemGroupName: `${itemGroup.name}`,
              hisCode: itemGroup.hisCode,
              hisName: itemGroup.hisName,
              classCode: itemGroup.classCode,
              platCode: itemGroup.platCode,
              platName: itemGroup.platName,
              departmentId: itemGroup.departmentId,
              departmentName: itemGroup.departmentName,
              departmentCode: itemGroup.departmentCode,
              itemGroupCategory: '健康项目',
              addMinusFlag: 0,
              price: itemGroup.price,
              priceDisDiffAmount: 0,
              disRate: 1,
              priceAfterDis: itemGroup.price,
              payerType: mainTeam.value.payerType as string,
              minDiscountRate: itemGroup.minDiscountRate,
              checkPartId: partId,
              checkPartName: partName,
              checkPartCode: partCode, // 使用从API获取的部位代码
            });
            addedCount++;
          } else {
            skippedCount++;
          }
        });
      });

      // 在保存前进行依赖检查
      if (addedCount > 0) {
        // 构建添加的项目列表用于依赖检查
        const addedItems = [];
        batchPartState.itemGroups.forEach((itemGroup) => {
          const selection = getItemPartSelection(itemGroup.id);
          selection.selectedParts.forEach((partId) => {
            const partOption = selection.options.find((opt) => opt.value === partId);
            addedItems.push({
              itemGroupId: itemGroup.id,
              itemGroupName: itemGroup.name,
              checkPartId: partId,
              checkPartName: partOption?.label || '',
            });
          });
        });

        // 先进行依赖检查
        await checkDependenciesAfterAdd(addedItems);

        message.success(`成功添加 ${addedCount} 个项目-部位组合${skippedCount > 0 ? `，跳过 ${skippedCount} 个重复项目` : ''}`);

        // 保存到后端（包含附属项目和赠送项目处理）
        await updateTeamGroupAsync(false); // false表示不跳过附属和赠送项目处理

        closeBatchPartSelector();
      } else if (skippedCount > 0) {
        message.warning(`所有 ${skippedCount} 个项目-部位组合都已存在，未添加新项目`);
        closeBatchPartSelector();
      }
    } catch (error) {
      console.error('批量添加项目失败:', error);

      // 检查是否是依赖项目错误，如果是则提供快捷添加选项
      const errorMessage = error.message || '未知错误';
      if (errorMessage.includes('依赖项目') || errorMessage.includes('依赖小项')) {
        // 解析错误消息中的依赖项目信息
        const dependencyProjects = parseDependencyErrorMessage(errorMessage);
        if (dependencyProjects.length > 0) {
          showDependencyQuickAddModal(dependencyProjects, batchPartState.itemGroups);
        } else {
          message.error('添加失败: ' + errorMessage);
          closeBatchPartSelector();
        }
      } else {
        message.error('添加失败: ' + errorMessage);
        closeBatchPartSelector();
      }

      // 回滚：移除刚添加的项目
      batchPartState.itemGroups.forEach((itemGroup) => {
        const selection = getItemPartSelection(itemGroup.id);
        selection.selectedParts.forEach((partId) => {
          const index = dataSource.value.findIndex((row) => row.itemGroupId === itemGroup.id && row.checkPartId === partId);
          if (index !== -1) {
            dataSource.value.splice(index, 1);
          }
        });
      });
    } finally {
      batchPartState.loading = false;
    }
  }

  // 处理一键添加所有依赖项目（从项目列表上方的提示区域）
  async function handleQuickAddAllDependencies() {
    if (missingDependencies.value.length === 0) {
      return;
    }

    try {
      addingDependencies.value = true;

      console.log('开始快捷添加依赖项目:', missingDependencies.value);

      // 直接通过API搜索并添加依赖项目
      const projectsToAdd = [];

      for (const dependency of missingDependencies.value) {
        try {
          // 通过项目ID直接查询项目信息
          console.log(`搜索依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);

          // 设置搜索条件为项目名称
          const searchParams = {
            name: dependency.dependentName,
            pageNo: 1,
            pageSize: 10,
          };

          // 调用项目搜索API
          const searchResult = await listItemGroup(searchParams);
          console.log(`搜索结果:`, searchResult);

          // 在搜索结果中查找匹配的项目
          const foundProject = searchResult.records?.find((item) => item.id === dependency.dependentId);

          if (foundProject) {
            console.log(`找到依赖项目: ${foundProject.name}`);
            projectsToAdd.push(foundProject);
          } else {
            console.warn(`未找到依赖项目: ${dependency.dependentName} (${dependency.dependentId})`);
          }
        } catch (searchError) {
          console.error(`搜索依赖项目失败: ${dependency.dependentName}`, searchError);
        }
      }

      if (projectsToAdd.length === 0) {
        message.warn('未找到可添加的依赖项目，请检查项目是否存在');
        return;
      }

      console.log(
        '准备添加的项目:',
        projectsToAdd.map((p) => p.name)
      );

      // 批量添加依赖项目，跳过数据刷新以避免加载现有项目的附属赠送项目
      await handleAddBatch(projectsToAdd, true);

      // 清空缺失依赖列表
      missingDependencies.value = [];

      message.success(`成功添加 ${projectsToAdd.length} 个依赖项目`);
    } catch (error) {
      console.error('快捷添加依赖项目失败:', error);
      message.error('快捷添加失败: ' + (error.message || '未知错误'));
    } finally {
      addingDependencies.value = false;
    }
  }

  // 批量添加项目的辅助方法
  async function handleAddBatch(projectsToAdd, skipRefresh = false) {
    const newItemsToAdd = [];
    const needPartSelectionItems = [];

    for (const project of projectsToAdd) {
      // 检查是否需要部位选择
      if (project.hasCheckPart === '1') {
        // 需要部位选择的项目，收集起来统一处理
        needPartSelectionItems.push(project);
      } else {
        // 不需要部位选择的项目，直接添加到前端数据源
        const newItem = {
          companyRegId: companyRegId.value as string,
          teamId: mainTeamId.value as string,
          itemGroupId: project.id,
          itemGroupName: project.name,
          hisCode: project.hisCode,
          hisName: project.hisName,
          classCode: project.classCode,
          platCode: project.platCode,
          platName: project.platName,
          departmentId: project.departmentId,
          departmentName: project.departmentName,
          itemGroupCategory: project.type || '健康项目',
          addMinusFlag: 0,
          price: project.price,
          priceDisDiffAmount: 0,
          disRate: 1,
          priceAfterDis: project.price,
          payerType: mainTeam.value.payerType as string,
          minDiscountRate: project.minDiscountRate,
          departmentCode: project.departmentCode,
        };
        dataSource.value.push(newItem);
        newItemsToAdd.push(newItem);
      }
    }

    // 处理需要部位选择的项目
    if (needPartSelectionItems.length > 0) {
      // 使用批量部位选择器
      showBatchPartSelector(needPartSelectionItems, 'manual');
    }

    // 如果有不需要部位选择的项目，保存到后端
    if (newItemsToAdd.length > 0) {
      // 保存所有项目，但跳过附属和赠送项目处理（避免重复添加）
      await updateTeamGroupAsyncWithoutRefresh(true, skipRefresh); // true表示跳过附属和赠送项目处理
    }
  }

  // 处理依赖项目快捷添加成功
  function handleDependencyQuickAddSuccess(addedItems) {
    console.log('依赖项目快捷添加成功:', addedItems);

    // 添加到数据源
    addedItems.forEach((item) => {
      const newItem = {
        companyRegId: companyRegId.value as string,
        teamId: mainTeamId.value as string,
        itemGroupId: item.itemGroupId,
        itemGroupName: item.itemGroupName,
        checkPartId: item.checkPartId,
        checkPartName: item.checkPartName,
        hisCode: item.hisCode,
        hisName: item.hisName,
        classCode: item.classCode,
        platCode: item.platCode,
        platName: item.platName,
        departmentId: item.departmentId,
        departmentName: item.departmentName,
        itemGroupCategory: item.itemGroupCategory || '健康项目',
        addMinusFlag: 0,
        price: item.price,
        priceDisDiffAmount: 0,
        disRate: 1,
        priceAfterDis: item.price,
        payerType: mainTeam.value.payerType as string,
        minDiscountRate: item.minDiscountRate,
        departmentCode: item.departmentCode,
      };
      dataSource.value.push(newItem);
    });

    // 清空缺失依赖列表
    missingDependencies.value = [];

    // 保存到后端
    updateTeamGroupInternal(false); // 处理附属和赠送项目

    message.success(`成功添加 ${addedItems.length} 个依赖项目`);
  }

  // 检查所有项目的依赖关系（用于打开列表时的全面检查）
  async function checkAllDependencies(forceCheck = false) {
    console.log('checkAllDependencies 被调用，forceCheck:', forceCheck, '项目数量:', dataSource.value?.length || 0);

    // 确保数据源存在且有效
    if (!dataSource.value || dataSource.value.length === 0) {
      console.log('项目列表为空，清空依赖提示');
      missingDependencies.value = [];
      return;
    }

    // 检查缓存，避免频繁检查
    const now = Date.now();
    if (!forceCheck && now - lastDependencyCheckTime.value < DEPENDENCY_CHECK_CACHE_DURATION) {
      console.log('依赖检查缓存有效，跳过检查');
      return;
    }

    try {
      console.log('开始检查所有项目的依赖关系');

      // 将所有现有项目作为"新添加"项目来检查依赖
      const allItems = dataSource.value
        .filter((item) => item.itemGroupId && item.addMinusFlag !== -1) // 过滤掉无效项目和减项
        .map((item) => ({
          itemGroupId: item.itemGroupId,
          itemGroupName: item.itemGroupName,
          checkPartId: item.checkPartId,
          checkPartName: item.checkPartName,
        }));

      console.log('过滤前项目数量:', dataSource.value.length, '过滤后项目数量:', allItems.length);

      if (allItems.length === 0) {
        console.log('没有有效的项目需要检查依赖');
        missingDependencies.value = [];
        return;
      }

      // 使用专门的函数检查所有项目的依赖关系
      const dependencyCheck = await checkAllItemsDependencies(allItems);
      console.log('依赖检查结果:', dependencyCheck);

      if (!dependencyCheck.isValid && dependencyCheck.missing.length > 0) {
        // 合并相同大项的依赖项目
        const mergedDependencies = mergeDependenciesByGroup(dependencyCheck.missing);
        console.log('合并后的依赖项目:', mergedDependencies);

        // 更新缺失依赖项目列表
        missingDependencies.value = mergedDependencies;

        console.log(
          '发现缺失的依赖大项:',
          mergedDependencies.map((dep) => dep.dependentName)
        );
      } else {
        // 如果没有依赖问题或项目为空，清空缺失依赖列表
        console.log('没有依赖问题，清空依赖提示');
        missingDependencies.value = [];
      }

      // 更新检查时间戳
      lastDependencyCheckTime.value = now;
      console.log('依赖检查完成，最终依赖项目数量:', missingDependencies.value.length);
    } catch (error) {
      console.error('检查所有依赖关系失败:', error);
      // 确保异常情况下不影响用户体验
      missingDependencies.value = [];
    }
  }

  // 合并相同大项的依赖项目
  function mergeDependenciesByGroup(dependencies) {
    const groupMap = new Map();

    dependencies.forEach((dep) => {
      const groupId = dep.dependentId;

      if (!groupMap.has(groupId)) {
        groupMap.set(groupId, {
          dependentId: groupId,
          dependentName: dep.dependentName,
          dependentType: dep.dependentType,
          relatedItems: [], // 依赖此大项的项目列表
          dependentItemDetails: [], // 具体依赖的小项列表
        });
      }

      const group = groupMap.get(groupId);

      // 添加依赖此大项的项目信息
      const itemDisplay = dep.partName ? `${dep.itemName}-${dep.partName}` : dep.itemName;
      if (!group.relatedItems.includes(itemDisplay)) {
        group.relatedItems.push(itemDisplay);
      }

      // 添加具体依赖的小项信息
      if (dep.dependentItemDetails && !group.dependentItemDetails.includes(dep.dependentItemDetails)) {
        group.dependentItemDetails.push(dep.dependentItemDetails);
      }
    });

    // 转换为数组并格式化小项信息
    return Array.from(groupMap.values()).map((group) => ({
      ...group,
      dependentItemDetails: group.dependentItemDetails.filter(Boolean).join('、') || '',
      relatedItemsText: group.relatedItems.join('、'),
    }));
  }

  // 分析项目来源类型
  async function analyzeProjectSources() {
    try {
      analyzingItemSources.value = true;

      const itemIds = dataSource.value.filter((item) => item.itemGroupId && item.addMinusFlag !== -1).map((item) => item.itemGroupId);

      if (itemIds.length > 0) {
        const sourceAnalysis = await analyzeItemSources(itemIds);
        itemSourceMap.value = new Map(Object.entries(sourceAnalysis));
      } else {
        itemSourceMap.value = new Map();
      }
    } catch (error) {
      console.error('分析项目来源失败:', error);
      itemSourceMap.value = new Map();
    } finally {
      analyzingItemSources.value = false;
    }
  }

  // 获取项目的来源类型
  function getItemSourceType(itemGroupId) {
    return itemSourceMap.value.get(itemGroupId) || 'main';
  }

  // 获取项目来源的badge配置
  function getItemSourceBadge(itemGroupId) {
    const sourceType = getItemSourceType(itemGroupId);

    switch (sourceType) {
      case 'dependent':
        return {
          text: '依赖',
          bg: '#fa8c16', // 橙色 - 依赖项目，表示需要注意的关联关系
          color: '#fff',
          title: '此项目是依赖项目，由其他项目的依赖关系自动添加，点击查看详情',
        };
      case 'gift':
        return {
          text: '赠送',
          bg: '#52c41a', // 绿色 - 赠送项目，表示免费获得
          color: '#fff',
          title: '此项目是赠送项目，由其他项目的赠送关系自动添加，点击查看详情',
        };
      case 'attach':
        return {
          text: '附属',
          bg: '#722ed1', // 紫色 - 附属项目，表示与主项目的紧密关联
          color: '#fff',
          title: '此项目是附属项目，由其他项目的附属关系自动添加，点击查看详情',
        };
      default:
        return {
          text: '主项',
          bg: '#1890ff', // 蓝色 - 主项目，表示用户主动添加
          color: '#fff',
          title: '此项目是主项目，由用户主动添加',
        };
    }
  }

  // 获取项目来源类型的颜色
  function getSourceTypeColor(sourceType) {
    switch (sourceType) {
      case 'gift':
        return 'green'; // 赠送项目 - 绿色
      case 'dependent':
        return 'orange'; // 依赖项目 - 橙色
      case 'attach':
        return 'purple'; // 附属项目 - 紫色
      default:
        return 'blue'; // 主项目 - 蓝色
    }
  }

  // 获取项目来源类型的文本
  function getSourceTypeText(sourceType) {
    switch (sourceType) {
      case 'gift':
        return '赠送';
      case 'dependent':
        return '依赖';
      case 'attach':
        return '附属';
      default:
        return '主项';
    }
  }

  // 显示关系详情弹窗
  function showRelationDetail(record) {
    // TODO: 实现关系详情弹窗
    message.info(`点击了 ${getSourceTypeText(record.sourceType)} 项目：${record.itemGroupName}`);
  }

  watch(
    mainTeamId,
    () => {
      if (mainTeamId.value) {
        fetchData();
      } else {
        dataSource.value = [];
        // 清空依赖项目提示
        missingDependencies.value = [];
        // 清空项目来源分析结果
        itemSourceMap.value = new Map();
        // 清空依赖检查缓存，确保重新检查
        lastDependencyCheckTime.value = 0;
      }
    },
    { immediate: false }
  );
</script>

<style scoped>
  .missing-dependencies-alert .alert-title {
    font-weight: bold;
    margin-bottom: 8px;
  }

  .missing-dependencies-alert .missing-projects-list {
    margin-top: 8px;
  }
</style>
