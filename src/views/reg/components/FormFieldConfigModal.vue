<template>
  <a-modal v-model:open="visible" title="字段显示位置配置" width="1000px" :confirm-loading="confirmLoading" @ok="handleSave" @cancel="handleCancel">
    <a-spin :spinning="loading">
      <div class="config-container">
        <!-- 配置基本信息 -->
        <a-card size="small" title="配置信息" class="config-info-card">
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item label="表单类型" v-bind="validateInfos.formType">
                <a-select v-model:value="formData.formType" placeholder="请选择表单类型" @change="handleFormTypeChange">
                  <a-select-option value="customer_reg">客户登记表单</a-select-option>
                  <!--                  <a-select-option value="company_reg">单位登记表单</a-select-option>
                  <a-select-option value="exam_config">体检配置表单</a-select-option>-->
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item label="是否启用">
                <a-switch v-model:checked="formData.isActive" />
              </a-form-item>
            </a-col>
          </a-row>
        </a-card>

        <!-- 字段显示配置区域 -->
        <a-card size="small" title="字段显示位置配置" class="fields-config-card">
          <template #extra>
            <a-space>
              <a-button size="small" @click="loadDefaultConfig"> <Icon icon="ant-design:reload-outlined" /> 加载默认配置 </a-button>
              <a-button size="small" @click="selectAll"> <Icon icon="ant-design:check-outlined" /> 全选 </a-button>
              <a-button size="small" @click="selectNone"> <Icon icon="ant-design:close-outlined" /> 全不选 </a-button>
            </a-space>
          </template>

          <!-- 字段配置表格 -->
          <div class="fields-table">
            <div class="field-group-title">配置字段显示位置：</div>
            <a-table :columns="fieldColumns" :data-source="formData.fields" :pagination="false" size="small" row-key="fieldKey" :scroll="{ y: 400 }">
              <!-- 字段名称列 -->
              <template #fieldName="{ record }">
                <div class="field-name-cell">
                  <span class="field-name">{{ record.fieldName }}</span>
                  <a-tag v-if="record.groupName" size="small" color="blue">
                    {{ record.groupName }}
                  </a-tag>
                </div>
              </template>

              <!-- 显示位置列 -->
              <template #displayLocation="{ record }">
                <a-select v-model:value="record.displayLocation" size="small" style="width: 120px" @change="handleLocationChange(record)">
                  <a-select-option value="outside">
                    <a-tag color="green">外部区域</a-tag>
                  </a-select-option>
                  <a-select-option value="collapse">
                    <a-tag color="blue">折叠区域</a-tag>
                  </a-select-option>
                  <a-select-option value="hidden">
                    <a-tag color="red">隐藏</a-tag>
                  </a-select-option>
                </a-select>
              </template>

              <!-- 是否可见列 -->
              <template #isVisible="{ record }">
                <a-switch v-model:checked="record.isVisible" size="small" @change="handleVisibilityChange(record)" />
              </template>

              <!-- 排序列 -->
              <template #sortOrder="{ record }">
                <a-input-number v-model:value="record.sortOrder" size="small" :min="0" :max="999" style="width: 80px" />
              </template>

              <!-- 操作列 -->
              <template #action="{ record }">
                <a-space size="small">
                  <a-button type="link" size="small" @click="moveUp(record)" :disabled="isFirstInGroup(record)">
                    <Icon icon="ant-design:arrow-up-outlined" />
                  </a-button>
                  <a-button type="link" size="small" @click="moveDown(record)" :disabled="isLastInGroup(record)">
                    <Icon icon="ant-design:arrow-down-outlined" />
                  </a-button>
                </a-space>
              </template>
            </a-table>
          </div>
        </a-card>
      </div>
    </a-spin>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue';
  import { Form, message } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import {
    FormDisplayConfig,
    FieldDisplayConfig,
    FieldDisplayLocation,
    saveFormDisplayConfig,
    FORM_TYPES,
    migrateFieldConfig,
    validateFieldConfig,
    getDefaultFieldConfig,
  } from '../FormFieldConfig.api';

  const emit = defineEmits(['register', 'success']);

  const visible = ref(false);
  const loading = ref(false);
  const confirmLoading = ref(false);

  const formData = reactive<FormDisplayConfig>({
    configName: '',
    centerId: '',
    centerName: '',
    formType: 'customer_reg',
    isActive: true,
    fields: [],
  });

  // 表单验证
  const useForm = Form.useForm;
  const rules = reactive({
    formType: [{ required: true, message: '请选择表单类型' }],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, rules);

  // 表格列定义
  const fieldColumns = [
    {
      title: '字段名称',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 150,
      slots: { customRender: 'fieldName' },
    },
    {
      title: '字段标识',
      dataIndex: 'fieldKey',
      key: 'fieldKey',
      width: 120,
    },
    {
      title: '显示位置',
      dataIndex: 'displayLocation',
      key: 'displayLocation',
      width: 140,
      slots: { customRender: 'displayLocation' },
    },
    {
      title: '是否可见',
      dataIndex: 'isVisible',
      key: 'isVisible',
      width: 80,
      align: 'center',
      slots: { customRender: 'isVisible' },
    },
    {
      title: '排序',
      dataIndex: 'sortOrder',
      key: 'sortOrder',
      width: 80,
      align: 'center',
      slots: { customRender: 'sortOrder' },
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      align: 'center',
      slots: { customRender: 'action' },
    },
  ];

  // 表单类型变化处理
  const handleFormTypeChange = (formType: string) => {
    loadDefaultConfig();
  };

  // 显示位置变化处理
  const handleLocationChange = (record: FieldDisplayConfig) => {
    // 根据显示位置自动设置可见性
    if (record.displayLocation === FieldDisplayLocation.HIDDEN) {
      record.isVisible = false;
    } else {
      record.isVisible = true;
    }
  };

  // 可见性变化处理
  const handleVisibilityChange = (record: FieldDisplayConfig) => {
    // 如果设置为不可见，自动设置为隐藏
    if (!record.isVisible) {
      record.displayLocation = FieldDisplayLocation.HIDDEN;
    } else if (record.displayLocation === FieldDisplayLocation.HIDDEN) {
      // 如果从隐藏状态设置为可见，默认放到折叠区域
      record.displayLocation = FieldDisplayLocation.COLLAPSE;
    }
  };

  // 检查是否是分组中的第一个
  const isFirstInGroup = (record: FieldDisplayConfig) => {
    const sameGroupFields = formData.fields.filter((f) => f.groupName === record.groupName);
    return sameGroupFields[0]?.fieldKey === record.fieldKey;
  };

  // 检查是否是分组中的最后一个
  const isLastInGroup = (record: FieldDisplayConfig) => {
    const sameGroupFields = formData.fields.filter((f) => f.groupName === record.groupName);
    return sameGroupFields[sameGroupFields.length - 1]?.fieldKey === record.fieldKey;
  };

  // 上移字段
  const moveUp = (record: FieldDisplayConfig) => {
    const index = formData.fields.findIndex((f) => f.fieldKey === record.fieldKey);
    if (index > 0) {
      const temp = formData.fields[index];
      formData.fields[index] = formData.fields[index - 1];
      formData.fields[index - 1] = temp;

      // 更新排序值
      updateSortOrder();
    }
  };

  // 下移字段
  const moveDown = (record: FieldDisplayConfig) => {
    const index = formData.fields.findIndex((f) => f.fieldKey === record.fieldKey);
    if (index < formData.fields.length - 1) {
      const temp = formData.fields[index];
      formData.fields[index] = formData.fields[index + 1];
      formData.fields[index + 1] = temp;

      // 更新排序值
      updateSortOrder();
    }
  };

  // 更新排序值
  const updateSortOrder = () => {
    formData.fields.forEach((field, index) => {
      field.sortOrder = index + 1;
    });
  };

  // 全选
  const selectAll = () => {
    formData.fields.forEach((field) => {
      field.isVisible = true;
    });
  };

  // 全不选
  const selectNone = () => {
    formData.fields.forEach((field) => {
      field.isVisible = false;
    });
  };

  // 加载默认配置
  const loadDefaultConfig = () => {
    if (formData.formType === FORM_TYPES.CUSTOMER_REG) {
      // 使用新的默认配置函数
      formData.fields = getDefaultFieldConfig();

      // 确保配置完整性
      formData.fields = migrateFieldConfig(formData.fields);

      // 验证配置
      const validation = validateFieldConfig(formData.fields);
      if (!validation.isValid) {
        console.warn('默认配置验证失败:', validation.errors);
        message.warning('默认配置存在问题，请检查字段配置');
      }

      if (validation.warnings.length > 0) {
        console.warn('默认配置警告:', validation.warnings);
      }

      message.success('已加载默认字段配置');
    } else {
      formData.fields = [];
      message.info('该表单类型暂无默认配置');
    }
  };

  // 保存配置
  const handleSave = async () => {
    try {
      await validate();
      confirmLoading.value = true;

      // 验证字段配置
      const validation = validateFieldConfig(formData.fields);
      if (!validation.isValid) {
        message.error('字段配置验证失败: ' + validation.errors.join(', '));
        return;
      }

      if (validation.warnings.length > 0) {
        console.warn('字段配置警告:', validation.warnings);
      }

      // 确保配置完整性
      formData.fields = migrateFieldConfig(formData.fields);

      try {
        // 尝试保存到服务器
        await saveFormDisplayConfig(formData);
        message.success('配置保存成功');
      } catch (apiError) {
        console.warn('API保存失败，尝试本地存储:', apiError);

        // 如果API失败，保存到本地存储作为备份
        try {
          localStorage.setItem('fieldDisplayConfig', JSON.stringify(formData));
          message.warning('服务器保存失败，已保存到本地存储');
        } catch (localError) {
          console.error('本地存储也失败:', localError);
          message.error('保存失败，请稍后重试');
          return;
        }
      }

      emit('success');
      handleCancel();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败: ' + (error.message || '未知错误'));
    } finally {
      confirmLoading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
    resetFields();
    formData.fields = [];
  };

  // 打开弹窗
  const open = (config?: FormDisplayConfig) => {
    visible.value = true;
    if (config) {
      Object.assign(formData, config);
    } else {
      // 新建时加载默认配置
      loadDefaultConfig();
    }
  };

  // 暴露方法
  defineExpose({
    open,
  });
</script>

<style scoped>
  .config-container {
    max-height: 600px;
    overflow-y: auto;
  }

  .config-info-card {
    margin-bottom: 16px;
  }

  .fields-config-card {
    margin-bottom: 16px;
  }

  .fields-list {
    max-height: 400px;
    overflow-y: auto;
  }

  .fields-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 12px;
  }

  .field-group-title {
    font-size: 14px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
    padding: 8px 12px;
    background: #f0f9ff;
    border-left: 3px solid #1890ff;
    border-radius: 4px;
  }

  .field-item {
    border: 1px solid #e8e8e8;
    border-radius: 6px;
    margin-bottom: 8px;
    padding: 12px;
    background: #fff;
    transition: all 0.2s;
  }

  .field-item:hover {
    border-color: #1890ff;
    box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
  }

  .field-enabled {
    background: #f6ffed;
    border-color: #b7eb8f;
  }

  .field-header {
    display: flex;
    align-items: center;
  }

  .field-info {
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
  }

  .field-checkbox {
    margin: 0;
  }

  .field-name {
    font-weight: 500;
    color: #333;
    flex: 1;
  }
</style>
