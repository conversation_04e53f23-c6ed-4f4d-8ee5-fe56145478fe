<template>
  <a-modal
    v-model:open="visible"
    title="字段排序预览"
    width="600px"
    :footer="null"
  >
    <div class="sort-preview">
      <div class="preview-header">
        <h4>折叠区域字段显示顺序预览</h4>
        <p class="preview-desc">以下是字段在表单中的实际显示顺序：</p>
      </div>
      
      <div class="preview-content">
        <div class="form-preview">
          <a-row :gutter="16">
            <template v-for="(field, index) in visibleFields" :key="field.fieldKey">
              <a-col :span="12" class="field-preview-item">
                <div class="field-preview">
                  <div class="field-preview-header">
                    <span class="field-order-badge">{{ index + 1 }}</span>
                    <span class="field-preview-name">{{ field.fieldName }}</span>
                    <a-tag v-if="field.groupName" size="small" color="blue">
                      {{ field.groupName }}
                    </a-tag>
                  </div>
                  <div class="field-preview-component">
                    <!-- 根据字段类型显示不同的预览组件 -->
                    <a-input 
                      v-if="isInputField(field.fieldKey)"
                      :placeholder="`请输入${field.fieldName}`"
                      size="small"
                      disabled
                    />
                    <a-select 
                      v-else-if="isSelectField(field.fieldKey)"
                      :placeholder="`请选择${field.fieldName}`"
                      size="small"
                      disabled
                      style="width: 100%"
                    />
                    <a-switch 
                      v-else-if="isSwitchField(field.fieldKey)"
                      size="small"
                      disabled
                    />
                    <a-textarea 
                      v-else-if="isTextareaField(field.fieldKey)"
                      :placeholder="`请输入${field.fieldName}`"
                      :rows="2"
                      size="small"
                      disabled
                    />
                    <a-cascader
                      v-else-if="field.fieldKey === 'pcaCode'"
                      :placeholder="`请选择${field.fieldName}`"
                      size="small"
                      disabled
                      style="width: 100%"
                    />
                    <a-input-number
                      v-else-if="field.fieldKey === 'workYears'"
                      :placeholder="`请输入${field.fieldName}`"
                      size="small"
                      disabled
                      style="width: 100%"
                    />
                    <a-input 
                      v-else
                      :placeholder="`请输入${field.fieldName}`"
                      size="small"
                      disabled
                    />
                  </div>
                </div>
              </a-col>
            </template>
          </a-row>
        </div>
      </div>
      
      <div class="preview-footer">
        <a-space>
          <a-button @click="visible = false">关闭</a-button>
          <a-button type="primary" @click="handleConfirm">确认排序</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from 'vue';
import { FieldDisplayConfig } from '../config/FieldDefinitions';

const props = defineProps<{
  open: boolean;
  fields: FieldDisplayConfig[];
}>();

const emit = defineEmits(['update:open', 'confirm']);

const visible = ref(false);

// 监听打开状态
watch(
  () => props.open,
  (newVal) => {
    visible.value = newVal;
  },
  { immediate: true }
);

// 监听visible变化
watch(visible, (newVal) => {
  emit('update:open', newVal);
});

// 计算可见字段
const visibleFields = computed(() => {
  return props.fields
    .filter(field => field.isVisible)
    .sort((a, b) => a.sortOrder - b.sortOrder);
});

// 判断字段类型
const isInputField = (fieldKey: string) => {
  const inputFields = [
    'nation', 'postCode', 'healthNo', 'examCardNo', 'introducer',
    'recipeTitle', 'emergencyContact', 'emergencyPhone', 'companyName',
    'belongCompany', 'department', 'originalIdCard'
  ];
  return inputFields.includes(fieldKey);
};

const isSelectField = (fieldKey: string) => {
  const selectFields = [
    'bloodType', 'countryCode', 'eduLevel', 'secretLevel',
    'customerCategory', 'relationWithOriginal', 'marriageStatus'
  ];
  return selectFields.includes(fieldKey);
};

const isSwitchField = (fieldKey: string) => {
  const switchFields = [
    'supplyFlag', 'prePayFlag', 'reExamStatus', 'isPregnancyPrep'
  ];
  return switchFields.includes(fieldKey);
};

const isTextareaField = (fieldKey: string) => {
  const textareaFields = ['address', 'remark', 'reExamRemark'];
  return textareaFields.includes(fieldKey);
};

// 确认排序
const handleConfirm = () => {
  emit('confirm');
  visible.value = false;
};
</script>

<style scoped>
.sort-preview {
  max-height: 600px;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 20px;
  text-align: center;
}

.preview-header h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.preview-desc {
  margin: 0;
  color: #666;
  font-size: 13px;
}

.preview-content {
  margin-bottom: 20px;
}

.form-preview {
  border: 1px solid #e8e8e8;
  border-radius: 6px;
  padding: 16px;
  background: #fafafa;
}

.field-preview-item {
  margin-bottom: 16px;
}

.field-preview {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 12px;
  transition: all 0.2s;
}

.field-preview:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.field-preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.field-order-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: #fff;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 500;
}

.field-preview-name {
  font-weight: 500;
  color: #333;
}

.field-preview-component {
  width: 100%;
}

.preview-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}
</style>
