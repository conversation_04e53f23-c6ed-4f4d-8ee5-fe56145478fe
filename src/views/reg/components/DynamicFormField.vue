<template>
  <a-col :span="fieldMetadata.span || 12" v-if="shouldRender">
    <!-- 输入框 -->
    <a-form-item 
      v-if="fieldMetadata.type === FieldTypes.INPUT" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <a-input 
        v-model:value="modelValue[fieldMetadata.key]" 
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled" 
        size="middle" 
      />
    </a-form-item>

    <!-- 文本域 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.TEXTAREA" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <a-textarea 
        v-model:value="modelValue[fieldMetadata.key]" 
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled" 
        :rows="2"
      />
    </a-form-item>

    <!-- 数字输入框 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.NUMBER" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <a-input-number 
        v-model:value="modelValue[fieldMetadata.key]" 
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled" 
        size="middle" 
        style="width: 100%" 
      />
    </a-form-item>

    <!-- 开关 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.SWITCH" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <j-switch 
        v-model:value="modelValue[fieldMetadata.key]" 
        :options="[1, 0]" 
        :disabled="disabled" 
      />
    </a-form-item>

    <!-- 字典选择 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.DICT_SELECT" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <j-dict-select-tag
        v-model:value="modelValue[fieldMetadata.key]"
        :dictCode="fieldMetadata.dictCode"
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled"
        size="middle"
      />
    </a-form-item>

    <!-- 异步选择 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.ASYNC_SELECT" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <j-async-search-select
        v-model:value="modelValue[fieldMetadata.key]"
        :dict="fieldMetadata.dict"
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled"
        size="middle"
        @change="handleAsyncSelectChange"
      />
    </a-form-item>

    <!-- 级联选择 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.CASCADER" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <j-area-linkage
        v-model:value="modelValue[fieldMetadata.key]"
        type="cascader"
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled"
        size="middle"
        @change="handleCascaderChange"
      />
    </a-form-item>

    <!-- 搜索输入框 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.SEARCH" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
      :extra="searchExtra"
    >
      <a-input-search
        v-model:value="modelValue[fieldMetadata.key]"
        :placeholder="fieldMetadata.placeholder"
        @search="handleSearch"
        :loading="searchLoading"
      />
    </a-form-item>

    <!-- 普通选择框 -->
    <a-form-item 
      v-else-if="fieldMetadata.type === FieldTypes.SELECT" 
      :label="fieldMetadata.name" 
      v-bind="getValidateInfo()"
    >
      <a-select 
        v-model:value="modelValue[fieldMetadata.key]" 
        :placeholder="fieldMetadata.placeholder"
        :disabled="disabled"
        size="middle" 
        :allow-clear="true"
      >
        <a-select-option 
          v-for="option in selectOptions" 
          :key="option.value" 
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </a-form-item>

    <!-- 未知类型的字段 -->
    <a-form-item v-else :label="fieldMetadata.name">
      <a-input 
        v-model:value="modelValue[fieldMetadata.key]" 
        placeholder="未知字段类型"
        disabled 
      />
    </a-form-item>
  </a-col>
</template>

<script lang="ts" setup>
import { computed, ref, watch } from 'vue';
import { 
  FieldMetadata, 
  FieldTypes, 
  FieldKeys, 
  FieldDisplayLocation 
} from '../config/FieldDefinitions';

interface Props {
  fieldMetadata: FieldMetadata;
  modelValue: Record<string, any>;
  disabled?: boolean;
  validateInfos?: Record<string, any>;
  displayLocation: FieldDisplayLocation;
  currentLocation: FieldDisplayLocation;
  selectOptions?: Array<{ label: string; value: any }>;
  searchExtra?: string;
  searchLoading?: boolean;
}

interface Emits {
  (e: 'update:modelValue', value: Record<string, any>): void;
  (e: 'change', fieldKey: FieldKeys, value: any, option?: any): void;
  (e: 'search', fieldKey: FieldKeys, value: string): void;
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  selectOptions: () => [],
  searchExtra: '',
  searchLoading: false,
});

const emit = defineEmits<Emits>();

// 计算是否应该渲染该字段
const shouldRender = computed(() => {
  // 只有当字段的显示位置与当前位置匹配时才渲染
  return props.displayLocation === props.currentLocation;
});

// 获取验证信息
const getValidateInfo = () => {
  if (!props.validateInfos) {
    return {};
  }
  return props.validateInfos[props.fieldMetadata.key] || {};
};

// 处理异步选择变化
const handleAsyncSelectChange = (value: any, option: any) => {
  emit('change', props.fieldMetadata.key, value, option);
};

// 处理级联选择变化
const handleCascaderChange = (value: any, selectedOptions: any[]) => {
  emit('change', props.fieldMetadata.key, value, selectedOptions);
};

// 处理搜索
const handleSearch = (value: string) => {
  emit('search', props.fieldMetadata.key, value);
};

// 监听模型值变化
watch(
  () => props.modelValue,
  (newValue) => {
    emit('update:modelValue', newValue);
  },
  { deep: true }
);
</script>

<style scoped>
/* 可以添加一些特定的样式 */
</style>
