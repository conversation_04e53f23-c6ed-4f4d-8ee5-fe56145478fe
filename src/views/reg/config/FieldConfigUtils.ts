/**
 * 字段配置工具类
 * 提供字段配置的各种工具方法
 */

import {
  FieldKeys,
  FieldDisplayLocation,
  FieldGroups,
  FieldMetadata,
  FieldDisplayConfig,
  FormDisplayConfig,
  CUSTOMER_REG_FIELD_METADATA,
} from './FieldDefinitions';

/**
 * 表单类型枚举
 */
export enum FormTypes {
  CUSTOMER_REG = 'customer_reg',
  COMPANY_REG = 'company_reg',
  EXAM_CONFIG = 'exam_config',
}

/**
 * 字段配置管理器
 */
export class FieldConfigManager {
  private fieldMetadata: Record<FieldKeys, FieldMetadata>;
  private formType: FormTypes;
  private cache: Map<string, any> = new Map();

  constructor(formType: FormTypes = FormTypes.CUSTOMER_REG) {
    this.formType = formType;
    this.fieldMetadata = CUSTOMER_REG_FIELD_METADATA;
  }

  /**
   * 清除缓存
   */
  private clearCache() {
    this.cache.clear();
  }

  /**
   * 生成缓存键
   */
  private getCacheKey(method: string, config: FieldDisplayConfig[]): string {
    const configHash = JSON.stringify(config.map(f => ({ key: f.fieldKey, loc: f.displayLocation })));
    return `${method}_${configHash}`;
  }

  /**
   * 获取所有字段元数据
   */
  getAllFieldMetadata(): Record<FieldKeys, FieldMetadata> {
    return this.fieldMetadata;
  }

  /**
   * 获取指定字段的元数据
   */
  getFieldMetadata(fieldKey: FieldKeys): FieldMetadata | undefined {
    return this.fieldMetadata[fieldKey];
  }

  /**
   * 获取指定分组的字段
   */
  getFieldsByGroup(group: FieldGroups): FieldMetadata[] {
    return Object.values(this.fieldMetadata).filter(field => field.group === group);
  }

  /**
   * 获取指定显示位置的字段
   */
  getFieldsByLocation(location: FieldDisplayLocation): FieldMetadata[] {
    return Object.values(this.fieldMetadata).filter(field => field.defaultLocation === location);
  }

  /**
   * 创建默认字段显示配置
   */
  createDefaultFieldDisplayConfig(): FieldDisplayConfig[] {
    return Object.values(this.fieldMetadata).map(field => ({
      fieldKey: field.key,
      fieldName: field.name,
      displayLocation: field.defaultLocation,
      groupName: field.group,
      sortOrder: 0,
    }));
  }

  /**
   * 创建默认表单显示配置
   */
  createDefaultFormDisplayConfig(configName: string, centerId: string, centerName: string): FormDisplayConfig {
    return {
      configName,
      centerId,
      centerName,
      formType: this.formType,
      isActive: true,
      fields: this.createDefaultFieldDisplayConfig(),
    };
  }

  /**
   * 验证字段配置的有效性
   */
  validateFieldConfig(config: FieldDisplayConfig[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const fieldKeys = Object.values(FieldKeys);

    config.forEach(fieldConfig => {
      // 检查字段键是否有效
      if (!fieldKeys.includes(fieldConfig.fieldKey)) {
        errors.push(`无效的字段键: ${fieldConfig.fieldKey}`);
      }

      // 检查显示位置是否有效
      if (!Object.values(FieldDisplayLocation).includes(fieldConfig.displayLocation)) {
        errors.push(`字段 ${fieldConfig.fieldKey} 的显示位置无效: ${fieldConfig.displayLocation}`);
      }

      // 检查分组是否有效
      if (!Object.values(FieldGroups).includes(fieldConfig.groupName)) {
        errors.push(`字段 ${fieldConfig.fieldKey} 的分组无效: ${fieldConfig.groupName}`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 将旧版本配置转换为新版本配置
   */
  migrateOldConfig(oldConfig: any[]): FieldDisplayConfig[] {
    return oldConfig.map(field => {
      // 确定显示位置
      let displayLocation = field.displayLocation;

      // 如果没有新版本的 displayLocation，根据旧版本的 isVisible 推断
      if (!displayLocation) {
        if (field.isVisible === true) {
          displayLocation = FieldDisplayLocation.COLLAPSE;
        } else if (field.isVisible === false) {
          displayLocation = FieldDisplayLocation.OUTSIDE;
        } else {
          // 如果都没有，使用字段元数据的默认位置
          const metadata = this.getFieldMetadata(field.fieldKey);
          displayLocation = metadata?.defaultLocation || FieldDisplayLocation.OUTSIDE;
        }
      }

      return {
        fieldKey: field.fieldKey as FieldKeys,
        fieldName: field.fieldName || field.name || field.fieldKey,
        displayLocation,
        groupName: field.groupName as FieldGroups,
        sortOrder: field.sortOrder || 0,
        isRequired: field.isRequired || false,
        fieldDescription: field.fieldDescription || '',
        id: field.id,
        configId: field.configId,
      };
    });
  }

  /**
   * 获取外部显示的字段
   */
  getOutsideFields(config: FieldDisplayConfig[]): FieldDisplayConfig[] {
    const cacheKey = this.getCacheKey('outsideFields', config);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const result = config.filter(field => field.displayLocation === FieldDisplayLocation.OUTSIDE);
    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * 获取折叠面板中显示的字段
   */
  getCollapseFields(config: FieldDisplayConfig[]): FieldDisplayConfig[] {
    const cacheKey = this.getCacheKey('collapseFields', config);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const result = config.filter(field => field.displayLocation === FieldDisplayLocation.COLLAPSE);
    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * 获取隐藏的字段
   */
  getHiddenFields(config: FieldDisplayConfig[]): FieldDisplayConfig[] {
    const cacheKey = this.getCacheKey('hiddenFields', config);
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    const result = config.filter(field => field.displayLocation === FieldDisplayLocation.HIDDEN);
    this.cache.set(cacheKey, result);
    return result;
  }

  /**
   * 检查字段是否在指定位置显示
   */
  isFieldInLocation(fieldKey: FieldKeys, location: FieldDisplayLocation, config: FieldDisplayConfig[]): boolean {
    const fieldConfig = config.find(field => field.fieldKey === fieldKey);
    if (!fieldConfig) {
      // 如果没有配置，使用默认位置
      const metadata = this.getFieldMetadata(fieldKey);
      return metadata?.defaultLocation === location;
    }

    return fieldConfig.displayLocation === location;
  }

  /**
   * 更新字段显示位置
   */
  updateFieldLocation(fieldKey: FieldKeys, location: FieldDisplayLocation, config: FieldDisplayConfig[]): FieldDisplayConfig[] {
    // 清除缓存，因为配置发生了变化
    this.clearCache();

    const updatedConfig = [...config];
    const fieldIndex = updatedConfig.findIndex(field => field.fieldKey === fieldKey);

    if (fieldIndex >= 0) {
      updatedConfig[fieldIndex] = {
        ...updatedConfig[fieldIndex],
        displayLocation: location,
      };
    } else {
      // 如果字段不存在，添加新配置
      const metadata = this.getFieldMetadata(fieldKey);
      if (metadata) {
        updatedConfig.push({
          fieldKey,
          fieldName: metadata.name,
          displayLocation: location,
          groupName: metadata.group,
          sortOrder: 0,
        });
      }
    }

    return updatedConfig;
  }

  /**
   * 按分组排序字段
   */
  sortFieldsByGroup(config: FieldDisplayConfig[]): FieldDisplayConfig[] {
    const groupOrder = Object.values(FieldGroups);
    
    return [...config].sort((a, b) => {
      const groupIndexA = groupOrder.indexOf(a.groupName);
      const groupIndexB = groupOrder.indexOf(b.groupName);
      
      if (groupIndexA !== groupIndexB) {
        return groupIndexA - groupIndexB;
      }
      
      // 同一分组内按排序号排序
      return (a.sortOrder || 0) - (b.sortOrder || 0);
    });
  }

  /**
   * 获取分组统计信息
   */
  getGroupStatistics(config: FieldDisplayConfig[]): Record<FieldGroups, { total: number; outside: number; collapse: number; hidden: number }> {
    const stats = {} as Record<FieldGroups, { total: number; outside: number; collapse: number; hidden: number }>;
    
    // 初始化统计
    Object.values(FieldGroups).forEach(group => {
      stats[group] = { total: 0, outside: 0, collapse: 0, hidden: 0 };
    });

    // 统计各分组字段数量
    config.forEach(field => {
      const group = field.groupName;
      stats[group].total++;
      
      switch (field.displayLocation) {
        case FieldDisplayLocation.OUTSIDE:
          stats[group].outside++;
          break;
        case FieldDisplayLocation.COLLAPSE:
          stats[group].collapse++;
          break;
        case FieldDisplayLocation.HIDDEN:
          stats[group].hidden++;
          break;
        default:
          // 未知位置，默认为外部显示
          stats[group].outside++;
      }
    });

    return stats;
  }
}

/**
 * 创建字段配置管理器实例
 */
export function createFieldConfigManager(formType: FormTypes = FormTypes.CUSTOMER_REG): FieldConfigManager {
  return new FieldConfigManager(formType);
}

/**
 * 默认的客户登记表单配置管理器
 */
export const customerRegFieldManager = createFieldConfigManager(FormTypes.CUSTOMER_REG);
