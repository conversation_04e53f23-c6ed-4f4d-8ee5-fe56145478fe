<template>
  <div class="display-location-demo">
    <a-card title="字段显示位置配置演示" size="small">
      <!-- 配置说明 -->
      <a-alert 
        message="字段显示位置配置功能" 
        description="现在可以为每个字段配置三种显示位置：外部显示、折叠面板内显示、隐藏。点击下方的配置按钮来体验新功能。"
        type="info" 
        show-icon 
        style="margin-bottom: 16px"
      />

      <!-- 操作按钮 -->
      <div class="demo-controls">
        <a-space>
          <a-button type="primary" @click="openConfigModal">
            <Icon icon="ant-design:setting-outlined" />
            打开字段配置
          </a-button>
          <a-button @click="resetToDefault">
            <Icon icon="ant-design:reload-outlined" />
            重置为默认配置
          </a-button>
          <a-button @click="showCurrentConfig">
            <Icon icon="ant-design:eye-outlined" />
            查看当前配置
          </a-button>
        </a-space>
      </div>

      <!-- 配置统计 -->
      <div class="config-stats">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-statistic 
              title="外部显示字段" 
              :value="outsideFields.length" 
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <Icon icon="ant-design:eye-outlined" />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="8">
            <a-statistic 
              title="折叠面板内字段" 
              :value="collapseFields.length" 
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <Icon icon="ant-design:folder-outlined" />
              </template>
            </a-statistic>
          </a-col>
          <a-col :span="8">
            <a-statistic 
              title="隐藏字段" 
              :value="hiddenFields.length" 
              :value-style="{ color: '#ff4d4f' }"
            >
              <template #prefix>
                <Icon icon="ant-design:eye-invisible-outlined" />
              </template>
            </a-statistic>
          </a-col>
        </a-row>
      </div>

      <!-- 字段列表展示 -->
      <a-tabs v-model:activeKey="activeTab" style="margin-top: 16px">
        <a-tab-pane key="outside" tab="外部显示字段">
          <a-list size="small" :data-source="outsideFields">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a-tag color="green">{{ item.fieldName }}</a-tag>
                  </template>
                  <template #description>
                    字段键: {{ item.fieldKey }} | 分组: {{ getGroupDisplayName(item.groupName) }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-tab-pane>

        <a-tab-pane key="collapse" tab="折叠面板内字段">
          <a-list size="small" :data-source="collapseFields">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a-tag color="blue">{{ item.fieldName }}</a-tag>
                  </template>
                  <template #description>
                    字段键: {{ item.fieldKey }} | 分组: {{ getGroupDisplayName(item.groupName) }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-tab-pane>

        <a-tab-pane key="hidden" tab="隐藏字段">
          <a-list size="small" :data-source="hiddenFields">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta>
                  <template #title>
                    <a-tag color="red">{{ item.fieldName }}</a-tag>
                  </template>
                  <template #description>
                    字段键: {{ item.fieldKey }} | 分组: {{ getGroupDisplayName(item.groupName) }}
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <!-- 实际表单演示 -->
    <a-card title="表单效果演示" size="small" style="margin-top: 16px">
      <CustomerRegFormOfPannel 
        :disabled="false"
        @submit="handleFormSubmit"
        @reset="handleFormReset"
      />
    </a-card>

    <!-- 字段配置弹窗 -->
    <FormFieldConfigModal ref="configModalRef" @success="handleConfigSuccess" />

    <!-- 配置详情弹窗 -->
    <a-modal v-model:open="configDetailVisible" title="当前字段配置详情" width="800px">
      <pre>{{ JSON.stringify(fieldConfig, null, 2) }}</pre>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { Icon } from '/@/components/Icon';
import CustomerRegFormOfPannel from './components/CustomerRegFormOfPannel.vue';
import FormFieldConfigModal from './components/FormFieldConfigModal.vue';
import { 
  FieldGroups,
  FieldDisplayLocation,
} from './config/FieldDefinitions';
import { FormTypes } from './config/FieldConfigUtils';
import { useFieldConfig } from './composables/useFieldConfig';

// 字段配置管理
const {
  isLoading: configLoading,
  fieldConfig,
  loadError,
  outsideFields,
  collapseFields,
  hiddenFields,
  loadFieldConfig,
  resetToDefault,
} = useFieldConfig({
  formType: FormTypes.CUSTOMER_REG,
  enableCache: true,
  fallbackToDefault: true,
});

// 状态管理
const activeTab = ref('outside');
const configModalRef = ref();
const configDetailVisible = ref(false);

// 获取分组显示名称
const getGroupDisplayName = (groupName: string) => {
  const groupMap = {
    [FieldGroups.BASIC_INFO]: '基础信息',
    [FieldGroups.ADDRESS_INFO]: '地址信息',
    [FieldGroups.CONTACT_INFO]: '联系信息',
    [FieldGroups.HEALTH_INFO]: '健康信息',
    [FieldGroups.CERTIFICATE_INFO]: '证件信息',
    [FieldGroups.WORK_INFO]: '工作信息',
    [FieldGroups.EXAM_INFO]: '体检信息',
    [FieldGroups.OTHER_INFO]: '其他信息',
  };
  return groupMap[groupName] || groupName;
};

// 打开配置弹窗
const openConfigModal = () => {
  configModalRef.value?.open();
};

// 显示当前配置
const showCurrentConfig = () => {
  configDetailVisible.value = true;
};

// 配置成功回调
const handleConfigSuccess = async () => {
  message.success('字段配置已更新');
  await loadFieldConfig();
};

// 处理表单提交
const handleFormSubmit = (data: Record<string, any>) => {
  console.log('表单提交数据:', data);
  message.success('表单提交成功');
};

// 处理表单重置
const handleFormReset = () => {
  console.log('表单已重置');
  message.info('表单已重置');
};

// 组件挂载时加载配置
onMounted(async () => {
  await loadFieldConfig();
  
  if (loadError.value) {
    message.warning(`字段配置加载失败: ${loadError.value}`);
  }
});
</script>

<style scoped>
.display-location-demo {
  padding: 16px;
}

.demo-controls {
  margin-bottom: 16px;
}

.config-stats {
  margin-top: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}
</style>
