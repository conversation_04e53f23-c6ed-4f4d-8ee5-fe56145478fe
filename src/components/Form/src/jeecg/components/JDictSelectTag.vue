<template>
  <a-radio-group v-if="compType === CompTypeEnum.Radio" v-bind="attrs" v-model:value="state" @change="handleChangeRadio">
    <template v-for="item in dictOptions" :key="`${item.value}`">
      <a-radio :value="item.value">
        <span :class="[useDicColor && item.color ? 'colorText' : '']" :style="{ backgroundColor: `${useDicColor && item.color}` }">
          {{ item.label }}
          <span v-if="showHelpChar && item.helpChar" class="help-char-text">[{{ item.helpChar }}]</span>
        </span>
      </a-radio>
    </template>
  </a-radio-group>

  <a-radio-group
    v-else-if="compType === CompTypeEnum.RadioButton"
    v-bind="attrs"
    v-model:value="state"
    buttonStyle="solid"
    @change="handleChangeRadio"
  >
    <template v-for="item in dictOptions" :key="`${item.value}`">
      <a-radio-button :value="item.value">
        {{ item.label }}
        <span v-if="showHelpChar && item.helpChar" class="help-char-text">[{{ item.helpChar }}]</span>
      </a-radio-button>
    </template>
  </a-radio-group>

  <template v-else-if="compType === CompTypeEnum.Select">
    <!-- 显示加载效果 -->
    <a-input v-if="loadingEcho" readOnly placeholder="加载中…">
      <template #prefix>
        <LoadingOutlined />
      </template>
    </a-input>
    <a-select
      v-else
      :placeholder="computedPlaceholder"
      v-bind="attrs"
      show-search
      v-model:value="state"
      :filterOption="handleFilterOption"
      :getPopupContainer="getPopupContainer"
      :style="style"
      @change="handleChange"
    >
      <a-select-option v-if="showChooseOption" :value="null">请选择…</a-select-option>
      <template v-for="item in dictOptions" :key="`${item.value}`">
        <a-select-option :value="item.value" :helpChar="item.helpChar">
          <span
            :class="[useDicColor && item.color ? 'colorText' : '']"
            :style="{ backgroundColor: `${useDicColor && item.color}` }"
            :title="getOptionTitle(item)"
          >
            {{ item.label }}
            <span v-if="showHelpChar && item.helpChar" class="help-char-text">[{{ item.helpChar }}]</span>
          </span>
        </a-select-option>
      </template>
    </a-select>
  </template>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, reactive, watchEffect, computed, unref, watch, onMounted, nextTick } from 'vue';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { initDictOptions } from '/@/utils/dict';
  import { get, omit } from 'lodash-es';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { CompTypeEnum } from '/@/enums/CompTypeEnum';
  import { LoadingOutlined } from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'JDictSelectTag',
    components: { LoadingOutlined },
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.string, propTypes.number, propTypes.array]),
      dictCode: propTypes.string,
      type: propTypes.string,
      placeholder: propTypes.string,
      stringToNumber: propTypes.bool,
      useDicColor: propTypes.bool.def(false),
      getPopupContainer: {
        type: Function,
        default: (node) => node?.parentNode,
      },
      // 是否显示【请选择】选项
      showChooseOption: propTypes.bool.def(true),
      // 下拉项-online使用
      options: {
        type: Array,
        default: [],
        required: false,
      },
      style: propTypes.any,

      // 新增：助记码相关属性
      helpCharField: propTypes.string.def('helpChar'), // 助记码字段名，默认为helpChar
      enableHelpCharSearch: propTypes.bool.def(undefined), // 是否启用助记码搜索，默认undefined，由组件智能判断
      searchType: propTypes.oneOf(['text', 'helpChar', 'both']).def('both'), // 搜索类型
      showHelpChar: propTypes.bool.def(true), // 是否显示助记码
      helpCharPlaceholder: propTypes.string, // 助记码搜索提示文本
    },
    emits: ['options-change', 'change', 'update:value'],
    setup(props, { emit, refs }) {
      const dictOptions = ref<any[]>([]);
      const attrs = useAttrs();
      const [state, , , formItemContext] = useRuleFormItem(props, 'value', 'change');
      const getBindValue = Object.assign({}, unref(props), unref(attrs));
      // 是否正在加载回显数据
      const loadingEcho = ref<boolean>(false);
      // 是否是首次加载回显，只有首次加载，才会显示 loading
      let isFirstLoadEcho = true;

      //组件类型
      const compType = computed(() => {
        return !props.type || props.type === 'list' ? 'select' : props.type;
      });

      // 智能判断是否启用助记码搜索
      const shouldEnableHelpCharSearch = computed(() => {
        // 如果明确设置了enableHelpCharSearch，则使用设置的值
        if (props.enableHelpCharSearch !== undefined) {
          return props.enableHelpCharSearch;
        }

        // 智能判断：如果是系统字典表（不包含逗号），则默认启用助记码搜索
        if (props.dictCode && props.dictCode.indexOf(',') < 0) {
          return true;
        }
        return false;
      });

      // 计算占位符文本
      const computedPlaceholder = computed(() => {
        if (props.placeholder) {
          return props.placeholder;
        }
        if (shouldEnableHelpCharSearch.value && props.helpCharPlaceholder) {
          return props.helpCharPlaceholder;
        }
        if (shouldEnableHelpCharSearch.value) {
          const searchTypeText = {
            text: '请输入名称搜索',
            helpChar: '请输入助记码搜索',
            both: '请输入名称或助记码搜索',
          };
          return searchTypeText[props.searchType] || '请选择';
        }
        return '请选择';
      });
      /**
       * 监听字典code
       */
      watchEffect(() => {
        if (props.dictCode) {
          loadingEcho.value = isFirstLoadEcho;
          isFirstLoadEcho = false;
          initDictData().finally(() => {
            loadingEcho.value = isFirstLoadEcho;
          });
        }
        //update-begin-author:taoyan date: 如果没有提供dictCode 可以走options的配置--
        if (!props.dictCode) {
          dictOptions.value = props.options;
        }
        //update-end-author:taoyan date: 如果没有提供dictCode 可以走options的配置--
      });

      //update-begin-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件
      // 此处添加空值的change事件,即当组件调用地代码设置value为''也能触发change事件
      watch(
        () => props.value,
        () => {
          if (props.value === '') {
            emit('change', '');
            nextTick(() => formItemContext.onFieldChange());
          }
        }
      );
      //update-end-author:taoyan date:20220404 for: 使用useRuleFormItem定义的value，会有一个问题，如果不是操作设置的值而是代码设置的控件值而不能触发change事件

      async function initDictData() {
        let { dictCode, stringToNumber, helpCharField, enableHelpCharSearch, searchType } = props;

        try {
          // 构建请求参数
          const requestParams = {};

          // 如果启用助记码功能，添加相应参数
          if (shouldEnableHelpCharSearch.value) {
            if (dictCode.indexOf(',') < 0) {
              // 对于系统字典表，传递searchType参数以启用助记码功能
              requestParams.searchType = searchType || 'both';
            } else {
              // 对于表字典，添加helpCharField参数
              if (helpCharField) {
                requestParams.helpCharField = helpCharField;
                requestParams.searchType = searchType || 'both';
                //console.log('🔍 表字典启用助记码:', dictCode, requestParams);
              }
            }
          } else {
            //console.log('❌ 助记码功能未启用:', dictCode);
          }

          //根据字典Code, 初始化字典数组
          const dictData = await initDictOptions(dictCode, requestParams);
          dictOptions.value = dictData.reduce((prev, next) => {
            if (next) {
              const value = next['value'];
              const item = {
                label: next['text'] || next['label'],
                value: stringToNumber ? +value : value,
                color: next['color'],
                helpChar: next[helpCharField] || next['helpChar'], // 支持助记码字段
                ...omit(next, ['text', 'value', 'color', 'helpChar', helpCharField]),
              };
              prev.push(item);
            }
            return prev;
          }, []);
        } catch (error) {
          console.warn('助记码字段查询失败，回退到基础查询:', error);
          // 回退到原有逻辑
          const dictData = await initDictOptions(dictCode);
          dictOptions.value = dictData.reduce((prev, next) => {
            if (next) {
              const value = next['value'];
              prev.push({
                label: next['text'] || next['label'],
                value: stringToNumber ? +value : value,
                color: next['color'],
                ...omit(next, ['text', 'value', 'color']),
              });
            }
            return prev;
          }, []);
        }
      }

      function handleChange(e) {
        const { mode } = unref<Recordable>(getBindValue);
        let changeValue: any;
        // 兼容多选模式

        //update-begin---author:wangshuai ---date:20230216  for：[QQYUN-4290]公文发文：选择机关代字报错,是因为值改变触发了change事件三次，导致数据发生改变------------
        //采用一个值，不然的话state值变换触发多个change
        if (mode === 'multiple') {
          changeValue = e?.target?.value ?? e;
          // 过滤掉空值
          if (changeValue == null || changeValue === '') {
            changeValue = [];
          }
          if (Array.isArray(changeValue)) {
            changeValue = changeValue.filter((item) => item != null && item !== '');
          }
        } else {
          changeValue = e?.target?.value ?? e;
        }
        state.value = changeValue;

        //update-begin---author:wangshuai ---date:20230403  for：【issues/4507】JDictSelectTag组件使用时，浏览器给出警告提示：Expected Function, got Array------------
        emit('update:value', changeValue);
        //update-end---author:wangshuai ---date:20230403  for：【issues/4507】JDictSelectTag组件使用时，浏览器给出警告提示：Expected Function, got Array述------------
        //update-end---author:wangshuai ---date:20230216  for：[QQYUN-4290]公文发文：选择机关代字报错,是因为值改变触发了change事件三次，导致数据发生改变------------

        // nextTick(() => formItemContext.onFieldChange());
      }

      /** 单选radio的值变化事件 */
      function handleChangeRadio(e) {
        state.value = e?.target?.value ?? e;
        //update-begin---author:wangshuai ---date:20230504  for：【issues/506】JDictSelectTag 组件 type="radio" 没有返回值------------
        emit('update:value', e?.target?.value ?? e);
        //update-end---author:wangshuai ---date:20230504  for：【issues/506】JDictSelectTag 组件 type="radio" 没有返回值------------
      }

      /** 用于搜索下拉框中的内容 */
      /*    function handleFilterOption(input, option) {
        // update-begin--author:liaozhiyang---date:20230914---for：【QQYUN-6514】 配置的时候，Y轴不能输入多个字段了，控制台报错
        if (typeof option.children === 'function') {
          // 在 label 中搜索
          let labelIf = option.children()[0]?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
          if (labelIf) {
            return true;
          }
        }
        // update-end--author:liaozhiyang---date:20230914---for：【QQYUN-6514】 配置的时候，Y轴不能输入多个字段了，控制台报错
        // 在 value 中搜索
        return (option.value || '').toString().toLowerCase().indexOf(input.toLowerCase()) >= 0;
      }*/

      function handleFilterOption(input, option) {
        const inputLower = input.toLowerCase();
        const optionLabel = typeof option.children === 'function' ? option.children()[0]?.children : option.children;
        const optionValue = option.value || '';

        // 安全地获取标签文本
        const labelText = optionLabel ? (typeof optionLabel === 'string' ? optionLabel : String(optionLabel)) : '';
        const valueText = optionValue.toString();

        // 基础匹配
        const labelMatch = labelText.toLowerCase().includes(inputLower);
        const valueMatch = valueText.toLowerCase().includes(inputLower);

        // 助记码匹配
        let helpCharMatch = false;
        if (props.enableHelpCharSearch && option.helpChar) {
          const helpCharText = typeof option.helpChar === 'string' ? option.helpChar : String(option.helpChar);
          helpCharMatch = helpCharText.toLowerCase().includes(inputLower);
        }

        // 根据搜索类型决定匹配策略
        if (props.enableHelpCharSearch) {
          switch (props.searchType) {
            case 'text':
              return labelMatch || valueMatch;
            case 'helpChar':
              return helpCharMatch;
            case 'both':
            default:
              return labelMatch || valueMatch || helpCharMatch;
          }
        }

        return labelMatch || valueMatch;
      }

      /** 获取选项的title提示文本 */
      function getOptionTitle(item) {
        let title = item.label;
        if (props.showHelpChar && item.helpChar) {
          title += ` [${item.helpChar}]`;
        }
        return title;
      }

      return {
        state,
        compType,
        attrs,
        loadingEcho,
        getBindValue,
        dictOptions,
        CompTypeEnum,
        computedPlaceholder,
        handleChange,
        handleChangeRadio,
        handleFilterOption,
        getOptionTitle,
      };
    },
  });
</script>
<style scoped lang="less">
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
  .colorText {
    display: inline-block;
    height: 20px;
    line-height: 20px;
    padding: 0 6px;
    border-radius: 8px;
    background-color: red;
    color: #fff;
    font-size: 12px;
  }

  // 助记码样式
  .help-char-text {
    color: #999;
    font-size: 12px;
    margin-left: 4px;
  }
  // update-begin--author:liaozhiyang---date:20230110---for：【QQYUN-7799】字典组件（原生组件除外）加上颜色配置
</style>
