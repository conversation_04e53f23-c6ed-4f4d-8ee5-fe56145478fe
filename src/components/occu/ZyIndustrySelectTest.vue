<template>
  <div class="test-container">
    <a-card title="ZyIndustrySelect 组件测试" class="test-card">
      <div class="test-section">
        <h4>功能测试</h4>
        
        <!-- 基本功能测试 -->
        <div class="test-item">
          <label>基本选择测试：</label>
          <ZyIndustrySelect
            v-model:value="testValue1"
            placeholder="测试基本功能"
            style="width: 300px"
            @change="handleTestChange1"
          />
          <span class="test-result">当前值: {{ testValue1 || '未选择' }}</span>
        </div>

        <!-- 搜索功能测试 -->
        <div class="test-item">
          <label>搜索功能测试：</label>
          <ZyIndustrySelect
            v-model:value="testValue2"
            placeholder="输入关键词搜索"
            style="width: 300px"
            @change="handleTestChange2"
          />
          <span class="test-result">搜索值: {{ testValue2 || '未选择' }}</span>
        </div>

        <!-- 禁用状态测试 -->
        <div class="test-item">
          <label>禁用状态测试：</label>
          <ZyIndustrySelect
            v-model:value="testValue3"
            placeholder="禁用状态"
            disabled
            style="width: 300px"
          />
          <a-button @click="toggleDisabled" size="small" style="margin-left: 8px">
            {{ isDisabled ? '启用' : '禁用' }}
          </a-button>
        </div>

        <!-- 方法调用测试 -->
        <div class="test-item">
          <label>方法调用测试：</label>
          <ZyIndustrySelect
            ref="testSelectRef"
            v-model:value="testValue4"
            placeholder="方法测试"
            style="width: 300px"
          />
          <a-button @click="testRefresh" size="small" style="margin-left: 8px">
            刷新数据
          </a-button>
        </div>
      </div>

      <a-divider />

      <div class="test-section">
        <h4>测试结果</h4>
        <div class="test-results">
          <p><strong>测试1结果：</strong>{{ testResult1 }}</p>
          <p><strong>测试2结果：</strong>{{ testResult2 }}</p>
          <p><strong>组件状态：</strong>{{ componentStatus }}</p>
          <p><strong>最后操作：</strong>{{ lastAction }}</p>
        </div>
      </div>

      <a-divider />

      <div class="test-section">
        <h4>测试日志</h4>
        <div class="test-logs">
          <div
            v-for="(log, index) in testLogs"
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <a-button @click="clearLogs" size="small" style="margin-top: 8px">
          清空日志
        </a-button>
      </div>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue';
import { useMessage } from '/@/hooks/web/useMessage';
import ZyIndustrySelect from './ZyIndustrySelect.vue';

const { createMessage } = useMessage();

// 测试数据
const testValue1 = ref<string>();
const testValue2 = ref<string>();
const testValue3 = ref<string>();
const testValue4 = ref<string>();

const isDisabled = ref(false);
const testSelectRef = ref();

// 测试结果
const testResult1 = ref('等待测试');
const testResult2 = ref('等待测试');
const componentStatus = ref('正常');
const lastAction = ref('无');

// 测试日志
const testLogs = ref<Array<{time: string, message: string, type: string}>>([]);

// 添加日志
const addLog = (message: string, type = 'info') => {
  const now = new Date();
  const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
  
  testLogs.value.unshift({
    time,
    message,
    type
  });
  
  // 限制日志数量
  if (testLogs.value.length > 20) {
    testLogs.value = testLogs.value.slice(0, 20);
  }
};

// 事件处理
const handleTestChange1 = (value: string | undefined, option?: any) => {
  testResult1.value = value ? `选择成功: ${value}` : '已清空选择';
  lastAction.value = '基本选择测试';
  addLog(`基本选择测试: ${value || '清空'}`, 'success');
  console.log('测试1变化:', value, option);
};

const handleTestChange2 = (value: string | undefined, option?: any) => {
  testResult2.value = value ? `搜索选择成功: ${value}` : '已清空搜索选择';
  lastAction.value = '搜索功能测试';
  addLog(`搜索功能测试: ${value || '清空'}`, 'success');
  console.log('测试2变化:', value, option);
};

const toggleDisabled = () => {
  isDisabled.value = !isDisabled.value;
  lastAction.value = `${isDisabled.value ? '禁用' : '启用'}组件`;
  addLog(`组件状态切换: ${isDisabled.value ? '禁用' : '启用'}`, 'warning');
};

const testRefresh = () => {
  try {
    if (testSelectRef.value) {
      testSelectRef.value.refresh();
      lastAction.value = '刷新数据';
      addLog('数据刷新成功', 'success');
      createMessage.success('数据刷新成功');
    } else {
      addLog('组件引用获取失败', 'error');
      createMessage.error('组件引用获取失败');
    }
  } catch (error) {
    addLog(`刷新失败: ${error}`, 'error');
    createMessage.error('刷新失败');
  }
};

const clearLogs = () => {
  testLogs.value = [];
  addLog('日志已清空', 'info');
};

// 初始化日志
addLog('组件测试页面已加载', 'info');
</script>

<style lang="less" scoped>
.test-container {
  padding: 20px;
  max-width: 900px;
  margin: 0 auto;
}

.test-card {
  .ant-card-body {
    padding: 24px;
  }
}

.test-section {
  margin-bottom: 24px;
  
  h4 {
    color: #333;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
  }
}

.test-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
  
  label {
    min-width: 140px;
    font-weight: 500;
    color: #333;
  }
  
  .test-result {
    margin-left: 16px;
    padding: 4px 8px;
    background-color: #e6f7ff;
    border-radius: 4px;
    font-size: 12px;
    color: #1890ff;
    border: 1px solid #91d5ff;
  }
}

.test-results {
  background-color: #f6ffed;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
  
  p {
    margin-bottom: 8px;
    color: #333;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    strong {
      color: #52c41a;
    }
  }
}

.test-logs {
  max-height: 300px;
  overflow-y: auto;
  background-color: #001529;
  padding: 12px;
  border-radius: 6px;
  
  .log-item {
    display: flex;
    margin-bottom: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    
    &.info {
      color: #1890ff;
    }
    
    &.success {
      color: #52c41a;
    }
    
    &.warning {
      color: #faad14;
    }
    
    &.error {
      color: #ff4d4f;
    }
    
    .log-time {
      color: #666;
      margin-right: 8px;
      min-width: 60px;
    }
    
    .log-message {
      flex: 1;
    }
  }
}
</style>
