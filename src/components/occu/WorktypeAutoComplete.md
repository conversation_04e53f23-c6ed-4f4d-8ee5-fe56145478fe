# WorktypeAutoComplete 工种自动完成组件

## 概述

WorktypeAutoComplete 是一个基于 Ant Design Vue 的 a-auto-complete 组件开发的工种自动完成组件，支持名称和助记码匹配、自动创建新工种、使用频次统计等功能。

## 功能特性

- ✅ **智能搜索**：支持按工种名称和助记码进行模糊搜索
- ✅ **自动完成**：实时显示匹配的工种选项
- ✅ **自动创建**：当输入的工种不存在时，可自动创建新工种
- ✅ **使用频次**：显示工种使用频次，自动统计使用次数
- ✅ **热门推荐**：初始化时显示热门工种选项
- ✅ **缓存机制**：后端支持Redis缓存，提升查询性能
- ✅ **美观界面**：精美的下拉选项样式和动画效果

## 基本用法

### 简单使用

```vue
<template>
  <WorktypeAutoComplete
    v-model:value="worktypeName"
    placeholder="请输入工种名称"
    @select="handleSelect"
  />
</template>

<script setup>
import { ref } from 'vue';
import WorktypeAutoComplete from '@/components/occu/WorktypeAutoComplete.vue';

const worktypeName = ref('');

const handleSelect = (value, option) => {
  console.log('选择的工种:', value, option);
};
</script>
```

### 完整配置

```vue
<template>
  <WorktypeAutoComplete
    v-model:value="worktypeName"
    placeholder="请输入工种名称或助记码"
    :disabled="false"
    :limit="15"
    :allowAutoCreate="true"
    searchType="both"
    :dropdownMatchSelectWidth="true"
    @select="handleSelect"
    @change="handleChange"
    @create="handleCreate"
  />
</template>

<script setup>
import { ref } from 'vue';
import WorktypeAutoComplete from '@/components/occu/WorktypeAutoComplete.vue';

const worktypeName = ref('');

const handleSelect = (value, option) => {
  console.log('选择的工种:', value);
  if (option.isNew) {
    console.log('这是新创建的工种');
  }
};

const handleChange = (value) => {
  console.log('输入值变化:', value);
};

const handleCreate = (newWorktype) => {
  console.log('新创建的工种:', newWorktype);
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 绑定值 | string | '' |
| placeholder | 输入框占位符 | string | '请输入工种名称' |
| disabled | 是否禁用 | boolean | false |
| limit | 搜索结果数量限制 | number | 10 |
| dropdownMatchSelectWidth | 下拉菜单和选择器同宽 | boolean | true |
| allowAutoCreate | 是否允许自动创建新工种 | boolean | true |
| searchType | 搜索类型 | 'name' \| 'helpChar' \| 'both' | 'both' |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:value | 值变化时触发 | (value: string) |
| change | 输入框内容变化时触发 | (value: string) |
| select | 选择选项时触发 | (value: string, option: OptionItem) |
| create | 自动创建新工种时触发 | (worktype: any) |

### OptionItem 类型

```typescript
interface OptionItem {
  value: string;        // 工种名称
  label: string;        // 显示标签
  helpChar?: string;    // 助记码
  useCount?: number;    // 使用频次
  isNew?: boolean;      // 是否为新建选项
  raw?: any;           // 原始数据
}
```

## 搜索类型说明

- `name`: 仅按工种名称搜索
- `helpChar`: 仅按助记码搜索  
- `both`: 同时按名称和助记码搜索（推荐）

## 样式定制

组件使用了 scoped 样式，如需自定义样式，可以通过以下方式：

```vue
<style>
.worktype-auto-complete :deep(.ant-select-dropdown) {
  /* 自定义下拉菜单样式 */
}

.worktype-auto-complete .option-item {
  /* 自定义选项样式 */
}
</style>
```

## 注意事项

1. **后端依赖**：组件依赖后端提供的自动完成接口，确保以下接口已实现：
   - `/occu/zyWorktype/autoComplete` - 自动完成搜索
   - `/occu/zyWorktype/updateUseCount` - 更新使用频次
   - `/occu/zyWorktype/autoCreate` - 自动创建工种

2. **权限控制**：自动创建功能需要相应的权限，建议在生产环境中谨慎开启

3. **性能优化**：组件内置了防抖搜索（300ms），避免频繁请求

4. **缓存机制**：后端建议实现Redis缓存以提升查询性能

## 集成示例

### 在表单中使用

```vue
<template>
  <a-form :model="form" layout="vertical">
    <a-form-item label="工种名称" name="worktypeName">
      <WorktypeAutoComplete
        v-model:value="form.worktypeName"
        placeholder="请选择或输入工种名称"
        @select="handleWorktypeSelect"
      />
    </a-form-item>
  </a-form>
</template>
```

### 在表格中使用

```vue
<template>
  <a-table :columns="columns" :dataSource="dataSource">
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'worktype'">
        <WorktypeAutoComplete
          v-model:value="record.worktypeName"
          :allowAutoCreate="false"
          @select="(value) => updateRecord(record, value)"
        />
      </template>
    </template>
  </a-table>
</template>
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的自动完成功能
- 支持自动创建和使用频次统计
- 美观的UI设计和动画效果
