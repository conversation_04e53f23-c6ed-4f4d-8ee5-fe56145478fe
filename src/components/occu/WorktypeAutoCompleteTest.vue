<template>
  <div class="worktype-test-page">
    <a-card title="工种自动完成组件测试" style="margin: 20px;">
      <a-space direction="vertical" size="large" style="width: 100%;">
        
        <!-- 基本使用测试 -->
        <a-card size="small" title="基本使用测试">
          <a-form layout="vertical">
            <a-form-item label="选择工种">
              <WorktypeAutoComplete
                v-model:value="basicValue"
                placeholder="请输入工种名称"
                @select="handleBasicSelect"
                @change="handleBasicChange"
                @create="handleBasicCreate"
              />
            </a-form-item>
            <a-form-item label="当前值">
              <a-input :value="basicValue" readonly />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 高级配置测试 -->
        <a-card size="small" title="高级配置测试">
          <a-form layout="vertical">
            <a-form-item label="配置选项">
              <a-space>
                <a-checkbox v-model:checked="allowAutoCreate">允许自动创建</a-checkbox>
                <a-select v-model:value="searchType" style="width: 120px;">
                  <a-select-option value="both">名称+助记码</a-select-option>
                  <a-select-option value="name">仅名称</a-select-option>
                  <a-select-option value="helpChar">仅助记码</a-select-option>
                </a-select>
                <a-input-number v-model:value="limit" :min="5" :max="20" placeholder="结果数量" />
              </a-space>
            </a-form-item>
            <a-form-item label="选择工种">
              <WorktypeAutoComplete
                v-model:value="advancedValue"
                placeholder="请输入工种名称或助记码"
                :allowAutoCreate="allowAutoCreate"
                :searchType="searchType"
                :limit="limit"
                @select="handleAdvancedSelect"
                @change="handleAdvancedChange"
                @create="handleAdvancedCreate"
              />
            </a-form-item>
            <a-form-item label="当前值">
              <a-input :value="advancedValue" readonly />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 禁用状态测试 -->
        <a-card size="small" title="禁用状态测试">
          <a-form layout="vertical">
            <a-form-item label="禁用状态">
              <a-switch v-model:checked="disabled" />
            </a-form-item>
            <a-form-item label="选择工种">
              <WorktypeAutoComplete
                v-model:value="disabledValue"
                placeholder="禁用状态测试"
                :disabled="disabled"
              />
            </a-form-item>
          </a-form>
        </a-card>

        <!-- 事件日志 -->
        <a-card size="small" title="事件日志">
          <a-button @click="clearLogs" type="primary" size="small" style="margin-bottom: 10px;">
            清空日志
          </a-button>
          <div class="event-logs">
            <div v-for="(log, index) in eventLogs" :key="index" class="log-item">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type" :class="log.type">{{ log.type }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            <div v-if="!eventLogs.length" class="no-logs">暂无事件日志</div>
          </div>
        </a-card>

      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import WorktypeAutoComplete from './WorktypeAutoComplete.vue';

// 基本测试
const basicValue = ref('');

// 高级配置测试
const advancedValue = ref('');
const allowAutoCreate = ref(true);
const searchType = ref('both');
const limit = ref(10);

// 禁用状态测试
const disabledValue = ref('');
const disabled = ref(false);

// 事件日志
const eventLogs = ref<Array<{time: string, type: string, message: string}>>([]);

// 添加日志
const addLog = (type: string, message: string) => {
  const now = new Date();
  const time = now.toLocaleTimeString();
  eventLogs.value.unshift({ time, type, message });
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50);
  }
};

// 清空日志
const clearLogs = () => {
  eventLogs.value = [];
};

// 基本测试事件处理
const handleBasicSelect = (value: string, option: any) => {
  addLog('SELECT', `基本测试 - 选择: ${value}, 是否新建: ${option.isNew ? '是' : '否'}`);
};

const handleBasicChange = (value: string) => {
  addLog('CHANGE', `基本测试 - 输入变化: ${value}`);
};

const handleBasicCreate = (worktype: any) => {
  addLog('CREATE', `基本测试 - 创建工种: ${JSON.stringify(worktype)}`);
};

// 高级配置事件处理
const handleAdvancedSelect = (value: string, option: any) => {
  addLog('SELECT', `高级测试 - 选择: ${value}, 助记码: ${option.helpChar || '无'}, 使用次数: ${option.useCount || 0}`);
};

const handleAdvancedChange = (value: string) => {
  addLog('CHANGE', `高级测试 - 输入变化: ${value}`);
};

const handleAdvancedCreate = (worktype: any) => {
  addLog('CREATE', `高级测试 - 创建工种: ${JSON.stringify(worktype)}`);
};
</script>

<style lang="less" scoped>
.worktype-test-page {
  min-height: 100vh;
  background: #f0f2f5;
  
  .event-logs {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #d9d9d9;
    border-radius: 4px;
    padding: 8px;
    background: #fafafa;
    
    .log-item {
      display: flex;
      align-items: center;
      padding: 4px 0;
      border-bottom: 1px solid #f0f0f0;
      font-size: 12px;
      
      &:last-child {
        border-bottom: none;
      }
      
      .log-time {
        color: #999;
        margin-right: 8px;
        min-width: 80px;
      }
      
      .log-type {
        padding: 2px 6px;
        border-radius: 4px;
        margin-right: 8px;
        min-width: 60px;
        text-align: center;
        font-weight: 500;
        
        &.SELECT {
          background: #e6f7ff;
          color: #1890ff;
        }
        
        &.CHANGE {
          background: #f6ffed;
          color: #52c41a;
        }
        
        &.CREATE {
          background: #fff2e8;
          color: #fa8c16;
        }
      }
      
      .log-message {
        flex: 1;
        color: #333;
      }
    }
    
    .no-logs {
      text-align: center;
      color: #999;
      padding: 20px;
    }
  }
}
</style>
