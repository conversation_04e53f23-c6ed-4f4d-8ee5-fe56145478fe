# ZyIndustrySelect 行业选择组件

基于 ZyIndustry 实体类的可检索下拉框组件，专门用于选择行业类别。该组件只显示叶子节点，并在选项中以副标题的形式显示完整的父节点路径，方便用户做出准确的选择。

## 功能特性

- ✅ **叶子节点显示**：只显示树形结构中的叶子节点，避免选择到父级分类
- ✅ **路径显示**：在下拉选项中显示完整的父节点路径，格式为"叶子节点名称 - 父节点1 > 父节点2 > ..."
- ✅ **搜索功能**：支持按名称、代码、助记码和父节点路径进行搜索
- ✅ **双向绑定**：支持 v-model 双向数据绑定
- ✅ **加载状态**：显示数据加载状态和空数据提示
- ✅ **样式优化**：美观的UI设计，支持hover和focus状态
- ✅ **方法暴露**：提供刷新数据等方法供外部调用
- 🚀 **高性能**：单一API调用 + 延迟加载 + 前端过滤，请求数量从409次优化到1次
- 🚀 **即时搜索**：前端过滤实现毫秒级搜索响应

## 基本用法

```vue
<template>
  <ZyIndustrySelect
    v-model:value="selectedIndustry"
    placeholder="请选择行业类别"
    @change="handleChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import ZyIndustrySelect from '/@/components/occu/ZyIndustrySelect.vue';

const selectedIndustry = ref();

const handleChange = (value, option) => {
  console.log('选择的行业:', value, option);
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| value | 选中的值 | `string` | - |
| placeholder | 占位符文本 | `string` | `'请选择行业类别'` |
| disabled | 是否禁用 | `boolean` | `false` |

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:value | 值变化时触发 | `(value: string \| undefined) => void` |
| change | 选择变化时触发 | `(value: string \| undefined, option?: any) => void` |

### Methods

| 方法名 | 说明 | 参数 |
|--------|------|------|
| loadData | 重新加载数据 | `(keyword?: string) => Promise<void>` |
| refresh | 刷新数据（loadData的别名） | `() => Promise<void>` |

## 使用示例

### 在表单中使用

```vue
<template>
  <a-form :model="formData">
    <a-form-item label="所属行业" name="industry">
      <ZyIndustrySelect
        v-model:value="formData.industry"
        placeholder="请选择所属行业"
        @change="handleIndustryChange"
      />
    </a-form-item>
  </a-form>
</template>

<script setup>
import { reactive } from 'vue';
import ZyIndustrySelect from '/@/components/occu/ZyIndustrySelect.vue';

const formData = reactive({
  industry: '',
});

const handleIndustryChange = (value) => {
  console.log('选择的行业ID:', value);
};
</script>
```

### 调用组件方法

```vue
<template>
  <div>
    <ZyIndustrySelect
      ref="industrySelectRef"
      v-model:value="selectedIndustry"
    />
    <a-button @click="refreshData">刷新数据</a-button>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import ZyIndustrySelect from '/@/components/occu/ZyIndustrySelect.vue';

const industrySelectRef = ref();
const selectedIndustry = ref();

const refreshData = () => {
  industrySelectRef.value?.refresh();
};
</script>
```

## 数据结构

组件返回的数据结构：

```typescript
interface IndustryOption {
  id: string;           // 行业ID
  name: string;         // 行业名称
  code?: string;        // 行业代码
  helpChar?: string;    // 助记码
  parentNames: string[]; // 父节点名称数组
  fullPath: string;     // 完整路径（用于tooltip）
  parentPath: string;   // 父级路径（用于副标题显示）
}
```

## 样式定制

组件支持通过CSS变量或深度选择器进行样式定制：

```vue
<style>
/* 自定义下拉框样式 */
.custom-industry-select :deep(.ant-select-selector) {
  border-radius: 8px;
}

/* 自定义选项样式 */
.custom-industry-select :deep(.industry-option) {
  .industry-name {
    color: #1890ff;
  }
  
  .industry-path {
    color: #999;
  }
}
</style>
```

## 注意事项

1. **数据加载**：组件会在挂载时自动加载所有叶子节点数据，如果数据量较大可能会影响性能
2. **搜索功能**：目前搜索是在前端进行过滤，如需服务端搜索可以修改 `handleSearch` 方法
3. **API依赖**：组件依赖 `ZyIndustry.api.ts` 中的 `loadTreeData` 和 `getChildList` 方法
4. **递归加载**：组件会递归加载整个树形结构来获取所有叶子节点，请确保后端API支持

## 完整示例

查看 `ZyIndustrySelectExample.vue` 文件获取完整的使用示例，包括基本用法、表单集成、禁用状态等各种场景。
