import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import AutoComplete from '../AutoComplete.vue';
import * as api from '../AutoComplete.api';

// Mock API
vi.mock('../AutoComplete.api', () => ({
  autoCompleteSearch: vi.fn(),
  getPopularItems: vi.fn(),
  autoCreateItem: vi.fn(),
  updateUseCount: vi.fn(),
}));

// Mock ant-design-vue
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

describe('AutoComplete', () => {
  let wrapper: any;

  const mockSearchResponse = {
    success: true,
    result: [
      {
        id: '1',
        category: 'worktype',
        name: '电工',
        helpChar: 'DG',
        useCount: 10,
        isNew: false,
        matchType: 'name'
      },
      {
        id: '2',
        category: 'worktype',
        name: '焊工',
        helpChar: 'HG',
        useCount: 8,
        isNew: false,
        matchType: 'name'
      }
    ]
  };

  const mockPopularResponse = {
    success: true,
    result: [
      {
        id: '1',
        category: 'worktype',
        name: '电工',
        helpChar: 'DG',
        useCount: 10,
        isNew: false,
        matchType: 'popular'
      }
    ]
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock responses
    (api.autoCompleteSearch as any).mockResolvedValue(mockSearchResponse);
    (api.getPopularItems as any).mockResolvedValue(mockPopularResponse);
    (api.autoCreateItem as any).mockResolvedValue({
      success: true,
      result: { id: '3', name: '新工种' }
    });
    (api.updateUseCount as any).mockResolvedValue({ success: true });
  });

  const createWrapper = (props = {}) => {
    return mount(AutoComplete, {
      props: {
        category: 'worktype',
        ...props
      },
      global: {
        stubs: {
          'a-auto-complete': {
            template: `
              <div class="mock-auto-complete">
                <input 
                  :value="value" 
                  @input="$emit('search', $event.target.value)"
                  @change="$emit('change', $event.target.value)"
                />
                <div v-if="options.length" class="options">
                  <div 
                    v-for="option in options" 
                    :key="option.value"
                    class="option"
                    @click="$emit('select', option.value, option)"
                  >
                    {{ option.label }}
                  </div>
                </div>
              </div>
            `,
            props: ['value', 'options', 'placeholder', 'disabled'],
            emits: ['search', 'select', 'change', 'focus', 'blur']
          }
        }
      }
    });
  };

  it('renders correctly with required props', () => {
    wrapper = createWrapper();
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.auto-complete-wrapper').exists()).toBe(true);
  });

  it('handles search input correctly', async () => {
    wrapper = createWrapper();
    
    const input = wrapper.find('input');
    await input.setValue('电工');
    await input.trigger('input');
    
    // Wait for debounced search
    await new Promise(resolve => setTimeout(resolve, 350));
    
    expect(api.autoCompleteSearch).toHaveBeenCalledWith({
      category: 'worktype',
      keyword: '电工',
      limit: 10,
      searchType: 'both',
      includePopular: false
    });
  });

  it('loads popular items on focus when enabled', async () => {
    wrapper = createWrapper({ showPopularOnFocus: true });
    
    const input = wrapper.find('input');
    await input.trigger('focus');
    
    expect(api.getPopularItems).toHaveBeenCalledWith('worktype', 10);
  });

  it('handles item selection correctly', async () => {
    wrapper = createWrapper({ autoUpdateUseCount: true });
    
    // Trigger search first to populate options
    const input = wrapper.find('input');
    await input.setValue('电工');
    await input.trigger('input');
    
    // Wait for search to complete
    await new Promise(resolve => setTimeout(resolve, 350));
    await nextTick();
    
    // Simulate option selection
    const option = wrapper.find('.option');
    if (option.exists()) {
      await option.trigger('click');
      
      expect(api.updateUseCount).toHaveBeenCalledWith('1');
    }
  });

  it('emits correct events', async () => {
    wrapper = createWrapper();
    
    const input = wrapper.find('input');
    
    // Test change event
    await input.setValue('test value');
    await input.trigger('change');
    
    expect(wrapper.emitted('update:value')).toBeTruthy();
    expect(wrapper.emitted('change')).toBeTruthy();
  });

  it('handles auto-create functionality', async () => {
    wrapper = createWrapper({ allowAutoCreate: true });
    
    // Mock search response with new item
    const searchWithNewResponse = {
      success: true,
      result: [
        {
          category: 'worktype',
          name: '新工种',
          isNew: true,
          matchType: 'new'
        }
      ]
    };
    
    (api.autoCompleteSearch as any).mockResolvedValueOnce(searchWithNewResponse);
    
    const input = wrapper.find('input');
    await input.setValue('新工种');
    await input.trigger('input');
    
    // Wait for search
    await new Promise(resolve => setTimeout(resolve, 350));
    await nextTick();
    
    // Simulate selecting the new item
    const option = wrapper.find('.option');
    if (option.exists()) {
      await option.trigger('click');
      
      expect(api.autoCreateItem).toHaveBeenCalledWith({
        category: 'worktype',
        name: '新工种'
      });
    }
  });

  it('validates required category prop', () => {
    // Should log warning when category is missing
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
    
    wrapper = mount(AutoComplete, {
      props: {},
      global: {
        stubs: {
          'a-auto-complete': { template: '<div></div>' }
        }
      }
    });
    
    // Trigger search without category
    wrapper.vm.handleSearch('test');
    
    expect(consoleSpy).toHaveBeenCalledWith('AutoComplete: category is required');
    
    consoleSpy.mockRestore();
  });

  it('handles API errors gracefully', async () => {
    const mockError = new Error('API Error');
    (api.autoCompleteSearch as any).mockRejectedValueOnce(mockError);
    
    wrapper = createWrapper();
    
    const input = wrapper.find('input');
    await input.setValue('error test');
    await input.trigger('input');
    
    // Wait for search to complete
    await new Promise(resolve => setTimeout(resolve, 350));
    
    // Should not throw error and should handle gracefully
    expect(wrapper.vm.loading).toBe(false);
  });

  it('respects limit prop', async () => {
    wrapper = createWrapper({ limit: 5 });
    
    const input = wrapper.find('input');
    await input.setValue('test');
    await input.trigger('input');
    
    await new Promise(resolve => setTimeout(resolve, 350));
    
    expect(api.autoCompleteSearch).toHaveBeenCalledWith(
      expect.objectContaining({ limit: 5 })
    );
  });

  it('respects searchType prop', async () => {
    wrapper = createWrapper({ searchType: 'helpChar' });
    
    const input = wrapper.find('input');
    await input.setValue('DG');
    await input.trigger('input');
    
    await new Promise(resolve => setTimeout(resolve, 350));
    
    expect(api.autoCompleteSearch).toHaveBeenCalledWith(
      expect.objectContaining({ searchType: 'helpChar' })
    );
  });

  it('exposes correct methods', () => {
    wrapper = createWrapper();
    
    expect(typeof wrapper.vm.loadPopularOptions).toBe('function');
    expect(typeof wrapper.vm.clearOptions).toBe('function');
    expect(typeof wrapper.vm.refresh).toBe('function');
  });

  it('clears options when clearOptions is called', async () => {
    wrapper = createWrapper();
    
    // First populate some options
    const input = wrapper.find('input');
    await input.setValue('test');
    await input.trigger('input');
    await new Promise(resolve => setTimeout(resolve, 350));
    
    // Then clear them
    wrapper.vm.clearOptions();
    await nextTick();
    
    expect(wrapper.vm.options).toHaveLength(0);
  });
});
