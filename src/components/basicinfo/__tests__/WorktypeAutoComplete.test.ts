import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import WorktypeAutoComplete from '../WorktypeAutoComplete.vue';

// Mock the AutoComplete component
vi.mock('../AutoComplete.vue', () => ({
  default: {
    name: 'AutoComplete',
    template: `
      <div class="mock-auto-complete">
        <input 
          :value="value" 
          @input="$emit('update:value', $event.target.value)"
          @change="$emit('change', $event.target.value)"
          @focus="$emit('focus')"
          @blur="$emit('blur')"
        />
      </div>
    `,
    props: [
      'value', 'category', 'placeholder', 'disabled', 'allowClear', 'size',
      'limit', 'searchType', 'allowAutoCreate', 'showHelpChar', 'showUseCount',
      'dropdownMatchSelectWidth', 'getPopupContainer', 'showPopularOnFocus',
      'autoUpdateUseCount'
    ],
    emits: ['update:value', 'select', 'create', 'change', 'focus', 'blur'],
    methods: {
      loadPopularOptions: vi.fn(),
      clearOptions: vi.fn(),
      refresh: vi.fn(),
    }
  }
}));

// Mock ant-design-vue message
vi.mock('ant-design-vue', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
    info: vi.fn(),
  },
}));

describe('WorktypeAutoComplete', () => {
  let wrapper: any;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const createWrapper = (props = {}) => {
    return mount(WorktypeAutoComplete, {
      props: {
        ...props
      }
    });
  };

  it('renders correctly with default props', () => {
    wrapper = createWrapper();
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find('.worktype-auto-complete').exists()).toBe(true);
  });

  it('passes correct props to AutoComplete component', () => {
    wrapper = createWrapper({
      placeholder: '请输入工种',
      limit: 15,
      searchType: 'name'
    });

    const autoComplete = wrapper.findComponent({ name: 'AutoComplete' });
    expect(autoComplete.props('category')).toBe('worktype');
    expect(autoComplete.props('placeholder')).toBe('请输入工种');
    expect(autoComplete.props('limit')).toBe(15);
    expect(autoComplete.props('searchType')).toBe('name');
  });

  it('handles value binding correctly', async () => {
    wrapper = createWrapper({ value: '电工' });
    
    expect(wrapper.vm.inputValue).toBe('电工');
    
    // Test external value change
    await wrapper.setProps({ value: '焊工' });
    expect(wrapper.vm.inputValue).toBe('焊工');
    
    // Test internal value change
    wrapper.vm.inputValue = '司机';
    await nextTick();
    expect(wrapper.emitted('update:value')).toContainEqual(['司机']);
  });

  it('handles select event correctly', async () => {
    wrapper = createWrapper();
    
    const mockOption = {
      value: '电工',
      label: '电工',
      helpChar: 'DG',
      useCount: 10,
      isNew: false,
      raw: { id: '1', name: '电工' }
    };

    await wrapper.vm.handleSelect('电工', mockOption);
    
    expect(wrapper.emitted('select')).toContainEqual(['电工', mockOption]);
  });

  it('handles create event correctly', async () => {
    wrapper = createWrapper();
    
    const mockNewItem = { id: '3', name: '新工种' };
    
    await wrapper.vm.handleCreate(mockNewItem);
    
    expect(wrapper.emitted('create')).toContainEqual([mockNewItem]);
  });

  it('validates worktype when validation is enabled', async () => {
    const customRules = [
      (value: string) => {
        if (value.length < 2) {
          return '工种名称至少2个字符';
        }
        return true;
      }
    ];

    wrapper = createWrapper({
      validateWorktype: true,
      worktypeRules: customRules
    });

    // Test valid worktype
    expect(wrapper.vm.validateWorktypeValue('电工')).toBe(true);
    
    // Test invalid worktype (too short)
    expect(wrapper.vm.validateWorktypeValue('A')).toBe(false);
    
    // Test empty worktype
    expect(wrapper.vm.validateWorktypeValue('')).toBe(false);
    
    // Test too long worktype
    expect(wrapper.vm.validateWorktypeValue('A'.repeat(51))).toBe(false);
  });

  it('provides worktype suggestions', () => {
    wrapper = createWrapper();
    
    const suggestions = wrapper.vm.getWorktypeSuggestions();
    
    expect(Array.isArray(suggestions)).toBe(true);
    expect(suggestions.length).toBeGreaterThan(0);
    expect(suggestions).toContain('电工');
    expect(suggestions).toContain('焊工');
    expect(suggestions).toContain('司机');
  });

  it('exposes correct methods', () => {
    wrapper = createWrapper();
    
    // Test that all expected methods are exposed
    expect(typeof wrapper.vm.loadPopularOptions).toBe('function');
    expect(typeof wrapper.vm.clearOptions).toBe('function');
    expect(typeof wrapper.vm.refresh).toBe('function');
    expect(typeof wrapper.vm.validateWorktype).toBe('function');
    expect(typeof wrapper.vm.getWorktypeSuggestions).toBe('function');
    expect(typeof wrapper.vm.getValue).toBe('function');
    expect(typeof wrapper.vm.setValue).toBe('function');
    expect(typeof wrapper.vm.clear).toBe('function');
    expect(typeof wrapper.vm.focus).toBe('function');
    expect(typeof wrapper.vm.blur).toBe('function');
  });

  it('getValue and setValue methods work correctly', async () => {
    wrapper = createWrapper();
    
    // Test setValue
    wrapper.vm.setValue('测试工种');
    expect(wrapper.vm.getValue()).toBe('测试工种');
    
    // Test clear
    wrapper.vm.clear();
    expect(wrapper.vm.getValue()).toBe('');
  });

  it('handles custom validation rules', () => {
    const customRules = [
      (value: string) => {
        if (value.includes('测试')) {
          return '工种名称不能包含"测试"';
        }
        return true;
      },
      (value: string) => {
        if (!/^[\u4e00-\u9fa5a-zA-Z]+$/.test(value)) {
          return '工种名称只能包含中文和英文';
        }
        return true;
      }
    ];

    wrapper = createWrapper({
      validateWorktype: true,
      worktypeRules: customRules
    });

    // Test rule 1: contains '测试'
    expect(wrapper.vm.validateWorktypeValue('测试工种')).toBe(false);
    
    // Test rule 2: contains numbers
    expect(wrapper.vm.validateWorktypeValue('工种123')).toBe(false);
    
    // Test valid value
    expect(wrapper.vm.validateWorktypeValue('电工')).toBe(true);
  });

  it('handles select with validation enabled', async () => {
    wrapper = createWrapper({ validateWorktype: true });
    
    const mockOption = {
      value: 'A', // Too short, should fail validation
      label: 'A',
      raw: { id: '1', name: 'A' }
    };

    await wrapper.vm.handleSelect('A', mockOption);
    
    // Should not emit select event due to validation failure
    expect(wrapper.emitted('select')).toBeFalsy();
  });

  it('forwards all events correctly', async () => {
    wrapper = createWrapper();
    
    const autoComplete = wrapper.findComponent({ name: 'AutoComplete' });
    
    // Test change event
    await autoComplete.vm.$emit('change', 'test value');
    expect(wrapper.emitted('change')).toContainEqual(['test value']);
    
    // Test focus event
    await autoComplete.vm.$emit('focus');
    expect(wrapper.emitted('focus')).toBeTruthy();
    
    // Test blur event
    await autoComplete.vm.$emit('blur');
    expect(wrapper.emitted('blur')).toBeTruthy();
  });

  it('applies worktype-specific styling', () => {
    wrapper = createWrapper();
    
    expect(wrapper.find('.worktype-auto-complete').exists()).toBe(true);
    
    // Check that the component has the expected CSS class
    const wrapperElement = wrapper.find('.worktype-auto-complete');
    expect(wrapperElement.exists()).toBe(true);
  });

  it('handles disabled state correctly', () => {
    wrapper = createWrapper({ disabled: true });
    
    const autoComplete = wrapper.findComponent({ name: 'AutoComplete' });
    expect(autoComplete.props('disabled')).toBe(true);
  });

  it('handles different sizes correctly', () => {
    wrapper = createWrapper({ size: 'large' });
    
    const autoComplete = wrapper.findComponent({ name: 'AutoComplete' });
    expect(autoComplete.props('size')).toBe('large');
  });
});
