# 缺失字段修复说明

## 问题描述
保存的数据缺少 `summaryId` 和 `customerRegId`，导致保存后无法回显。

## 问题原因分析

### 1. 实体类字段定义
查看 `ZyConclusionDetail.java` 实体类，确认包含必要字段：
```java
/**登记ID*/
@Excel(name = "登记ID", width = 15)
@ApiModelProperty(value = "登记ID")
private java.lang.String customerRegId;

/**总检ID*/
@Excel(name = "总检ID", width = 15)
@ApiModelProperty(value = "总检ID")
private java.lang.String summaryId;
```

### 2. 前端数据创建问题
在创建新记录时，没有设置 `customerRegId` 和 `summaryId` 字段：

```javascript
// 问题：自动创建卡片时缺少关联字段
const newRecord = {
  uuid: uuid,
  riskFactorId: risk.id,
  riskCode: risk.code,
  riskFactor: risk.name,
  // 缺少 customerRegId 和 summaryId
  // ...其他字段
};

// 问题：手动创建卡片时也缺少关联字段
const newRecord = {
  uuid: uuid,
  riskFactorId: null,
  riskCode: null,
  riskFactor: '',
  // 缺少 customerRegId 和 summaryId
  // ...其他字段
};
```

### 3. 保存时数据不完整
虽然在保存时会复制记录的所有字段，但如果创建时没有设置这些字段，保存时也不会有。

## 修复方案

### 1. 在创建记录时设置关联字段

#### 自动创建危害因素卡片
```javascript
const newRecord = {
  // 不设置id字段，让后端生成
  uuid: uuid, // 使用UUID作为唯一标识
  customerRegId: customerReg4Summary.value?.id, // 设置登记ID
  summaryId: customerSummary.value?.id, // 设置总检ID
  riskFactorId: risk.id,
  riskCode: risk.code,
  riskFactor: risk.name,
  // 根据危害因素在原列表中的位置决定主要/次要
  mainFlag: riskFactorList.value.findIndex(r => r.id === risk.id) === 0 ? '1' : '0',
  workType: '',
  conclusion: '',
  advice: '',
  zyDisease: '',
  zySymptom: '',
  according: '',
  editable: true, // 默认进入编辑状态
  isNew: true,
  autoCreated: true, // 标记为自动创建
};
```

#### 手动创建结论卡片
```javascript
const newRecord = {
  // 不设置id字段，让后端生成
  uuid: uuid, // 使用UUID作为唯一标识
  customerRegId: customerReg4Summary.value?.id, // 设置登记ID
  summaryId: customerSummary.value?.id, // 设置总检ID
  riskFactorId: null, // 手动创建的卡片不绑定特定危害因素
  riskCode: null,
  riskFactor: '', // 用户可以手动选择或输入
  mainFlag: '1',
  workType: '',
  conclusion: '',
  advice: '',
  zyDisease: '',
  zySymptom: '',
  according: '',
  editable: true,
  isNew: true,
  manualCreated: true, // 标记为手动创建
};
```

### 2. 在保存时确保字段完整性
```javascript
// 准备保存数据
const saveData = { ...record };
delete saveData.editable;
delete saveData.isNew;
delete saveData.onEdit;
delete saveData.uuid; // 不发送UUID到后端
delete saveData.autoCreated; // 删除临时标记
delete saveData.manualCreated; // 删除临时标记

// 确保必要的关联字段存在（双重保险）
if (!saveData.customerRegId && customerReg4Summary.value?.id) {
  saveData.customerRegId = customerReg4Summary.value.id;
}
if (!saveData.summaryId && customerSummary.value?.id) {
  saveData.summaryId = customerSummary.value.id;
}

console.log('准备保存的数据:', saveData);

// 调用保存接口
const isUpdate = !record.isNew && record.id; // 有ID且不是新记录就是更新
const result = await saveOrUpdate(saveData, isUpdate);
```

### 3. 数据来源说明
关联字段的数据来源：
```javascript
// 从注入的数据中获取
const customerReg4Summary = inject('customerReg4Summary');
const customerSummary = inject('customerSummary');

// customerRegId 来自 customerReg4Summary.value?.id
// summaryId 来自 customerSummary.value?.id
```

## 修复效果

### ✅ 解决的问题
1. **数据完整性**：新创建的记录包含完整的关联字段
2. **保存成功**：后端能正确保存包含关联字段的数据
3. **数据回显**：保存后的数据能正确回显，因为包含了必要的关联字段
4. **数据一致性**：确保所有记录都有正确的 `customerRegId` 和 `summaryId`

### ✅ 修复流程
1. **创建时设置**：在创建新记录时就设置正确的关联字段
2. **保存时检查**：在保存前再次确保关联字段存在（双重保险）
3. **清理临时字段**：删除不需要发送到后端的临时标记字段
4. **日志记录**：添加日志输出，便于调试和验证

### ✅ 数据流程
1. **注入数据** → `customerReg4Summary` 和 `customerSummary`
2. **创建记录** → 设置 `customerRegId` 和 `summaryId`
3. **用户编辑** → 填写业务字段
4. **保存数据** → 确保关联字段完整，发送到后端
5. **后端保存** → 标准的 MyBatis-Plus 保存逻辑
6. **数据回显** → 包含完整关联字段的数据能正确显示

## 测试建议
1. 测试自动创建的危害因素卡片保存后能正确回显
2. 测试手动创建的结论卡片保存后能正确回显
3. 检查保存的数据在数据库中是否包含正确的 `customerRegId` 和 `summaryId`
4. 测试页面刷新后数据能正确加载和显示
