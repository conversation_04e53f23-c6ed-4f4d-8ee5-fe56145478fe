# reItems结构调整说明

## 概述

本次调整主要是将`BasicInfoDTO`中的`reItems`字段从简单的字符串列表改为包含更多信息的对象列表，以支持新的数据结构需求。

## 修改内容

### 1. BasicInfoDTO结构调整

**文件位置**: `jeecg-module-physicalex/src/main/java/org/jeecg/modules/comInterface/dto/CompanyRegBatchCreateDTO.java`

#### 原始结构
```java
private List<String> reItems;
```

#### 新结构
```java
@ApiModelProperty(value = "体检项目列表")
private List<ReItemDTO> reItems;
```

#### 新增ReItemDTO类
```java
/**
 * 体检项目信息
 */
@Data
@ApiModel(value = "ReItemDTO", description = "体检项目信息")
public static class ReItemDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "项目ID", required = true)
    private String id;

    @ApiModelProperty(value = "项目名称")
    private String name;

    @ApiModelProperty(value = "部位ID")
    private String partId;
}
```

### 2. 处理逻辑调整

**文件位置**: `jeecg-module-physicalex/src/main/java/org/jeecg/modules/comInterface/service/impl/CompanyRegApiServiceImpl.java`

#### 主要修改点

1. **ID提取逻辑调整**
   ```java
   // 原始代码
   List<String> itemGroupIds = basicInfo.getReItems();
   
   // 新代码
   List<String> itemGroupIds = basicInfo.getReItems().stream()
           .map(CompanyRegBatchCreateDTO.ReItemDTO::getId)
           .collect(java.util.stream.Collectors.toList());
   ```

2. **添加partId处理**
   ```java
   // 创建ID到ReItemDTO的映射，用于获取partId
   java.util.Map<String, CompanyRegBatchCreateDTO.ReItemDTO> reItemMap = basicInfo.getReItems().stream()
           .collect(java.util.stream.Collectors.toMap(
                   CompanyRegBatchCreateDTO.ReItemDTO::getId,
                   item -> item
           ));

   // 在创建CustomerRegItemGroup时设置部位信息
   CompanyRegBatchCreateDTO.ReItemDTO reItem = reItemMap.get(itemGroup.getId());
   if (reItem != null && StringUtils.isNotBlank(reItem.getPartId())) {
       crig.setCheckPartId(reItem.getPartId());
       // 这里可以根据partId查询部位名称和编码，暂时先设置ID
       // TODO: 如果需要，可以添加部位信息查询逻辑
   }
   ```

## 数据结构对比

### 原始数据结构
```json
{
  "reItems": ["1957690578070278146", "1957690474265448449", "1957690545300180994"]
}
```

### 新数据结构
```json
{
  "reItems": [
    {
      "id": "1957690578070278146",
      "name": "身高体重",
      "partId": null
    },
    {
      "id": "1957690474265448449",
      "name": "C-13呼气试验",
      "partId": null
    },
    {
      "id": "1957690545300180994",
      "name": "C-13呼气试验(赠送)",
      "partId": "492"
    },
    {
      "id": "1853381240242180097",
      "name": "采血费",
      "partId": null
    }
  ]
}
```

## 影响范围

### 1. 数据传输对象(DTO)
- `CompanyRegBatchCreateDTO.BasicInfoDTO`: reItems字段类型变更
- 新增`CompanyRegBatchCreateDTO.ReItemDTO`: 体检项目信息封装

### 2. 业务逻辑
- `CompanyRegApiServiceImpl.processPersonnelInfo()`: 处理reItems的逻辑调整
- 支持partId字段的处理和存储

### 3. 数据库实体
- `CustomerRegItemGroup`: 利用现有的`checkPartId`字段存储部位信息

## 兼容性说明

1. **向后兼容**: 新结构包含了原有的ID信息，通过提取ID的方式保持了与现有逻辑的兼容性
2. **扩展性**: 新增的name和partId字段为未来功能扩展提供了基础
3. **数据完整性**: partId字段可以为null，不影响现有数据的处理

## 测试建议

1. **单元测试**: 验证ReItemDTO的序列化和反序列化
2. **集成测试**: 测试完整的体检人员信息处理流程
3. **API测试**: 验证新的数据结构在API调用中的正确性

## 注意事项

1. 前端调用此API时需要按照新的数据结构传递reItems参数
2. partId字段目前只存储到CustomerRegItemGroup的checkPartId字段，如需要部位名称等信息，需要额外的查询逻辑
3. 建议在生产环境部署前进行充分的测试，确保数据处理的正确性

## 后续优化建议

1. 如果需要部位名称和编码信息，可以在处理时根据partId查询相关信息并设置到CustomerRegItemGroup的checkPartName和checkPartCode字段
2. 考虑添加数据验证逻辑，确保传入的项目ID和部位ID的有效性
3. 可以考虑添加缓存机制来提高部位信息查询的性能
