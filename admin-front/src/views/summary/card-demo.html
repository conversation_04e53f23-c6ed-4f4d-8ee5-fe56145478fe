<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>职业病体检结论卡片展示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .demo-header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: 600;
            color: #1890ff;
            margin-bottom: 10px;
        }
        
        .demo-description {
            color: #666;
            line-height: 1.6;
        }
        
        .cards-preview {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(600px, 1fr));
            gap: 16px;
        }
        
        .card-demo {
            background: white;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e8e8e8;
            transition: all 0.3s ease;
        }
        
        .card-demo:hover {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateY(-2px);
        }
        
        .card-demo.editing {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
            background-color: #f6ffed;
        }
        
        .card-demo.main-factor {
            border-left: 4px solid #ff4d4f;
        }
        
        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
            padding-bottom: 12px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .factor-info {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .tag {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .tag.main {
            background: #fff2f0;
            color: #ff4d4f;
            border: 1px solid #ffccc7;
        }
        
        .tag.secondary {
            background: #e6f7ff;
            color: #1890ff;
            border: 1px solid #91d5ff;
        }
        
        .risk-factor {
            font-weight: 500;
            color: #262626;
        }
        
        .card-actions {
            display: flex;
            gap: 8px;
        }
        
        .btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .btn-edit {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .btn-save {
            background: #f6ffed;
            color: #52c41a;
        }
        
        .btn-cancel {
            background: #f5f5f5;
            color: #8c8c8c;
        }
        
        .btn-delete {
            background: #fff2f0;
            color: #ff4d4f;
        }
        
        .card-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 12px;
        }
        
        .field-item {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        
        .field-label {
            font-size: 12px;
            color: #8c8c8c;
            font-weight: 500;
        }
        
        .field-value {
            font-size: 13px;
            color: #262626;
        }
        
        .tag-list {
            display: flex;
            flex-wrap: wrap;
            gap: 4px;
        }
        
        .tag-small {
            padding: 1px 6px;
            border-radius: 3px;
            font-size: 11px;
            background: #f0f0f0;
            color: #666;
        }
        
        .tag-small.disease {
            background: #fff7e6;
            color: #fa8c16;
            border: 1px solid #ffd591;
        }
        
        .tag-small.symptom {
            background: #f9f0ff;
            color: #722ed1;
            border: 1px solid #d3adf7;
        }
        
        .tag-small.conclusion {
            background: #f6ffed;
            color: #52c41a;
            border: 1px solid #b7eb8f;
        }
        
        .features-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .features-list h3 {
            color: #1890ff;
            margin-bottom: 15px;
        }
        
        .features-list ul {
            list-style: none;
            padding: 0;
        }
        
        .features-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .features-list li:before {
            content: "✅";
            margin-right: 8px;
        }
        
        @media (max-width: 768px) {
            .cards-preview {
                grid-template-columns: 1fr;
            }
            
            .card-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <div class="demo-title">🎯 职业病体检结论紧凑卡片设计</div>
            <div class="demo-description">
                将原有的表格形式改为紧凑的卡片布局，提供更好的视觉体验和编辑交互。每张卡片包含完整的结论信息，支持行内编辑和快速操作。
            </div>
        </div>

        <div class="features-list">
            <h3>🚀 卡片设计特点</h3>
            <ul>
                <li>紧凑布局：在有限空间内展示完整信息</li>
                <li>视觉层次：通过颜色和标签区分重要程度</li>
                <li>行内编辑：直接在卡片中编辑，无需弹窗</li>
                <li>状态指示：清晰的编辑、保存状态提示</li>
                <li>响应式设计：适配不同屏幕尺寸</li>
                <li>批量操作：支持多选和批量处理</li>
            </ul>
        </div>

        <div class="cards-preview">
            <!-- 普通状态卡片 -->
            <div class="card-demo">
                <div class="card-header">
                    <div class="factor-info">
                        <span class="tag secondary">次要</span>
                        <span class="risk-factor">噪声</span>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-edit">编辑</button>
                        <button class="btn btn-delete">删除</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="field-item">
                        <div class="field-label">工种:</div>
                        <div class="field-value">操作工</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业检结论:</div>
                        <div class="field-value">
                            <span class="tag-small conclusion">正常</span>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">处理意见:</div>
                        <div class="field-value">建议定期复查，注意防护</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业病:</div>
                        <div class="field-value">-</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业禁忌证:</div>
                        <div class="field-value">-</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">结论依据:</div>
                        <div class="field-value">GBZ188-2014职业健康监护技术规范</div>
                    </div>
                </div>
            </div>

            <!-- 主要因素卡片 -->
            <div class="card-demo main-factor">
                <div class="card-header">
                    <div class="factor-info">
                        <span class="tag main">主要</span>
                        <span class="risk-factor">粉尘</span>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-edit">编辑</button>
                        <button class="btn btn-delete">删除</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="field-item">
                        <div class="field-label">工种:</div>
                        <div class="field-value">打磨工</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业检结论:</div>
                        <div class="field-value">
                            <span class="tag-small conclusion">疑似职业病</span>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">处理意见:</div>
                        <div class="field-value">建议进一步诊断，暂时脱离接触</div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业病:</div>
                        <div class="field-value">
                            <div class="tag-list">
                                <span class="tag-small disease">尘肺病</span>
                            </div>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业禁忌证:</div>
                        <div class="field-value">
                            <div class="tag-list">
                                <span class="tag-small symptom">呼吸系统疾病</span>
                            </div>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">结论依据:</div>
                        <div class="field-value">GBZ70-2015尘肺病诊断标准</div>
                    </div>
                </div>
            </div>

            <!-- 编辑状态卡片 -->
            <div class="card-demo editing">
                <div class="card-header">
                    <div class="factor-info">
                        <span class="tag main">主要</span>
                        <span class="risk-factor">化学毒物</span>
                    </div>
                    <div class="card-actions">
                        <button class="btn btn-save">保存</button>
                        <button class="btn btn-cancel">取消</button>
                        <button class="btn btn-delete" disabled>删除</button>
                    </div>
                </div>
                <div class="card-content">
                    <div class="field-item">
                        <div class="field-label">工种:</div>
                        <div class="field-value">
                            <input type="text" value="化验员" style="width: 100%; padding: 4px; border: 1px solid #d9d9d9; border-radius: 4px;">
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业检结论:</div>
                        <div class="field-value">
                            <select style="width: 100%; padding: 4px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option>正常</option>
                                <option selected>异常</option>
                                <option>疑似职业病</option>
                            </select>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">处理意见:</div>
                        <div class="field-value">
                            <textarea rows="2" style="width: 100%; padding: 4px; border: 1px solid #d9d9d9; border-radius: 4px;">建议加强个人防护，定期监测肝功能</textarea>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业病:</div>
                        <div class="field-value">
                            <select multiple style="width: 100%; height: 60px; padding: 4px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option>职业性肝病</option>
                                <option>职业性皮肤病</option>
                            </select>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">职业禁忌证:</div>
                        <div class="field-value">
                            <select multiple style="width: 100%; height: 60px; padding: 4px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option>肝功能异常</option>
                                <option>皮肤过敏</option>
                            </select>
                        </div>
                    </div>
                    <div class="field-item">
                        <div class="field-label">结论依据:</div>
                        <div class="field-value">
                            <select style="width: 100%; padding: 4px; border: 1px solid #d9d9d9; border-radius: 4px;">
                                <option selected>GBZ188-2014职业健康监护技术规范</option>
                                <option>GBZ18-2013职业性皮肤病诊断标准</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
