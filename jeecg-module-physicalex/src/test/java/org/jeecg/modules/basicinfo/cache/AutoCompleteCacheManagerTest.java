package org.jeecg.modules.basicinfo.cache;

import org.jeecg.modules.basicinfo.entity.AutoCompleteConfig;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 自动补全缓存管理器测试
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("test")
public class AutoCompleteCacheManagerTest {

    @Autowired
    private AutoCompleteCacheManager cacheManager;

    private static final String TEST_CATEGORY = "test_cache";

    @BeforeEach
    void setUp() {
        // 清理测试缓存
        cacheManager.clearCategoryCache(TEST_CATEGORY);
    }

    @Test
    void testPopularCache() {
        // 准备测试数据
        List<AutoCompleteResultVO> testData = createTestResults();

        // 设置缓存
        cacheManager.setPopularCache(TEST_CATEGORY, 10, testData, 1);

        // 获取缓存
        List<AutoCompleteResultVO> cachedData = cacheManager.getPopularCache(TEST_CATEGORY, 10);

        // 验证结果
        assertNotNull(cachedData);
        assertEquals(testData.size(), cachedData.size());
        assertEquals(testData.get(0).getName(), cachedData.get(0).getName());
    }

    @Test
    void testSearchCache() {
        // 准备测试数据
        List<AutoCompleteResultVO> testData = createTestResults();

        // 设置搜索缓存
        cacheManager.setSearchCache(TEST_CATEGORY, "电工", "both", 10, testData, 30);

        // 获取搜索缓存
        List<AutoCompleteResultVO> cachedData = cacheManager.getSearchCache(TEST_CATEGORY, "电工", "both", 10);

        // 验证结果
        assertNotNull(cachedData);
        assertEquals(testData.size(), cachedData.size());
    }

    @Test
    void testConfigCache() {
        // 准备测试配置
        AutoCompleteConfig testConfig = new AutoCompleteConfig();
        testConfig.setCategory(TEST_CATEGORY);
        testConfig.setCategoryName("测试分类");
        testConfig.setCacheEnabled(1);
        testConfig.setCacheExpireHours(24);
        testConfig.setMaxSuggestions(10);

        // 设置配置缓存
        cacheManager.setConfigCache(TEST_CATEGORY, testConfig, 24);

        // 获取配置缓存
        AutoCompleteConfig cachedConfig = cacheManager.getConfigCache(TEST_CATEGORY);

        // 验证结果
        assertNotNull(cachedConfig);
        assertEquals(TEST_CATEGORY, cachedConfig.getCategory());
        assertEquals("测试分类", cachedConfig.getCategoryName());
    }

    @Test
    void testStatsCache() {
        // 准备测试统计数据
        Object testStats = createTestStats();

        // 设置统计缓存
        cacheManager.setStatsCache(TEST_CATEGORY, testStats, 6);

        // 获取统计缓存
        Object cachedStats = cacheManager.getStatsCache(TEST_CATEGORY);

        // 验证结果
        assertNotNull(cachedStats);
    }

    @Test
    void testClearCategoryCache() {
        // 先设置一些缓存
        List<AutoCompleteResultVO> testData = createTestResults();
        cacheManager.setPopularCache(TEST_CATEGORY, 10, testData, 1);
        cacheManager.setSearchCache(TEST_CATEGORY, "test", "both", 10, testData, 30);

        // 验证缓存存在
        assertNotNull(cacheManager.getPopularCache(TEST_CATEGORY, 10));
        assertNotNull(cacheManager.getSearchCache(TEST_CATEGORY, "test", "both", 10));

        // 清除分类缓存
        boolean result = cacheManager.clearCategoryCache(TEST_CATEGORY);
        assertTrue(result);

        // 验证缓存已清除
        assertNull(cacheManager.getPopularCache(TEST_CATEGORY, 10));
        assertNull(cacheManager.getSearchCache(TEST_CATEGORY, "test", "both", 10));
    }

    @Test
    void testClearAllCache() {
        // 先设置一些缓存
        List<AutoCompleteResultVO> testData = createTestResults();
        cacheManager.setPopularCache(TEST_CATEGORY, 10, testData, 1);
        cacheManager.setPopularCache("another_category", 10, testData, 1);

        // 清除所有缓存
        boolean result = cacheManager.clearAllCache();
        assertTrue(result);

        // 验证缓存已清除
        assertNull(cacheManager.getPopularCache(TEST_CATEGORY, 10));
        assertNull(cacheManager.getPopularCache("another_category", 10));
    }

    @Test
    void testGetCacheStats() {
        // 设置一些缓存
        List<AutoCompleteResultVO> testData = createTestResults();
        cacheManager.setPopularCache(TEST_CATEGORY, 10, testData, 1);
        cacheManager.setSearchCache(TEST_CATEGORY, "test", "both", 10, testData, 30);

        // 获取缓存统计
        Object stats = cacheManager.getCacheStats();
        assertNotNull(stats);
    }

    @Test
    void testCacheExpiration() throws InterruptedException {
        // 准备测试数据
        List<AutoCompleteResultVO> testData = createTestResults();

        // 设置短期缓存（1秒）
        cacheManager.setSearchCache(TEST_CATEGORY, "expire_test", "both", 10, testData, 1);

        // 立即获取，应该存在
        List<AutoCompleteResultVO> cachedData = cacheManager.getSearchCache(TEST_CATEGORY, "expire_test", "both", 10);
        assertNotNull(cachedData);

        // 等待缓存过期
        Thread.sleep(2000);

        // 再次获取，应该为空
        List<AutoCompleteResultVO> expiredData = cacheManager.getSearchCache(TEST_CATEGORY, "expire_test", "both", 10);
        assertNull(expiredData);
    }

    @Test
    void testNullParameterHandling() {
        // 测试空参数处理
        assertNull(cacheManager.getPopularCache(null, 10));
        assertNull(cacheManager.getPopularCache("", 10));
        
        cacheManager.setPopularCache(null, 10, createTestResults(), 1);
        cacheManager.setPopularCache("", 10, null, 1);
        
        // 这些操作不应该抛出异常
        assertTrue(true);
    }

    /**
     * 创建测试结果数据
     */
    private List<AutoCompleteResultVO> createTestResults() {
        List<AutoCompleteResultVO> results = new ArrayList<>();
        
        AutoCompleteResultVO result1 = new AutoCompleteResultVO();
        result1.setId("1");
        result1.setCategory(TEST_CATEGORY);
        result1.setName("电工");
        result1.setHelpChar("DG");
        result1.setUseCount(10);
        results.add(result1);
        
        AutoCompleteResultVO result2 = new AutoCompleteResultVO();
        result2.setId("2");
        result2.setCategory(TEST_CATEGORY);
        result2.setName("焊工");
        result2.setHelpChar("HG");
        result2.setUseCount(8);
        results.add(result2);
        
        return results;
    }

    /**
     * 创建测试统计数据
     */
    private Object createTestStats() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("category", TEST_CATEGORY);
        stats.put("totalCount", 10);
        stats.put("popularCount", 5);
        return stats;
    }
}
