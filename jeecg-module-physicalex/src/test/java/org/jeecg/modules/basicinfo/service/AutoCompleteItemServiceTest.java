package org.jeecg.modules.basicinfo.service;

import org.jeecg.modules.basicinfo.entity.AutoCompleteItem;
import org.jeecg.modules.basicinfo.vo.AutoCompleteCreateVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteSearchVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: 自动补全项目服务测试
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class AutoCompleteItemServiceTest {

    @Autowired
    private IAutoCompleteItemService autoCompleteItemService;

    private static final String TEST_CATEGORY = "test_worktype";

    @BeforeEach
    void setUp() {
        // 清理测试数据
        autoCompleteItemService.clearCategoryCache(TEST_CATEGORY);
    }

    @Test
    void testAutoCreateItem() {
        // 准备测试数据
        AutoCompleteCreateVO createVO = new AutoCompleteCreateVO();
        createVO.setCategory(TEST_CATEGORY);
        createVO.setName("测试工种");
        createVO.setHelpChar("CSGZ");
        createVO.setRemark("这是一个测试工种");

        // 执行创建
        AutoCompleteItem result = autoCompleteItemService.autoCreateItem(createVO);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(TEST_CATEGORY, result.getCategory());
        assertEquals("测试工种", result.getName());
        assertEquals("CSGZ", result.getHelpChar());
        assertEquals(Integer.valueOf(1), result.getStatus());
        assertEquals(Integer.valueOf(0), result.getDelFlag());
    }

    @Test
    void testAutoCreateItemDuplicate() {
        // 创建第一个项目
        AutoCompleteCreateVO createVO = new AutoCompleteCreateVO();
        createVO.setCategory(TEST_CATEGORY);
        createVO.setName("重复工种");

        AutoCompleteItem first = autoCompleteItemService.autoCreateItem(createVO);
        assertNotNull(first);

        // 尝试创建重复项目
        AutoCompleteItem second = autoCompleteItemService.autoCreateItem(createVO);
        assertNotNull(second);
        assertEquals(first.getId(), second.getId()); // 应该返回已存在的项目
    }

    @Test
    void testAutoCompleteSearch() {
        // 先创建测试数据
        createTestData();

        // 测试按名称搜索
        AutoCompleteSearchVO searchVO = new AutoCompleteSearchVO();
        searchVO.setCategory(TEST_CATEGORY);
        searchVO.setKeyword("电工");
        searchVO.setSearchType("name");
        searchVO.setLimit(10);

        List<AutoCompleteResultVO> results = autoCompleteItemService.autoCompleteSearch(searchVO);
        assertNotNull(results);
        assertTrue(results.size() > 0);
        assertTrue(results.stream().anyMatch(r -> r.getName().contains("电工")));
    }

    @Test
    void testAutoCompleteSearchByHelpChar() {
        // 先创建测试数据
        createTestData();

        // 测试按助记码搜索
        AutoCompleteSearchVO searchVO = new AutoCompleteSearchVO();
        searchVO.setCategory(TEST_CATEGORY);
        searchVO.setKeyword("DG");
        searchVO.setSearchType("helpChar");
        searchVO.setLimit(10);

        List<AutoCompleteResultVO> results = autoCompleteItemService.autoCompleteSearch(searchVO);
        assertNotNull(results);
        assertTrue(results.size() > 0);
        assertTrue(results.stream().anyMatch(r -> "DG".equals(r.getHelpChar())));
    }

    @Test
    void testAutoCompleteSearchBoth() {
        // 先创建测试数据
        createTestData();

        // 测试混合搜索
        AutoCompleteSearchVO searchVO = new AutoCompleteSearchVO();
        searchVO.setCategory(TEST_CATEGORY);
        searchVO.setKeyword("焊");
        searchVO.setSearchType("both");
        searchVO.setLimit(10);

        List<AutoCompleteResultVO> results = autoCompleteItemService.autoCompleteSearch(searchVO);
        assertNotNull(results);
        assertTrue(results.size() > 0);
    }

    @Test
    void testGetPopularItems() {
        // 先创建测试数据并设置使用频次
        createTestDataWithUseCount();

        List<AutoCompleteResultVO> results = autoCompleteItemService.getPopularItems(TEST_CATEGORY, 5);
        assertNotNull(results);
        assertTrue(results.size() > 0);
        
        // 验证按使用频次排序
        if (results.size() > 1) {
            for (int i = 0; i < results.size() - 1; i++) {
                assertTrue(results.get(i).getUseCount() >= results.get(i + 1).getUseCount());
            }
        }
    }

    @Test
    void testUpdateUseCount() {
        // 创建测试项目
        AutoCompleteCreateVO createVO = new AutoCompleteCreateVO();
        createVO.setCategory(TEST_CATEGORY);
        createVO.setName("使用频次测试");

        AutoCompleteItem item = autoCompleteItemService.autoCreateItem(createVO);
        assertNotNull(item);

        // 更新使用频次
        boolean result = autoCompleteItemService.updateUseCount(item.getId(), "testUser", "测试用户");
        assertTrue(result);

        // 验证使用频次已更新
        AutoCompleteItem updated = autoCompleteItemService.getById(item.getId());
        assertNotNull(updated);
        assertTrue(updated.getUseCount() > 0);
    }

    @Test
    void testCheckItemExists() {
        // 创建测试项目
        AutoCompleteCreateVO createVO = new AutoCompleteCreateVO();
        createVO.setCategory(TEST_CATEGORY);
        createVO.setName("存在性测试");

        AutoCompleteItem item = autoCompleteItemService.autoCreateItem(createVO);
        assertNotNull(item);

        // 检查项目是否存在
        String existingId = autoCompleteItemService.checkItemExists(TEST_CATEGORY, "存在性测试");
        assertNotNull(existingId);
        assertEquals(item.getId(), existingId);

        // 检查不存在的项目
        String nonExistingId = autoCompleteItemService.checkItemExists(TEST_CATEGORY, "不存在的项目");
        assertNull(nonExistingId);
    }

    @Test
    void testCacheOperations() {
        // 测试清除缓存
        boolean clearResult = autoCompleteItemService.clearCategoryCache(TEST_CATEGORY);
        assertTrue(clearResult);

        // 测试刷新缓存
        boolean refreshResult = autoCompleteItemService.refreshCategoryCache(TEST_CATEGORY);
        assertTrue(refreshResult);
    }

    @Test
    void testGetCategoryStats() {
        // 创建测试数据
        createTestData();

        Object stats = autoCompleteItemService.getCategoryStats(TEST_CATEGORY);
        assertNotNull(stats);
    }

    /**
     * 创建测试数据
     */
    private void createTestData() {
        String[] testWorktypes = {
            "电工", "焊工", "司机", "操作工", "维修工"
        };
        String[] testHelpChars = {
            "DG", "HG", "SJ", "CZG", "WXG"
        };

        for (int i = 0; i < testWorktypes.length; i++) {
            AutoCompleteCreateVO createVO = new AutoCompleteCreateVO();
            createVO.setCategory(TEST_CATEGORY);
            createVO.setName(testWorktypes[i]);
            createVO.setHelpChar(testHelpChars[i]);
            createVO.setRemark("测试工种 - " + testWorktypes[i]);

            autoCompleteItemService.autoCreateItem(createVO);
        }
    }

    /**
     * 创建带使用频次的测试数据
     */
    private void createTestDataWithUseCount() {
        createTestData();

        // 模拟使用频次
        List<AutoCompleteResultVO> items = autoCompleteItemService.getPopularItems(TEST_CATEGORY, 10);
        for (int i = 0; i < items.size(); i++) {
            AutoCompleteResultVO item = items.get(i);
            if (item.getId() != null) {
                // 设置不同的使用频次
                for (int j = 0; j < (items.size() - i); j++) {
                    autoCompleteItemService.updateUseCount(item.getId(), "testUser", "测试用户");
                }
            }
        }
    }
}
