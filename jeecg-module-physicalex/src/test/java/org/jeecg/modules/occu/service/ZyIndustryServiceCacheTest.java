package org.jeecg.modules.occu.service;

import lombok.extern.slf4j.Slf4j;
import org.jeecg.modules.occu.vo.ZyIndustryLeafNodeVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.cache.CacheManager;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

/**
 * @Description: ZyIndustry缓存功能测试
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
public class ZyIndustryServiceCacheTest {

    @Autowired
    private IZyIndustryService zyIndustryService;

    @Autowired
    private CacheManager cacheManager;

    private static final String CACHE_NAME = "zyIndustryLeafNodes";

    @Test
    public void testCacheFunction() {
        log.info("=== 开始测试ZyIndustry缓存功能 ===");

        // 1. 清空缓存
        cacheManager.getCache(CACHE_NAME).clear();
        log.info("1. 已清空缓存");

        // 2. 第一次查询（应该从数据库查询并缓存）
        long startTime1 = System.currentTimeMillis();
        List<ZyIndustryLeafNodeVO> result1 = zyIndustryService.getAllLeafNodes(null);
        long endTime1 = System.currentTimeMillis();
        log.info("2. 第一次查询耗时: {}ms, 结果数量: {}", (endTime1 - startTime1), result1.size());

        // 3. 第二次查询（应该从缓存获取）
        long startTime2 = System.currentTimeMillis();
        List<ZyIndustryLeafNodeVO> result2 = zyIndustryService.getAllLeafNodes(null);
        long endTime2 = System.currentTimeMillis();
        log.info("3. 第二次查询耗时: {}ms, 结果数量: {}", (endTime2 - startTime2), result2.size());

        // 4. 验证缓存是否生效（第二次查询应该更快）
        if (endTime2 - startTime2 < endTime1 - startTime1) {
            log.info("4. ✅ 缓存生效！第二次查询比第一次快 {}ms", 
                (endTime1 - startTime1) - (endTime2 - startTime2));
        } else {
            log.warn("4. ❌ 缓存可能未生效");
        }

        // 5. 测试搜索功能
        long startTime3 = System.currentTimeMillis();
        List<ZyIndustryLeafNodeVO> searchResult = zyIndustryService.getAllLeafNodes("制造");
        long endTime3 = System.currentTimeMillis();
        log.info("5. 搜索查询耗时: {}ms, 搜索结果数量: {}", (endTime3 - startTime3), searchResult.size());

        // 6. 测试手动刷新缓存
        zyIndustryService.refreshLeafNodesCache();
        log.info("6. 已手动刷新缓存");

        // 7. 刷新后再次查询
        long startTime4 = System.currentTimeMillis();
        List<ZyIndustryLeafNodeVO> result4 = zyIndustryService.getAllLeafNodes(null);
        long endTime4 = System.currentTimeMillis();
        log.info("7. 刷新后查询耗时: {}ms, 结果数量: {}", (endTime4 - startTime4), result4.size());

        log.info("=== ZyIndustry缓存功能测试完成 ===");
    }

    @Test
    public void testCacheEviction() {
        log.info("=== 开始测试缓存清除功能 ===");

        // 1. 先查询一次，确保缓存存在
        List<ZyIndustryLeafNodeVO> result1 = zyIndustryService.getAllLeafNodes(null);
        log.info("1. 初始查询结果数量: {}", result1.size());

        // 2. 检查缓存是否存在（如果cacheManager可用）
        try {
            boolean cacheExists = cacheManager.getCache(CACHE_NAME) != null &&
                                 cacheManager.getCache(CACHE_NAME).get("all") != null;
            log.info("2. 缓存是否存在: {}", cacheExists);
        } catch (Exception e) {
            log.info("2. 无法直接检查缓存状态（这是正常的）: {}", e.getMessage());
        }

        // 3. 手动刷新缓存（应该清除缓存）
        zyIndustryService.refreshLeafNodesCache();
        log.info("3. 已执行缓存刷新");

        // 4. 再次查询验证缓存是否重新构建
        long startTime = System.currentTimeMillis();
        List<ZyIndustryLeafNodeVO> result2 = zyIndustryService.getAllLeafNodes(null);
        long endTime = System.currentTimeMillis();
        log.info("4. 刷新后查询耗时: {}ms, 结果数量: {}", (endTime - startTime), result2.size());

        log.info("✅ 缓存清除功能测试完成");
        log.info("=== 缓存清除功能测试完成 ===");
    }
}
