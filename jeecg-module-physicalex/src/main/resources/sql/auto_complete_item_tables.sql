-- 通用自动补全数据表创建脚本
-- 此脚本用于创建通用自动补全功能所需的数据表
-- 支持各种场景的自动完成功能，包括工种、部门、职位等

-- 1. 创建通用自动补全项目表
DROP TABLE IF EXISTS `auto_complete_item`;
CREATE TABLE `auto_complete_item` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `category` varchar(50) NOT NULL COMMENT '分类标识(worktype-工种,department-部门,position-职位等)',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `help_char` varchar(50) DEFAULT NULL COMMENT '助记码(拼音首字母)',
  `pinyin` varchar(200) DEFAULT NULL COMMENT '全拼',
  `use_count` int(11) DEFAULT '0' COMMENT '使用频次',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(1-启用,0-禁用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_name` (`category`, `name`, `del_flag`),
  KEY `idx_category_status` (`category`, `status`),
  KEY `idx_use_count` (`use_count` DESC),
  KEY `idx_help_char` (`help_char`),
  KEY `idx_pinyin` (`pinyin`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通用自动补全项目表';

-- 2. 创建自动补全使用统计表
DROP TABLE IF EXISTS `auto_complete_usage_stat`;
CREATE TABLE `auto_complete_usage_stat` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `item_id` varchar(32) NOT NULL COMMENT '自动补全项目ID',
  `category` varchar(50) NOT NULL COMMENT '分类标识',
  `item_name` varchar(100) NOT NULL COMMENT '项目名称',
  `use_date` date NOT NULL COMMENT '使用日期',
  `use_count` int(11) DEFAULT '1' COMMENT '当日使用次数',
  `user_id` varchar(32) DEFAULT NULL COMMENT '使用用户ID',
  `user_name` varchar(50) DEFAULT NULL COMMENT '使用用户名',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_date_user` (`item_id`, `use_date`, `user_id`),
  KEY `idx_category_date` (`category`, `use_date`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_use_date` (`use_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动补全使用统计表';

-- 3. 创建自动补全配置表
DROP TABLE IF EXISTS `auto_complete_config`;
CREATE TABLE `auto_complete_config` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `category` varchar(50) NOT NULL COMMENT '分类标识',
  `category_name` varchar(100) NOT NULL COMMENT '分类名称',
  `cache_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用缓存(1-启用,0-禁用)',
  `cache_expire_hours` int(11) DEFAULT '24' COMMENT '缓存过期时间(小时)',
  `auto_create_enabled` tinyint(1) DEFAULT '1' COMMENT '是否允许自动创建(1-允许,0-不允许)',
  `max_suggestions` int(11) DEFAULT '10' COMMENT '最大建议数量',
  `search_type` varchar(20) DEFAULT 'both' COMMENT '搜索类型(name-仅名称,helpChar-仅助记码,both-两者)',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态(1-启用,0-禁用)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志(0-正常,1-删除)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category` (`category`, `del_flag`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='自动补全配置表';

-- 4. 插入默认配置数据
INSERT INTO `auto_complete_config` (`id`, `category`, `category_name`, `cache_enabled`, `cache_expire_hours`, `auto_create_enabled`, `max_suggestions`, `search_type`, `status`, `remark`, `create_by`) VALUES
('worktype_config', 'worktype', '工种', 1, 24, 1, 10, 'both', 1, '工种自动补全配置', 'system'),
('department_config', 'department', '部门', 1, 24, 1, 10, 'both', 1, '部门自动补全配置', 'system'),
('position_config', 'position', '职位', 1, 24, 1, 10, 'both', 1, '职位自动补全配置', 'system'),
('company_config', 'company', '公司', 1, 24, 1, 10, 'both', 1, '公司自动补全配置', 'system'),
('hazard_config', 'hazard', '危害因素', 1, 24, 1, 10, 'both', 1, '危害因素自动补全配置', 'system');

-- 5. 插入一些示例工种数据
INSERT INTO `auto_complete_item` (`id`, `category`, `name`, `help_char`, `pinyin`, `use_count`, `sort_order`, `status`, `remark`, `create_by`) VALUES
('worktype_001', 'worktype', '电工', 'DG', 'diangong', 0, 1, 1, '电气作业人员', 'system'),
('worktype_002', 'worktype', '焊工', 'HG', 'hangong', 0, 2, 1, '焊接作业人员', 'system'),
('worktype_003', 'worktype', '司机', 'SJ', 'siji', 0, 3, 1, '驾驶员', 'system'),
('worktype_004', 'worktype', '操作工', 'CZG', 'caozuogong', 0, 4, 1, '设备操作人员', 'system'),
('worktype_005', 'worktype', '维修工', 'WXG', 'weixiugong', 0, 5, 1, '设备维修人员', 'system'),
('worktype_006', 'worktype', '清洁工', 'QJG', 'qingjiangong', 0, 6, 1, '清洁作业人员', 'system'),
('worktype_007', 'worktype', '管理员', 'GLY', 'guanliyuan', 0, 7, 1, '管理人员', 'system'),
('worktype_008', 'worktype', '技术员', 'JSY', 'jishuyuan', 0, 8, 1, '技术人员', 'system'),
('worktype_009', 'worktype', '质检员', 'ZJY', 'zhijianyuan', 0, 9, 1, '质量检验人员', 'system'),
('worktype_010', 'worktype', '安全员', 'AQY', 'anquanyuan', 0, 10, 1, '安全管理人员', 'system');

-- 6. 创建视图简化查询
CREATE VIEW `v_auto_complete_popular` AS
SELECT 
  aci.`id`,
  aci.`category`,
  aci.`name`,
  aci.`help_char`,
  aci.`pinyin`,
  aci.`use_count`,
  aci.`sort_order`,
  aci.`status`,
  acc.`category_name`,
  acc.`max_suggestions`
FROM `auto_complete_item` aci
LEFT JOIN `auto_complete_config` acc ON aci.`category` = acc.`category`
WHERE aci.`del_flag` = 0 
  AND aci.`status` = 1 
  AND acc.`status` = 1
ORDER BY aci.`use_count` DESC, aci.`sort_order` ASC;

-- 7. 创建存储过程用于更新使用频次
DELIMITER $$
CREATE PROCEDURE `UpdateAutoCompleteUseCount`(
  IN p_item_id VARCHAR(32),
  IN p_user_id VARCHAR(32),
  IN p_user_name VARCHAR(50)
)
BEGIN
  DECLARE v_category VARCHAR(50);
  DECLARE v_item_name VARCHAR(100);
  DECLARE v_today DATE;
  
  SET v_today = CURDATE();
  
  -- 获取项目信息
  SELECT `category`, `name` INTO v_category, v_item_name
  FROM `auto_complete_item` 
  WHERE `id` = p_item_id AND `del_flag` = 0;
  
  IF v_category IS NOT NULL THEN
    -- 更新主表使用频次
    UPDATE `auto_complete_item` 
    SET `use_count` = `use_count` + 1,
        `update_time` = NOW()
    WHERE `id` = p_item_id;
    
    -- 插入或更新统计表
    INSERT INTO `auto_complete_usage_stat` 
    (`id`, `item_id`, `category`, `item_name`, `use_date`, `use_count`, `user_id`, `user_name`)
    VALUES 
    (UUID(), p_item_id, v_category, v_item_name, v_today, 1, p_user_id, p_user_name)
    ON DUPLICATE KEY UPDATE 
    `use_count` = `use_count` + 1,
    `update_time` = NOW();
  END IF;
END$$
DELIMITER ;

-- 8. 创建清理过期统计数据的存储过程
DELIMITER $$
CREATE PROCEDURE `CleanupAutoCompleteStats`(
  IN p_days_to_keep INT
)
BEGIN
  DECLARE v_cutoff_date DATE;
  
  SET v_cutoff_date = DATE_SUB(CURDATE(), INTERVAL p_days_to_keep DAY);
  
  DELETE FROM `auto_complete_usage_stat` 
  WHERE `use_date` < v_cutoff_date;
  
  SELECT ROW_COUNT() as deleted_rows;
END$$
DELIMITER ;

-- 9. 创建事件调度器定期清理过期数据（保留90天）
-- CREATE EVENT IF NOT EXISTS `cleanup_auto_complete_stats`
-- ON SCHEDULE EVERY 1 DAY
-- STARTS CURRENT_TIMESTAMP
-- DO
--   CALL CleanupAutoCompleteStats(90);

-- 10. 创建索引优化查询性能
-- 复合索引用于搜索
CREATE INDEX `idx_category_name_status` ON `auto_complete_item` (`category`, `name`, `status`);
CREATE INDEX `idx_category_helpchar_status` ON `auto_complete_item` (`category`, `help_char`, `status`);
CREATE INDEX `idx_category_pinyin_status` ON `auto_complete_item` (`category`, `pinyin`, `status`);

-- 用于热门排序的索引
CREATE INDEX `idx_category_usecount_sort` ON `auto_complete_item` (`category`, `use_count` DESC, `sort_order` ASC);

-- 统计表索引
CREATE INDEX `idx_stat_category_date_count` ON `auto_complete_usage_stat` (`category`, `use_date`, `use_count` DESC);
