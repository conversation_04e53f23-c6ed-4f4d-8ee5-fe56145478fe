package org.jeecg.modules.reg.enums;

/**
 * 字段显示位置枚举
 */
public enum FieldDisplayLocation {
    
    /**
     * 外部显示 - 在折叠面板外直接显示
     */
    OUTSIDE("outside", "外部显示"),
    
    /**
     * 折叠面板内 - 在可折叠区域内显示
     */
    COLLAPSE("collapse", "折叠面板内"),
    
    /**
     * 隐藏 - 不显示该字段
     */
    HIDDEN("hidden", "隐藏");
    
    private final String code;
    private final String description;
    
    FieldDisplayLocation(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static FieldDisplayLocation fromCode(String code) {
        for (FieldDisplayLocation location : values()) {
            if (location.getCode().equals(code)) {
                return location;
            }
        }
        return HIDDEN; // 默认隐藏
    }
    
    /**
     * 检查是否为有效的显示位置代码
     */
    public static boolean isValidCode(String code) {
        for (FieldDisplayLocation location : values()) {
            if (location.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
