package org.jeecg.modules.reg.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.reg.entity.FormDisplayConfig;
import org.jeecg.modules.reg.vo.FormDisplayConfigQueryParam;
import org.jeecg.modules.reg.vo.ConfigValidationResult;
import org.jeecg.modules.reg.vo.FieldConfigRequest;

import java.util.List;
import java.util.Map;

/**
 * 表单显示配置Service接口
 */
public interface IFormDisplayConfigService extends IService<FormDisplayConfig> {

    /**
     * 获取配置列表（分页）
     */
    IPage<FormDisplayConfig> getConfigList(Page<FormDisplayConfig> page, FormDisplayConfigQueryParam param);

    /**
     * 获取配置详情（包含字段）
     */
    FormDisplayConfig getConfigWithFields(String id);

    /**
     * 保存配置（包含字段）
     */
    boolean saveConfigWithFields(FormDisplayConfig config);

    /**
     * 获取当前生效的配置
     */
    FormDisplayConfig getActiveConfig(String formType);

    /**
     * 获取当前生效的配置（支持多中心）
     */
    FormDisplayConfig getActiveConfig(String formType, String centerId);

    /**
     * 获取默认配置模板
     */
    FormDisplayConfig getDefaultConfig(String formType);

    // ==================== 新增方法 ====================

    /**
     * 验证字段配置
     */
    ConfigValidationResult validateFieldConfig(FieldConfigRequest request);

    /**
     * 获取配置版本列表
     */
    List<FormDisplayConfig> getConfigVersions(String formType, String centerId);

    /**
     * 复制配置
     */
    String copyConfig(String sourceId, String newConfigName, String targetCenterId);

    /**
     * 激活配置
     */
    boolean activateConfig(String configId);

    /**
     * 获取用户字段配置权限
     */
    Map<String, Boolean> getUserFieldConfigPermissions(LoginUser loginUser);

    /**
     * 批量更新字段配置
     */
    boolean batchUpdateFieldConfigs(List<FieldConfigRequest> requests);

    /**
     * 导出配置
     */
    String exportConfig(String configId, String format);

    /**
     * 导入配置
     */
    String importConfig(String configData, String format, String targetCenterId);

    /**
     * 获取配置统计信息
     */
    Map<String, Object> getConfigStatistics(String formType, String centerId);
}
