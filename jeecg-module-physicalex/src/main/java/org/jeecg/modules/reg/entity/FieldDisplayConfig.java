package org.jeecg.modules.reg.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 字段显示配置
 */
@Data
@TableName("field_display_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "FieldDisplayConfig", description = "字段显示配置")
public class FieldDisplayConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 配置ID
     */
    @ApiModelProperty(value = "配置ID")
    private String configId;

    /**
     * 字段标识
     */
    @ApiModelProperty(value = "字段标识")
    private String fieldKey;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;

    /**
     * 是否显示（兼容字段）
     */
    @ApiModelProperty(value = "是否显示")
    private Boolean isVisible;

    /**
     * 显示位置：outside-外部显示，collapse-折叠面板内，hidden-隐藏
     */
    @ApiModelProperty(value = "显示位置")
    private String displayLocation;

    /**
     * 分组名称
     */
    @ApiModelProperty(value = "分组名称")
    private String groupName;

    /**
     * 排序号
     */
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**
     * 是否必填
     */
    @ApiModelProperty(value = "是否必填")
    private Boolean isRequired;

    /**
     * 字段描述
     */
    @ApiModelProperty(value = "字段描述")
    private String fieldDescription;
}