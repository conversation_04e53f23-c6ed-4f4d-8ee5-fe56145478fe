package org.jeecg.modules.reg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 表单配置查询参数
 */
@Data
@ApiModel("表单配置查询参数")
public class FormDisplayConfigQueryParam {

    @ApiModelProperty("配置名称")
    private String configName;

    @ApiModelProperty("表单类型")
    private String formType;

    @ApiModelProperty("是否启用")
    private Boolean isActive;

    @ApiModelProperty("体检中心ID")
    private String centerId;
}

