package org.jeecg.modules.reg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 配置验证结果
 */
@Data
@ApiModel("配置验证结果")
public class ConfigValidationResult {

    @ApiModelProperty("是否有效")
    private Boolean isValid;

    @ApiModelProperty("错误信息列表")
    private List<String> errors;

    @ApiModelProperty("警告信息列表")
    private List<String> warnings;

    @ApiModelProperty("建议信息列表")
    private List<String> suggestions;

    public ConfigValidationResult() {
    }

    public ConfigValidationResult(Boolean isValid, List<String> errors, List<String> warnings) {
        this.isValid = isValid;
        this.errors = errors;
        this.warnings = warnings;
    }

    public static ConfigValidationResult success() {
        ConfigValidationResult result = new ConfigValidationResult();
        result.setIsValid(true);
        return result;
    }

    public static ConfigValidationResult error(List<String> errors) {
        ConfigValidationResult result = new ConfigValidationResult();
        result.setIsValid(false);
        result.setErrors(errors);
        return result;
    }
}
