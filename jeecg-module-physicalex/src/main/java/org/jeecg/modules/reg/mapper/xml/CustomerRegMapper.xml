<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.reg.mapper.CustomerRegMapper">

    <select id="getItemGroupOfCustomer" resultType="org.jeecg.modules.reg.entity.CustomerRegItemGroup"
            parameterType="java.lang.String">
        select * from customer_reg_item_group where customer_reg_id = #{customerRegId} order by  create_time desc
    </select>
    <select id="list4GuidanceSheet" resultMap="CustomerRegItemGroupResultMap">
        select g.*,d.id as d_id,d.depart_name as d_depart_name,d.guide_sort as d_guid_sort,d.vip_address as d_vip_address,d.women_address as d_women_address,d.men_address as d_men_address,d.intro as d_intro,d.depart_fun_category as d_depart_fun_category from customer_reg_item_group g join sys_depart d on g.department_id=d.id where g.customer_reg_id = #{customerRegId} order by d.guide_sort asc
    </select>
    <select id="pageCustomerReg" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select * from customer_reg where del_flag=0 <if test="idCard!=null and idCard!=''"> and id_card=#{idCard}</if> <if test="examNo!=null and examNo!=''"> and exam_no=#{examNo}</if> <if test="companyRegId!=null and companyRegId!=''"> and company_reg_id=#{companyRegId}</if> <if test="teamId!=null and teamId!=''"> and team_id=#{teamId}</if> <if test="name!=null and name!=''"> and name like concat('%',#{name},'%') </if> <if test="status!=null and status!=''"> and status=#{status}</if> <if test="retrieveStatus!=null and retrieveStatus!=''"> and retrieve_status=#{retrieveStatus}</if> <if test="emplyee!=null and emplyee!=''"> and creator_by=#{emplyee}</if> <if test="paymentState!=null and paymentState!=''"> and payment_state=#{paymentState}</if>
        <if test="companyNotifyFlag!=null and companyNotifyFlag!=''"> and company_notify_flag=#{companyNotifyFlag}</if>
        <if test="customerId!=null and customerId!=''"> and customer_id=#{customerId}</if>
        <choose>
            <when test="dateType=='预约'">
                <if test="regTimeStart!=null and regTimeStart!=''"> and appointment_date <![CDATA[>=]]> #{regTimeStart}</if> <if test="regTimeEnd!=null and regTimeEnd!=''"> and appointment_date <![CDATA[<=]]> #{regTimeEnd}</if>
            </when>
            <when test="dateType=='登记'">
                <if test="regTimeStart!=null and regTimeStart!=''"> and reg_time <![CDATA[>=]]> #{regTimeStart}</if> <if test="regTimeEnd!=null and regTimeEnd!=''"> and reg_time <![CDATA[<=]]> #{regTimeEnd}</if>
            </when>
            <when test="dateType=='添加'">
                <if test="regTimeStart!=null and regTimeStart!=''"> and create_time <![CDATA[>=]]> #{regTimeStart}</if> <if test="regTimeEnd!=null and regTimeEnd!=''"> and create_time <![CDATA[<=]]> #{regTimeEnd}</if>
            </when>
        </choose>
        <choose>
            <when test="dateType=='预约'">
                order by appointment_sort desc
            </when>
            <when  test="dateType=='登记'">
                order by serial_no desc
            </when>
            <otherwise>
                order by create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getDepartsByUser" resultType="org.jeecg.modules.system.entity.SysDepart">
        select sd.* from sys_depart sd join sys_user_depart sud on sd.id = sud.dep_id where sud.user_id = #{userId}
    </select>

    <resultMap id="CustomerRegItemGroupResultMap" type="org.jeecg.modules.reg.entity.CustomerRegItemGroup">
        <id property="id" column="id" />
        <result property="customerRegId" column="customer_reg_id" />
        <result property="itemGroupId" column="item_group_id" />
        <result property="itemGroupName" column="item_group_name" />
        <result property="departmentId" column="department_id" />
        <result property="departmentName" column="department_name" />
        <result property="type" column="type" />
        <result property="itemSuitId" column="item_suit_id" />
        <result property="itemSuitName" column="item_suit_name" />
        <result property="addMinusFlag" column="add_minus_flag" />
        <result property="price" column="price" />
        <result property="disRate" column="dis_rate" />
        <result property="priceAfterDis" column="price_after_dis" />
        <result property="payerType" column="payer_type" />
        <result property="payStatus" column="pay_status" />
        <result property="checkTime" column="check_time" />
        <result property="checkStatus" column="check_status" />
        <result property="regBy" column="reg_by" />
        <result property="receiptId" column="receipt_id" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
        <result property="departFunction" column="d_depart_fun_category" />
        <result property="guideSort" column="d_guide_sort" />
        <association property="department" columnPrefix="d_" javaType="org.jeecg.modules.system.entity.SysDepart">
            <id property="id" column="id" />
            <result property="departName" column="depart_name" />
            <result property="guideSort" column="guid_sort" />
            <result property="vipAddress" column="vip_address" />
            <result property="womenAddress" column="women_address" />
            <result property="menAddress" column="men_address" />
            <result property="intro" column="intro" />
            <result property="departFunCategory" column="depart_fun_category" />
        </association>
    </resultMap>

    <select id="getCustomerRegsByCompanyRegId"
            resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select distinct  cr.*
        from customer_reg cr join customer_reg_item_group crig
                                  on cr.id=crig.customer_reg_id
        where cr.company_reg_id=#{companyRegId}
          <if test="companyDeptId !=null and companyDeptId!='' ">
              and cr.company_dept_id=#{companyDeptId}
          </if>
          <if test="jobStatus !=null and jobStatus!='' ">
              and cr.job_status=#{jobStatus}
          </if>
          and cr.del_flag!=1 and cr.status='已登记'
    </select>

    <select id="listByEReportStatus" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select id,name,gender,age,phone,id_card,exam_no,serial_no,e_report_url,audit_date from customer_reg
        <where>
            <if test="eReportStatus!=null and eReportStatus!=''">and e_report_status=#{eReportStatus}</if>
            <if test="paperReportStatus!=null and paperReportStatus!=''">and paper_report_status=#{paperReportStatus} and e_report_status='待发送'</if>
            and e_report_url is not null
        </where>
        limit #{limit}
    </select>
    <select id="getLiteById" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select id,name,gender,age,phone,id_card,customer_id,exam_no,serial_no,status,e_report_url,company_reg_id,team_id,limit_amount,origin_customer_limit_amount_id,origin_customer_idcard from customer_reg where id=#{id}
    </select>
    <select id="getLastNYearsReg" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select id,name,gender,exam_no,exam_category,reg_time,status from customer_reg where id_card=#{idCard} and status='已登记' and reg_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL #{years} YEAR) order by reg_time desc limit 5
    </select>
    <select id="getLastNYearsRegByCustomer" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select id,name,gender,exam_no,exam_category,reg_time,status,appointment_date,summary_status from customer_reg where customer_id=#{customerId}  <if test="status!=null"> and status=#{status} </if>  and create_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL #{years} YEAR) order by appointment_date desc
    </select>
    <select id="selectLatestReg" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select id,exam_no,summary_time from customer_reg where customer_id=#{customerId} and summary_status='审核通过' limit 1
    </select>
    <select id="selectLatestRegByTeamIdAndIdCard" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select id,exam_no,limit_amount,team_id,id_card,name from customer_reg where team_id=#{teamId} and id_card=#{idCard} and status='已登记' and del_flag!=1 order by reg_time desc limit 1
    </select>
    <select id="selectRelationRegsByOriginCustomerIdAndTeamId" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select r.id,r.name,r.exam_no from customer_reg r join team_customer_limit_amount a on a.id=r.origin_customer_limit_amount_id where a.customer_id=#{originCustomerId} and a.team_id=#{originTeamId} and r.status='已登记' and r.del_flag!=1
    </select>
    <select id="selectOriginRegsByRelationCustomerAccountId"
            resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select r.id,r.name,r.exam_no from customer_reg r join team_customer_limit_amount a on r.customer_id=a.customer_id and r.team_id=a.team_id where a.id=#{relationCustomerAccountId} and r.status='已登记' and r.del_flag!=1
    </select>
    <resultMap id="CustomerReg2OrderMap" type="org.jeecg.modules.reg.entity.CustomerReg">
        <id property="id" column="id" />
        <result property="id" column="id" />
        <result property="name" column="name" />
        <result property="examNo" column="exam_no" />
        <result property="status" column="status" />
        <result property="age" column="age" />
        <result property="gender" column="gender" />
        <result property="companyRegId" column="company_reg_id" />
        <result property="companyRegName" column="company_reg_name" />
        <result property="companyId" column="company_id" />
        <result property="companyName" column="company_name" />
        <result property="regTime" column="reg_tiem" />
        <association property="customerOrder" columnPrefix="o_" javaType="org.jeecg.modules.appointment.entity.CustomerOrder">
            <id property="id" column="id" />
            <result property="id" column="id" />
            <result property="orderNo" column="order_no" />
            <result property="bookTime" column="book_time" />
            <result property="payableAmount" column="payable_amount" />
            <result property="actualAmount" column="actual_amount" />
            <result property="payTime" column="pay_time" />
            <result property="status" column="status" />
            <result property="suitName" column="suit_name" />
        </association>
    </resultMap>

    <select id="getRegListByIdCardOrExamNo" resultMap="CustomerReg2OrderMap">
        SELECT r.id,r.exam_no,r.name,r.age,r.gender,r.status,r.id_card,r.company_reg_id,r.company_reg_name,
               r.create_time,r.company_id,r.company_name,r.reg_time,r.company_dept_id,r.company_dept_name,
            o.id as o_id,o.book_time as o_book_time,o.pay_time as o_pay_time,o.status as o_status,
            o.order_no as o_order_no,o.actual_amount as o_actual_amount,o.payable_amount as o_payable_amount,
            o.suit_name as o_suit_name FROM `customer_reg`  r left join customer_order o on r.id= o.customer_reg_id
        <where>
            r.create_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL 1 YEAR)
        <if test="idCard !=null and idCard!='' ">
            and r.id_card=#{idCard}
        </if>
        <if test="examNo !=null and examNo!='' ">
            and r.exam_no=#{examNo}
        </if>
        </where>
        order by r.create_time desc
    </select>
    <select id="getRegList4ReportByIdCardOrExamNo" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        SELECT r.id,r.exam_no,r.name,r.age,r.gender,r.status,r.id_card,r.company_reg_id,r.company_reg_name,
        r.create_time,r.company_id,r.company_name,r.reg_time,r.company_dept_id,r.company_dept_name,r.summary_time,
        r.summary_status,r.summary_audit_time,r.report_print_time,r.paper_report_status,r.e_report_status,r.e_report_url,
        r.exam_category
         FROM `customer_reg`  r
        <where>
            r.create_time <![CDATA[>=]]> DATE_SUB(NOW(), INTERVAL 1 YEAR)
            <if test="idCard !=null and idCard!='' ">
                and r.id_card=#{idCard}
            </if>
            <if test="examNo !=null and examNo!='' ">
                and r.exam_no=#{examNo}
            </if>
            <if test="status !=null and status!='' ">
                and r.status=#{status}
            </if>
        </where>
        order by r.create_time desc
    </select>
    <select id="pageCustomerReg4Occu" resultType="org.jeecg.modules.reg.entity.CustomerReg">
        select r.*,os.result_status as occuReportResultStatus,os.result_msg as occuReportResultMsg,os.cost_ms as occuReportCostMs,os.update_time as  occuReportUploadTime from customer_reg r left join occu_sync_record os on r.id=os.biz_id where r.del_flag=0 <if test="idCard!=null and idCard!=''"> and r.id_card=#{idCard}</if> <if test="examNo!=null and examNo!=''"> and r.exam_no=#{examNo}</if> <if test="companyRegId!=null and companyRegId!=''"> and r.company_reg_id=#{companyRegId}</if> <if test="teamId!=null and teamId!=''"> and r.team_id=#{teamId}</if> <if test="name!=null and name!=''"> and r.name like concat('%',#{name},'%') </if> <if test="status!=null and status!=''"> and r.status=#{status}</if> <if test="retrieveStatus!=null and retrieveStatus!=''"> and r.retrieve_status=#{retrieveStatus}</if> <if test="emplyee!=null and emplyee!=''"> and r.creator_by=#{emplyee}</if> <if test="paymentState!=null and paymentState!=''"> and r.payment_state=#{paymentState}</if>
        <if test="companyNotifyFlag!=null and companyNotifyFlag!=''"> and r.company_notify_flag=#{companyNotifyFlag}</if>
        <if test="customerId!=null and customerId!=''"> and r.customer_id=#{customerId}</if>
        <if test="riskFactor!=null and riskFactor!=''"> and r.risk_factor like concat('%',#{riskFactor},'%')</if>
        <if test="examCategory!=null and examCategory!=''"> and r.exam_category=#{examCategory}</if>
        <if test="occuReportResultStatus!=null and occuReportResultStatus!=''"> and os.result_status=#{occuReportResultStatus}</if>
        <if test="occuReportUploadTimeStart!=null and occuReportUploadTimeStart!=''"> and os.update_time <![CDATA[>=]]> #{occuReportUploadTimeStart}</if> <if test="occuReportUploadTimeEnd!=null and occuReportUploadTimeEnd!=''"> and os.update_time <![CDATA[<=]]> #{occuReportUploadTimeEnd}</if>
        <if test="summaryStatus!=null and summaryStatus!=''"> and r.summary_status=#{summaryStatus}</if>
        <choose>
            <when test="dateType=='预约'">
                <if test="regTimeStart!=null and regTimeStart!=''"> and r.appointment_date <![CDATA[>=]]> #{regTimeStart}</if> <if test="regTimeEnd!=null and regTimeEnd!=''"> and r.appointment_date <![CDATA[<=]]> #{regTimeEnd}</if>
            </when>
            <when test="dateType=='登记'">
                <if test="regTimeStart!=null and regTimeStart!=''"> and r.reg_time <![CDATA[>=]]> #{regTimeStart}</if> <if test="regTimeEnd!=null and regTimeEnd!=''"> and r.reg_time <![CDATA[<=]]> #{regTimeEnd}</if>
            </when>
            <when test="dateType=='添加'">
                <if test="regTimeStart!=null and regTimeStart!=''"> and r.create_time <![CDATA[>=]]> #{regTimeStart}</if> <if test="regTimeEnd!=null and regTimeEnd!=''"> and r.create_time <![CDATA[<=]]> #{regTimeEnd}</if>
            </when>
        </choose>
        <choose>
            <when test="dateType=='预约'">
                order by r.appointment_sort desc
            </when>
            <when  test="dateType=='登记'">
                order by r.serial_no desc
            </when>
            <otherwise>
                order by r.create_time desc
            </otherwise>
        </choose>
    </select>


</mapper>