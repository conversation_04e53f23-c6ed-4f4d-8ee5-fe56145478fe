package org.jeecg.modules.reg.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 字段配置请求
 */
@Data
@ApiModel("字段配置请求")
public class FieldConfigRequest {

    @ApiModelProperty("表单类型")
    private String formType;

    @ApiModelProperty("字段键")
    private String fieldKey;

    @ApiModelProperty("字段名称")
    private String fieldName;

    @ApiModelProperty("显示位置")
    private String displayLocation;

    @ApiModelProperty("分组名称")
    private String groupName;

    @ApiModelProperty("排序号")
    private Integer sortOrder;

    @ApiModelProperty("是否可见（兼容字段）")
    private Boolean isVisible;
}
