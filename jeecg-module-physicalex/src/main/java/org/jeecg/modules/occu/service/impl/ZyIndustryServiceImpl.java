package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.occu.entity.ZyIndustry;
import org.jeecg.modules.occu.mapper.ZyIndustryMapper;
import org.jeecg.modules.occu.service.IZyIndustryService;
import org.jeecg.modules.occu.vo.ZyIndustryLeafNodeVO;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.transaction.annotation.Transactional;
import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.stream.Collectors;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description: 行业类别
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Slf4j
@Service
@EnableCaching
public class ZyIndustryServiceImpl extends ServiceImpl<ZyIndustryMapper, ZyIndustry> implements IZyIndustryService {

	/** 缓存名称 */
	private static final String CACHE_NAME = "zyIndustryLeafNodes";

	@Override
	@CacheEvict(value = CACHE_NAME, allEntries = true)
	public void addZyIndustry(ZyIndustry zyIndustry) {
	   //新增时设置hasChild为0
	    zyIndustry.setHasChild(IZyIndustryService.NOCHILD);
		if(oConvertUtils.isEmpty(zyIndustry.getPid())){
			zyIndustry.setPid(IZyIndustryService.ROOT_PID_VALUE);
		}else{
			//如果当前节点父ID不为空 则设置父节点的hasChildren 为1
			ZyIndustry parent = baseMapper.selectById(zyIndustry.getPid());
			if(parent!=null && !"1".equals(parent.getHasChild())){
				parent.setHasChild("1");
				baseMapper.updateById(parent);
			}
		}
		baseMapper.insert(zyIndustry);
		log.info("新增行业节点，已清除叶子节点缓存: {}", zyIndustry.getName());
	}
	
	@Override
	@CacheEvict(value = CACHE_NAME, allEntries = true)
	public void updateZyIndustry(ZyIndustry zyIndustry) {
		ZyIndustry entity = this.getById(zyIndustry.getId());
		if(entity==null) {
			throw new JeecgBootException("未找到对应实体");
		}
		String old_pid = entity.getPid();
		String new_pid = zyIndustry.getPid();
		if(!old_pid.equals(new_pid)) {
			updateOldParentNode(old_pid);
			if(oConvertUtils.isEmpty(new_pid)){
				zyIndustry.setPid(IZyIndustryService.ROOT_PID_VALUE);
			}
			if(!IZyIndustryService.ROOT_PID_VALUE.equals(zyIndustry.getPid())) {
				baseMapper.updateTreeNodeStatus(zyIndustry.getPid(), IZyIndustryService.HASCHILD);
			}
		}
		baseMapper.updateById(zyIndustry);
		log.info("更新行业节点，已清除叶子节点缓存: {}", zyIndustry.getName());
	}
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	@CacheEvict(value = CACHE_NAME, allEntries = true)
	public void deleteZyIndustry(String id) throws JeecgBootException {
		//查询选中节点下所有子节点一并删除
        id = this.queryTreeChildIds(id);
        if(id.indexOf(",")>0) {
            StringBuffer sb = new StringBuffer();
            String[] idArr = id.split(",");
            for (String idVal : idArr) {
                if(idVal != null){
                    ZyIndustry zyIndustry = this.getById(idVal);
                    String pidVal = zyIndustry.getPid();
                    //查询此节点上一级是否还有其他子节点
                    List<ZyIndustry> dataList = baseMapper.selectList(new QueryWrapper<ZyIndustry>().eq("pid", pidVal).notIn("id",Arrays.asList(idArr)));
                    boolean flag = (dataList == null || dataList.size() == 0) && !Arrays.asList(idArr).contains(pidVal) && !sb.toString().contains(pidVal);
                    if(flag){
                        //如果当前节点原本有子节点 现在木有了，更新状态
                        sb.append(pidVal).append(",");
                    }
                }
            }
            //批量删除节点
            baseMapper.deleteBatchIds(Arrays.asList(idArr));
            //修改已无子节点的标识
            String[] pidArr = sb.toString().split(",");
            for(String pid : pidArr){
                this.updateOldParentNode(pid);
            }
        }else{
            ZyIndustry zyIndustry = this.getById(id);
            if(zyIndustry==null) {
                throw new JeecgBootException("未找到对应实体");
            }
            updateOldParentNode(zyIndustry.getPid());
            baseMapper.deleteById(id);
        }
        log.info("删除行业节点，已清除叶子节点缓存");
	}
	
	@Override
    public List<ZyIndustry> queryTreeListNoPage(QueryWrapper<ZyIndustry> queryWrapper) {
        List<ZyIndustry> dataList = baseMapper.selectList(queryWrapper);
        List<ZyIndustry> mapList = new ArrayList<>();
        for(ZyIndustry data : dataList){
            String pidVal = data.getPid();
            //递归查询子节点的根节点
            if(pidVal != null && !IZyIndustryService.NOCHILD.equals(pidVal)){
                ZyIndustry rootVal = this.getTreeRoot(pidVal);
                if(rootVal != null && !mapList.contains(rootVal)){
                    mapList.add(rootVal);
                }
            }else{
                if(!mapList.contains(data)){
                    mapList.add(data);
                }
            }
        }
        return mapList;
    }

    @Override
    public List<SelectTreeModel> queryListByCode(String parentCode) {
        String pid = ROOT_PID_VALUE;
        if (oConvertUtils.isNotEmpty(parentCode)) {
            LambdaQueryWrapper<ZyIndustry> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(ZyIndustry::getPid, parentCode);
            List<ZyIndustry> list = baseMapper.selectList(queryWrapper);
            if (list == null || list.size() == 0) {
                throw new JeecgBootException("该编码【" + parentCode + "】不存在，请核实!");
            }
            if (list.size() > 1) {
                throw new JeecgBootException("该编码【" + parentCode + "】存在多个，请核实!");
            }
            pid = list.get(0).getId();
        }
        return baseMapper.queryListByPid(pid, null);
    }

    @Override
    public List<SelectTreeModel> queryListByPid(String pid) {
        if (oConvertUtils.isEmpty(pid)) {
            pid = ROOT_PID_VALUE;
        }
        return baseMapper.queryListByPid(pid, null);
    }

	/**
	 * 根据所传pid查询旧的父级节点的子节点并修改相应状态值
	 * @param pid
	 */
	private void updateOldParentNode(String pid) {
		if(!IZyIndustryService.ROOT_PID_VALUE.equals(pid)) {
			Long count = baseMapper.selectCount(new QueryWrapper<ZyIndustry>().eq("pid", pid));
			if(count==null || count<=1) {
				baseMapper.updateTreeNodeStatus(pid, IZyIndustryService.NOCHILD);
			}
		}
	}

	/**
     * 递归查询节点的根节点
     * @param pidVal
     * @return
     */
    private ZyIndustry getTreeRoot(String pidVal){
        ZyIndustry data =  baseMapper.selectById(pidVal);
        if(data != null && !IZyIndustryService.ROOT_PID_VALUE.equals(data.getPid())){
            return this.getTreeRoot(data.getPid());
        }else{
            return data;
        }
    }

    /**
     * 根据id查询所有子节点id
     * @param ids
     * @return
     */
    private String queryTreeChildIds(String ids) {
        //获取id数组
        String[] idArr = ids.split(",");
        StringBuffer sb = new StringBuffer();
        for (String pidVal : idArr) {
            if(pidVal != null){
                if(!sb.toString().contains(pidVal)){
                    if(sb.toString().length() > 0){
                        sb.append(",");
                    }
                    sb.append(pidVal);
                    this.getTreeChildIds(pidVal,sb);
                }
            }
        }
        return sb.toString();
    }

    /**
     * 递归查询所有子节点
     * @param pidVal
     * @param sb
     * @return
     */
    private StringBuffer getTreeChildIds(String pidVal,StringBuffer sb){
        List<ZyIndustry> dataList = baseMapper.selectList(new QueryWrapper<ZyIndustry>().eq("pid", pidVal));
        if(dataList != null && dataList.size()>0){
            for(ZyIndustry tree : dataList) {
                if(!sb.toString().contains(tree.getId())){
                    sb.append(",").append(tree.getId());
                }
                this.getTreeChildIds(tree.getId(),sb);
            }
        }
        return sb;
    }

	@Override
	public List<ZyIndustryLeafNodeVO> getAllLeafNodes(String keyword) {
		// 获取所有叶子节点（带缓存）
		List<ZyIndustryLeafNodeVO> allNodes = getAllLeafNodesFromCache();

		// 如果有搜索关键词，进行过滤
		if (oConvertUtils.isNotEmpty(keyword)) {
			String lowerKeyword = keyword.toLowerCase();
			return allNodes.stream()
				.filter(node -> matchesKeyword(node, lowerKeyword))
				.collect(Collectors.toList());
		}

		return allNodes;
	}

	/**
	 * 从缓存获取所有叶子节点
	 */
	@Cacheable(value = CACHE_NAME, key = "'all'")
	public List<ZyIndustryLeafNodeVO> getAllLeafNodesFromCache() {
		log.info("从数据库查询叶子节点数据并缓存");
		return buildLeafNodesFromDatabase();
	}

	@Override
	@CacheEvict(value = CACHE_NAME, allEntries = true)
	public void refreshLeafNodesCache() {
		log.info("手动刷新叶子节点缓存");
		// 缓存会在下次调用getAllLeafNodesFromCache时重新构建
	}

	/**
	 * 从数据库构建叶子节点数据
	 */
	private List<ZyIndustryLeafNodeVO> buildLeafNodesFromDatabase() {
		// 查询所有行业数据
		List<ZyIndustry> allIndustries = baseMapper.selectList(
			new QueryWrapper<ZyIndustry>().orderByAsc("code", "name")
		);

		// 构建ID到实体的映射
		Map<String, ZyIndustry> industryMap = allIndustries.stream()
			.collect(Collectors.toMap(ZyIndustry::getId, industry -> industry));

		// 筛选叶子节点并构建VO
		List<ZyIndustryLeafNodeVO> leafNodes = new ArrayList<>();

		for (ZyIndustry industry : allIndustries) {
			// 判断是否为叶子节点
			if (NOCHILD.equals(industry.getHasChild())) {
				ZyIndustryLeafNodeVO leafNode = buildLeafNodeVO(industry, industryMap);
				leafNodes.add(leafNode);
			}
		}

		// 按层级和名称排序
		leafNodes.sort((a, b) -> {
			int levelCompare = Integer.compare(a.getLevel(), b.getLevel());
			if (levelCompare != 0) {
				return levelCompare;
			}
			return a.getName().compareTo(b.getName());
		});

		return leafNodes;
	}

	/**
	 * 构建叶子节点VO
	 */
	private ZyIndustryLeafNodeVO buildLeafNodeVO(ZyIndustry industry, Map<String, ZyIndustry> industryMap) {
		ZyIndustryLeafNodeVO vo = new ZyIndustryLeafNodeVO();
		vo.setId(industry.getId());
		vo.setName(industry.getName());
		vo.setCode(industry.getCode());
		vo.setHelpChar(industry.getHelpChar());
		vo.setPid(industry.getPid());

		// 构建父级路径
		List<String> parentNames = buildParentNames(industry.getPid(), industryMap);
		vo.setParentNames(parentNames);

		// 构建显示路径
		if (parentNames.isEmpty()) {
			vo.setFullPath(industry.getName());
			vo.setParentPath("");
		} else {
			vo.setFullPath(industry.getName() + " - " + String.join(" > ", parentNames));
			vo.setParentPath(String.join(" > ", parentNames));
		}

		vo.setLevel(parentNames.size() + 1);
		vo.setSortWeight(calculateSortWeight(vo.getLevel()));

		return vo;
	}

	/**
	 * 递归构建父级名称列表
	 */
	private List<String> buildParentNames(String pid, Map<String, ZyIndustry> industryMap) {
		List<String> parentNames = new ArrayList<>();

		if (oConvertUtils.isNotEmpty(pid) && !ROOT_PID_VALUE.equals(pid)) {
			ZyIndustry parent = industryMap.get(pid);
			if (parent != null) {
				// 递归获取更上级的父级名称
				List<String> grandParentNames = buildParentNames(parent.getPid(), industryMap);
				parentNames.addAll(grandParentNames);
				// 添加当前父级名称
				parentNames.add(parent.getName());
			}
		}

		return parentNames;
	}

	/**
	 * 计算排序权重
	 */
	private Integer calculateSortWeight(Integer level) {
		// 层级越深，权重越高（优先显示更具体的分类）
		return level * 100;
	}

	/**
	 * 检查节点是否匹配关键词
	 */
	private boolean matchesKeyword(ZyIndustryLeafNodeVO node, String keyword) {
		if (oConvertUtils.isEmpty(keyword)) {
			return true;
		}

		// 检查名称
		if (oConvertUtils.isNotEmpty(node.getName()) &&
			node.getName().toLowerCase().contains(keyword)) {
			return true;
		}

		// 检查代码
		if (oConvertUtils.isNotEmpty(node.getCode()) &&
			node.getCode().toLowerCase().contains(keyword)) {
			return true;
		}

		// 检查助记码
		if (oConvertUtils.isNotEmpty(node.getHelpChar()) &&
			node.getHelpChar().toLowerCase().contains(keyword)) {
			return true;
		}

		// 检查父级路径
		if (oConvertUtils.isNotEmpty(node.getParentPath()) &&
			node.getParentPath().toLowerCase().contains(keyword)) {
			return true;
		}

		// 检查父级名称列表
		if (node.getParentNames() != null) {
			for (String parentName : node.getParentNames()) {
				if (oConvertUtils.isNotEmpty(parentName) &&
					parentName.toLowerCase().contains(keyword)) {
					return true;
				}
			}
		}

		return false;
	}

}
