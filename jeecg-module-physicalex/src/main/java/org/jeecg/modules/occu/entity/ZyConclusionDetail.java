package org.jeecg.modules.occu.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 职业检总检详细
 * @Author: jeecg-boot
 * @Date:   2024-05-31
 * @Version: V1.0
 */
@Data
@TableName("zy_conclusion_detail")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="zy_conclusion_detail对象", description="职业检总检详细")
public class ZyConclusionDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
	/**登记ID*/
	@Excel(name = "登记ID", width = 15)
    @ApiModelProperty(value = "登记ID")
    private java.lang.String customerRegId;
	/**危害因素*/
	@Excel(name = "危害因素", width = 15)
    @ApiModelProperty(value = "危害因素")
    private java.lang.String riskFactor;
    /**危害因素ID*/
    @Excel(name = "危害因素ID", width = 15)
    @ApiModelProperty(value = "危害因素ID")
    private String riskFactorId;
	/**工种*/
	@Excel(name = "工种", width = 15)
    @ApiModelProperty(value = "工种")
    private java.lang.String workType;
    /**结论依据*/
    @Excel(name = "结论依据", width = 15)
    @ApiModelProperty(value = "结论依据")
    private java.lang.String according;
	/**职业检结论*/
	@Excel(name = "职业检结论", width = 15)
    @ApiModelProperty(value = "职业检结论")
    private java.lang.String conclusion;
	/**处理意见*/
	@Excel(name = "处理意见", width = 15)
    @ApiModelProperty(value = "处理意见")
    private java.lang.String advice;
	/**职业病*/
	@Excel(name = "职业病", width = 15)
    @ApiModelProperty(value = "职业病")
    private java.lang.String zyDisease;
	/**职业禁忌证*/
	@Excel(name = "职业禁忌证", width = 15)
    @ApiModelProperty(value = "职业禁忌证")
    private java.lang.String zySymptom;
	/**创建人账号*/
    @ApiModelProperty(value = "创建人账号")
    private java.lang.String createBy;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private java.util.Date createTime;
	/**总检ID*/
	@Excel(name = "总检ID", width = 15)
    @ApiModelProperty(value = "总检ID")
    private java.lang.String summaryId;
    /**主要标志*/
    @Excel(name = "主要标志", width = 15)
    @ApiModelProperty(value = "主要标志")
    private String mainFlag;

    @TableLogic
    private String delFlag;

    @TableField(exist = false)
    private String conclusionText;
    @TableField(exist = false)
    private String riskFactorText;
    @TableField(exist = false)
    private String workTypeText;
    @TableField(exist = false)
    private String zyDiseaseText;
    @TableField(exist = false)
    private String zySymptomText;

}
