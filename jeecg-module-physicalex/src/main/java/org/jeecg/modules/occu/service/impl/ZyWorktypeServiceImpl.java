package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.occu.entity.ZyRiskFactor;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.mapper.ZyRiskFactorMapper;
import org.jeecg.modules.occu.mapper.ZyWorktypeMapper;
import org.jeecg.modules.occu.mapper.ZyWorktypeRiskFactorMapper;
import org.jeecg.modules.occu.service.IZyWorktypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Service
public class ZyWorktypeServiceImpl extends ServiceImpl<ZyWorktypeMapper, ZyWorktype> implements IZyWorktypeService {

    @Autowired
    private ZyWorktypeMapper zyWorktypeMapper;
    @Autowired
    private ZyWorktypeRiskFactorMapper zyWorktypeRiskFactorMapper;
    @Autowired
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(ZyWorktype zyWorktype, List<ZyRiskFactorWorktype> zyWorktypeRiskFactorList) {
        zyWorktypeMapper.insert(zyWorktype);
        if(zyWorktypeRiskFactorList != null && !zyWorktypeRiskFactorList.isEmpty()) {
            for(ZyRiskFactorWorktype entity : zyWorktypeRiskFactorList) {
                //外键设置
                entity.setWorktypeId(zyWorktype.getId());
                zyWorktypeRiskFactorMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(ZyWorktype zyWorktype, List<ZyRiskFactorWorktype> zyWorktypeRiskFactorList) {
        zyWorktypeMapper.updateById(zyWorktype);

        //1.删除子表数据
        zyWorktypeRiskFactorMapper.deleteByWorktypeId(zyWorktype.getId());

        //2.子表数据重新插入
        if(zyWorktypeRiskFactorList != null && !zyWorktypeRiskFactorList.isEmpty()) {
            for(ZyRiskFactorWorktype entity : zyWorktypeRiskFactorList) {
                //外键设置
                entity.setWorktypeId(zyWorktype.getId());
                zyWorktypeRiskFactorMapper.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        //1.删除子表数据
        zyWorktypeRiskFactorMapper.deleteByWorktypeId(id);
        //2.删除主表数据
        zyWorktypeMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addRiskFactorsBatch(String worktypeId, List<String> riskFactorIds) {
        if (riskFactorIds != null && !riskFactorIds.isEmpty()) {
            ZyWorktype zyWorktype = zyWorktypeMapper.selectById(worktypeId);
            if (zyWorktype == null) {
                return;
            }
            for (String riskFactorId : riskFactorIds) {
                // 检查是否已存在关联关系，避免重复添加
                List<ZyRiskFactorWorktype> existingList = zyWorktypeRiskFactorMapper.selectByWorktypeIdAndRiskFactorId(worktypeId, riskFactorId);
                if (existingList == null || existingList.isEmpty()) {
                    // 先查询危害因素的详细信息
                    ZyRiskFactor riskFactor = zyWorktypeRiskFactorMapper.selectRiskFactorById(riskFactorId);
                    if (riskFactor != null) {
                        ZyRiskFactorWorktype relation = new ZyRiskFactorWorktype();
                        relation.setWorktypeId(worktypeId);
                        relation.setWorktypeCode(zyWorktype.getCode());
                        relation.setWorktypeName(zyWorktype.getName());
                        relation.setFactorId(riskFactorId);
                        relation.setFactorCode(riskFactor.getCode());
                        relation.setFactorName(riskFactor.getName());

                        zyWorktypeRiskFactorMapper.insert(relation);
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeRiskFactorsBatch(String worktypeId, List<String> riskFactorIds) {
        if (riskFactorIds != null && !riskFactorIds.isEmpty()) {
            zyWorktypeRiskFactorMapper.deleteByWorktypeIdAndRiskFactorIds(worktypeId, riskFactorIds);
        }
    }

    @Override
    public List<?> getAvailableRiskFactors() {
        // 获取所有可用的危害因素
        return zyRiskFactorMapper.selectList(new LambdaQueryWrapper<ZyRiskFactor>()
                .orderByAsc(ZyRiskFactor::getCode));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyRiskFactorsToWorktypes(String sourceWorktypeId, List<String> targetWorktypeIds) {
        if (targetWorktypeIds == null || targetWorktypeIds.isEmpty()) {
            return;
        }

        // 获取源工种的所有危害因素关联
        List<ZyRiskFactorWorktype> sourceRiskFactors = zyWorktypeRiskFactorMapper.selectList(
                new LambdaQueryWrapper<ZyRiskFactorWorktype>()
                        .eq(ZyRiskFactorWorktype::getWorktypeId, sourceWorktypeId));

        if (sourceRiskFactors.isEmpty()) {
            return;
        }

        // 为每个目标工种复制危害因素关联
        for (String targetWorktypeId : targetWorktypeIds) {
            // 先删除目标工种现有的危害因素关联
            zyWorktypeRiskFactorMapper.deleteByWorktypeId(targetWorktypeId);
            ZyWorktype targetWorktype = zyWorktypeMapper.selectById(targetWorktypeId);

            // 复制源工种的危害因素关联
            for (ZyRiskFactorWorktype sourceRiskFactor : sourceRiskFactors) {
                ZyRiskFactorWorktype newRiskFactor = new ZyRiskFactorWorktype();
                newRiskFactor.setWorktypeId(targetWorktypeId);
                newRiskFactor.setWorktypeName(targetWorktype.getName());
                newRiskFactor.setWorktypeCode(targetWorktype.getCode());

                newRiskFactor.setFactorId(sourceRiskFactor.getFactorId());
                newRiskFactor.setFactorName(sourceRiskFactor.getFactorName());
                newRiskFactor.setFactorCode(sourceRiskFactor.getFactorCode());

                zyWorktypeRiskFactorMapper.insert(newRiskFactor);
            }
        }
    }

    @Override
    public ZyWorktype getWorktypeWithRiskFactors(String worktypeId) {
        if (worktypeId == null || worktypeId.trim().isEmpty()) {
            return null;
        }

        // 获取工种基本信息
        ZyWorktype worktype = zyWorktypeMapper.selectById(worktypeId);
        if (worktype == null) {
            return null;
        }

        // 获取关联的危害因素列表
        List<ZyRiskFactorWorktype> riskFactorList = zyWorktypeRiskFactorMapper.selectList(
                new LambdaQueryWrapper<ZyRiskFactorWorktype>()
                        .eq(ZyRiskFactorWorktype::getWorktypeId, worktypeId)
                        .orderByAsc(ZyRiskFactorWorktype::getWorktypeCode));

        worktype.setRiskFactorList(riskFactorList);
        return worktype;
    }

    @Override
    public List<ZyWorktype> autoCompleteSearch(String keyword, Integer limit, String searchType) {
        // 设置默认值
        if (limit == null || limit <= 0) {
            limit = 10;
        }
        if (limit > 50) {
            limit = 50; // 限制最大返回数量
        }
        if (searchType == null) {
            searchType = "both";
        }

        // 尝试从缓存获取
        String cacheKey = "worktype:autocomplete:" + searchType + ":" + (keyword != null ? keyword : "popular") + ":" + limit;
        try {
            @SuppressWarnings("unchecked")
            List<ZyWorktype> cachedResult = (List<ZyWorktype>) redisTemplate.opsForValue().get(cacheKey);
            if (cachedResult != null) {
                return cachedResult;
            }
        } catch (Exception e) {
            // 缓存异常时继续执行数据库查询
        }

        QueryWrapper<ZyWorktype> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("enable_flag", 1);

        // 根据搜索类型和关键词构建查询条件
        if (StringUtils.hasText(keyword)) {
            switch (searchType.toLowerCase()) {
                case "name":
                    queryWrapper.like("name", keyword);
                    break;
                case "helpchar":
                    queryWrapper.like("help_char", keyword);
                    break;
                case "both":
                default:
                    queryWrapper.and(wrapper -> wrapper
                            .like("name", keyword)
                            .or()
                            .like("help_char", keyword));
                    break;
            }
            // 有关键词时按使用频次和名称排序
            queryWrapper.orderByDesc("use_count").orderByAsc("name");
        } else {
            // 无关键词时返回热门工种（按使用频次排序）
            queryWrapper.orderByDesc("use_count").orderByAsc("sort").orderByAsc("name");
        }

        queryWrapper.last("LIMIT " + limit);

        List<ZyWorktype> result = zyWorktypeMapper.selectList(queryWrapper);

        // 将结果缓存5分钟
        try {
            redisTemplate.opsForValue().set(cacheKey, result, 5, TimeUnit.MINUTES);
        } catch (Exception e) {
            // 缓存异常不影响正常功能
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUseCount(String id) {
        if (!StringUtils.hasText(id)) {
            return;
        }

        // 更新使用频次
        ZyWorktype worktype = zyWorktypeMapper.selectById(id);
        if (worktype != null) {
            Integer currentCount = worktype.getUseCount();
            if (currentCount == null) {
                currentCount = 0;
            }
            worktype.setUseCount(currentCount + 1);
            zyWorktypeMapper.updateById(worktype);

            // 清除相关缓存
            try {
                String pattern = "worktype:autocomplete:*";
                redisTemplate.delete(redisTemplate.keys(pattern));
            } catch (Exception e) {
                // 缓存清除异常不影响正常功能
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ZyWorktype autoCreateWorktype(ZyWorktype worktype) {
        if (worktype == null || !StringUtils.hasText(worktype.getName())) {
            throw new IllegalArgumentException("工种名称不能为空");
        }

        // 检查是否已存在同名工种
        QueryWrapper<ZyWorktype> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", worktype.getName().trim());
        queryWrapper.eq("enable_flag", 1);
        ZyWorktype existingWorktype = zyWorktypeMapper.selectOne(queryWrapper);

        if (existingWorktype != null) {
            throw new IllegalArgumentException("工种名称已存在");
        }

        // 自动生成代码
        if (!StringUtils.hasText(worktype.getCode())) {
            worktype.setCode(generateWorktypeCode(worktype.getName()));
        }

        // 设置默认值
        if (worktype.getEnableFlag() == null) {
            worktype.setEnableFlag(1);
        }
        if (worktype.getSort() == null) {
            worktype.setSort(999);
        }
        if (worktype.getUseCount() == null) {
            worktype.setUseCount(1);
        }

        // 保存工种
        zyWorktypeMapper.insert(worktype);

        // 清除相关缓存
        try {
            String pattern = "worktype:autocomplete:*";
            redisTemplate.delete(redisTemplate.keys(pattern));
        } catch (Exception e) {
            // 缓存清除异常不影响正常功能
        }

        return worktype;
    }

    /**
     * 生成工种代码
     * @param name 工种名称
     * @return 工种代码
     */
    private String generateWorktypeCode(String name) {
        if (!StringUtils.hasText(name)) {
            return "AUTO001";
        }

        // 简单的代码生成逻辑，可以根据需要优化
        String baseCode = name.length() > 2 ? name.substring(0, 2) : name;

        // 查找最大的序号
        QueryWrapper<ZyWorktype> queryWrapper = new QueryWrapper<>();
        queryWrapper.likeRight("code", baseCode);
        queryWrapper.orderByDesc("code");
        queryWrapper.last("LIMIT 1");

        ZyWorktype lastWorktype = zyWorktypeMapper.selectOne(queryWrapper);
        int nextNumber = 1;

        if (lastWorktype != null && StringUtils.hasText(lastWorktype.getCode())) {
            String lastCode = lastWorktype.getCode();
            try {
                String numberPart = lastCode.replaceAll("\\D", "");
                if (StringUtils.hasText(numberPart)) {
                    nextNumber = Integer.parseInt(numberPart) + 1;
                }
            } catch (NumberFormatException e) {
                // 如果解析失败，使用默认值1
            }
        }

        return baseCode + String.format("%03d", nextNumber);
    }
}
