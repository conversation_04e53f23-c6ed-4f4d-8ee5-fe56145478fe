package org.jeecg.modules.occu.service;

import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
public interface IZyWorktypeService extends IService<ZyWorktype> {

    /**
     * 保存工种及其关联的危害因素
     * @param zyWorktype 工种信息
     * @param zyWorktypeRiskFactorList 关联的危害因素列表
     */
    public void saveMain(ZyWorktype zyWorktype, List<ZyRiskFactorWorktype> zyWorktypeRiskFactorList);

    /**
     * 更新工种及其关联的危害因素
     * @param zyWorktype 工种信息
     * @param zyWorktypeRiskFactorList 关联的危害因素列表
     */
    public void updateMain(ZyWorktype zyWorktype, List<ZyRiskFactorWorktype> zyWorktypeRiskFactorList);

    /**
     * 删除工种及其关联的危害因素
     * @param id 工种ID
     */
    public void delMain(String id);
    /**
     * 为工种批量添加危害因素关联
     * @param worktypeId 工种ID
     * @param riskFactorIds 危害因素ID列表
     */
    public void addRiskFactorsBatch(String worktypeId, List<String> riskFactorIds);

    /**
     * 为工种批量移除危害因素关联
     * @param worktypeId 工种ID
     * @param riskFactorIds 危害因素ID列表
     */
    public void removeRiskFactorsBatch(String worktypeId, List<String> riskFactorIds);

    /**
     * 获取所有可用的危害因素
     * @return 危害因素列表
     */
    public List<?> getAvailableRiskFactors();

    /**
     * 复制工种的危害因素关联到其他工种
     * @param sourceWorktypeId 源工种ID
     * @param targetWorktypeIds 目标工种ID列表
     */
    public void copyRiskFactorsToWorktypes(String sourceWorktypeId, List<String> targetWorktypeIds);

    /**
     * 获取工种详情（包含关联的危害因素）
     * @param worktypeId 工种ID
     * @return 工种详情
     */
    public ZyWorktype getWorktypeWithRiskFactors(String worktypeId);

    /**
     * 工种自动完成搜索
     * @param keyword 搜索关键词
     * @param limit 返回数量限制
     * @param searchType 搜索类型：name(名称)、helpChar(助记码)、both(两者)、popular(热门)
     * @return 搜索结果
     */
    public List<ZyWorktype> autoCompleteSearch(String keyword, Integer limit, String searchType);

    /**
     * 更新工种使用频次
     * @param id 工种ID
     */
    public void updateUseCount(String id);

    /**
     * 自动创建工种
     * @param worktype 工种信息
     * @return 创建的工种
     */
    public ZyWorktype autoCreateWorktype(ZyWorktype worktype);
}
