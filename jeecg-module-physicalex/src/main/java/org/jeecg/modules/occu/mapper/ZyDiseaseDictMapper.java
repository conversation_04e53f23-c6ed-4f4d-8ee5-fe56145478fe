package org.jeecg.modules.occu.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyDiseaseDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 疑似职业病字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
public interface ZyDiseaseDictMapper extends BaseMapper<ZyDiseaseDict> {

    ZyDiseaseDict getByCode(@Param("code") String code);

}
