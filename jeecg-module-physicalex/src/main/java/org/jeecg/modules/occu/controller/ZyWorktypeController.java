package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.occu.entity.ZyWorktype;
import org.jeecg.modules.occu.entity.ZyRiskFactorWorktype;
import org.jeecg.modules.occu.service.IZyWorktypeService;
import org.jeecg.modules.occu.service.IZyWorktypeRiskFactorService;
import org.jeecg.modules.occu.vo.ZyWorktypePage;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.BeanUtils;

 /**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Api(tags="工种")
@RestController
@RequestMapping("/occu/zyWorktype")
@Slf4j
public class ZyWorktypeController extends JeecgController<ZyWorktype, IZyWorktypeService> {
	@Autowired
	private IZyWorktypeService zyWorktypeService;
	@Autowired
	private IZyWorktypeRiskFactorService zyWorktypeRiskFactorService;
	
	/**
	 * 分页列表查询
	 *
	 * @param zyWorktype
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "工种-分页列表查询")
	@ApiOperation(value="工种-分页列表查询", notes="工种-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<ZyWorktype>> queryPageList(ZyWorktype zyWorktype,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<ZyWorktype> queryWrapper = QueryGenerator.initQueryWrapper(zyWorktype, req.getParameterMap());
		Page<ZyWorktype> page = new Page<ZyWorktype>(pageNo, pageSize);
		IPage<ZyWorktype> pageList = zyWorktypeService.page(page, queryWrapper);
		return Result.OK(pageList);
	}
	
	/**
	 *   添加
	 *
	 * @param zyWorktypePage
	 * @return
	 */
	@AutoLog(value = "工种-添加")
	@ApiOperation(value="工种-添加", notes="工种-添加")
	@RequiresPermissions("occu:zy_worktype:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyWorktypePage zyWorktypePage) {
		ZyWorktype zyWorktype = new ZyWorktype();
		BeanUtils.copyProperties(zyWorktypePage, zyWorktype);
		zyWorktypeService.saveMain(zyWorktype, zyWorktypePage.getZyWorktypeRiskFactorList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyWorktypePage
	 * @return
	 */
	@AutoLog(value = "工种-编辑")
	@ApiOperation(value="工种-编辑", notes="工种-编辑")
	@RequiresPermissions("occu:zy_worktype:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyWorktypePage zyWorktypePage) {
		ZyWorktype zyWorktype = new ZyWorktype();
		BeanUtils.copyProperties(zyWorktypePage, zyWorktype);
		ZyWorktype zyWorktypeEntity = zyWorktypeService.getById(zyWorktype.getId());
		if(zyWorktypeEntity == null) {
			return Result.error("未找到对应数据");
		}
		zyWorktypeService.updateMain(zyWorktype, zyWorktypePage.getZyWorktypeRiskFactorList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "工种-通过id删除")
	@ApiOperation(value="工种-通过id删除", notes="工种-通过id删除")
	@RequiresPermissions("occu:zy_worktype:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyWorktypeService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "工种-批量删除")
	@ApiOperation(value="工种-批量删除", notes="工种-批量删除")
	@RequiresPermissions("occu:zy_worktype:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		String[] idArray = ids.split(",");
		for(String id : idArray) {
			zyWorktypeService.delMain(id);
		}
		return Result.OK("批量删除成功!");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "工种-通过id查询")
	@ApiOperation(value="工种-通过id查询", notes="工种-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyWorktype> queryById(@RequestParam(name="id",required=true) String id) {
		ZyWorktype zyWorktype = zyWorktypeService.getById(id);
		if(zyWorktype==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyWorktype);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyWorktype
    */
    @RequiresPermissions("occu:zy_worktype:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyWorktype zyWorktype) {
        return super.exportXls(request, zyWorktype, ZyWorktype.class, "工种");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_worktype:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, ZyWorktype.class);
    }

	/**
	 * 根据工种ID查询关联的危害因素列表
	 *
	 * @param worktypeId 工种ID
	 * @return 危害因素列表
	 */
	@AutoLog(value = "工种-查询关联的危害因素")
	@ApiOperation(value="工种-询关联的危害因素", notes="工种-查询关联的危害因素")
	@GetMapping(value = "/queryRiskFactorsByWorktypeId")
	public Result<List<ZyRiskFactorWorktype>> queryRiskFactorsByWorktypeId(@RequestParam(name="worktypeId",required=true) String worktypeId) {
		List<ZyRiskFactorWorktype> list = zyWorktypeRiskFactorService.selectByWorktypeId(worktypeId);
		return Result.OK(list);
	}

	/**
	 * 为工种批量添加危害因素关联
	 *
	 * @param requestData 请求数据
	 * @return 操作结果
	 */
	@AutoLog(value = "工种-批量添��危害因素关联")
	@ApiOperation(value="工种-批量添加危害因素关联", notes="工种-批量添加危害因素关联")
	@PostMapping(value = "/addRiskFactorsBatch")
	public Result<String> addRiskFactorsBatch(@RequestBody Map<String, String> requestData) {
		try {
			String worktypeId = requestData.get("worktypeId");
			String riskFactorIds = requestData.get("riskFactorIds");

			if (worktypeId == null || worktypeId.trim().isEmpty()) {
				return Result.error("工种ID不能为空");
			}
			if (riskFactorIds == null || riskFactorIds.trim().isEmpty()) {
				return Result.error("危害因素ID列表不能为空");
			}

			String[] idArray = riskFactorIds.split(",");
			zyWorktypeService.addRiskFactorsBatch(worktypeId, Arrays.asList(idArray));
			return Result.OK("批量添加危害因素关联成功！");
		} catch (Exception e) {
			log.error("批量添加危害因素关联失败", e);
			return Result.error("批量添加危害因素关联失败：" + e.getMessage());
		}
	}

	/**
	 * 为工种批量移除危害因素关联
	 *
	 * @param requestData 请求数据
	 * @return 操作结果
	 */
	@AutoLog(value = "工种-批量移除危害因素关联")
	@ApiOperation(value="工种-批量移除危害因素关联", notes="工种-批量移除危害因素关联")
	@PostMapping(value = "/removeRiskFactorsBatch")
	public Result<String> removeRiskFactorsBatch(@RequestBody Map<String, String> requestData) {
		try {
			String worktypeId = requestData.get("worktypeId");
			String riskFactorIds = requestData.get("riskFactorIds");

			if (worktypeId == null || worktypeId.trim().isEmpty()) {
				return Result.error("工种ID不能为空");
			}
			if (riskFactorIds == null || riskFactorIds.trim().isEmpty()) {
				return Result.error("危害因素ID列表不能为空");
			}

			String[] idArray = riskFactorIds.split(",");
			zyWorktypeService.removeRiskFactorsBatch(worktypeId, Arrays.asList(idArray));
			return Result.OK("批量移除危害因素关联成功！");
		} catch (Exception e) {
			log.error("批量移除危害因素关联失败", e);
			return Result.error("批量移除危害因素关联失败：" + e.getMessage());
		}
	}

	/**
	 * 获取所有可用的危害因素（用于工种关联选择）
	 *
	 * @return 危害因素列表
	 */
	@AutoLog(value = "工种-获取可用危害因素列表")
	@ApiOperation(value="工种-获取可用危害因素列表", notes="工种-获取可用危害因素列表")
	@GetMapping(value = "/getAvailableRiskFactors")
	public Result<?> getAvailableRiskFactors() {
		try {
			List<?> riskFactors = zyWorktypeService.getAvailableRiskFactors();
			return Result.OK(riskFactors);
		} catch (Exception e) {
			log.error("获取可用危害因素列表失败", e);
			return Result.error("获取可用危害因素列表失败：" + e.getMessage());
		}
	}

	/**
	 * 复制工种的危害因素关联到其他工种
	 * @return 操作结果
	 */
	@AutoLog(value = "工种-复制危害因素关联")
	@ApiOperation(value="工种-复制危害因素关联", notes="工种-复制危害因素关联")
	@PostMapping(value = "/copyRiskFactorsToWorktypes")
	public Result<String> copyRiskFactorsToWorktypes(@RequestBody JSONObject info) {
		try {
			String sourceWorktypeId = info.getString("sourceWorktypeId");
			String targetWorktypeIds = info.getString("targetWorktypeIds");
			String[] idArray = targetWorktypeIds.split(",");
			zyWorktypeService.copyRiskFactorsToWorktypes(sourceWorktypeId, Arrays.asList(idArray));
			return Result.OK("复制危害因素关联成功！");
		} catch (Exception e) {
			log.error("复制危害因素关联失败", e);
			return Result.error("复制危害因素关联失败：" + e.getMessage());
		}
	}

	/**
	 * 工种自动完成搜索
	 * @param keyword 搜索关键词
	 * @param limit 返回数量限制
	 * @param searchType 搜索类型：name(名称)、helpChar(助记码)、both(两者)、popular(热门)
	 * @return 搜索结果
	 */
	@AutoLog(value = "工种-自动完成搜索")
	@ApiOperation(value="工种-自动完成搜索", notes="工种-自动完成搜索")
	@GetMapping(value = "/autoComplete")
	public Result<?> autoComplete(
			@RequestParam(name="keyword", defaultValue="") String keyword,
			@RequestParam(name="limit", defaultValue="10") Integer limit,
			@RequestParam(name="searchType", defaultValue="both") String searchType) {
		try {
			List<ZyWorktype> result = zyWorktypeService.autoCompleteSearch(keyword, limit, searchType);
			return Result.OK(result);
		} catch (Exception e) {
			log.error("工种自动完成搜索失败", e);
			return Result.error("工种自动完成搜索失败：" + e.getMessage());
		}
	}

	/**
	 * 更新工种使用频次
	 * @param id 工种ID
	 * @return 操作结果
	 */
	@AutoLog(value = "工种-更新使用频次")
	@ApiOperation(value="工种-更新使用频次", notes="工种-更新使用频次")
	@PostMapping(value = "/updateUseCount")
	public Result<String> updateUseCount(@RequestParam(name="id", required=true) String id) {
		try {
			zyWorktypeService.updateUseCount(id);
			return Result.OK("更新使用频次成功！");
		} catch (Exception e) {
			log.error("更新使用频次失败", e);
			return Result.error("更新使用频次失败：" + e.getMessage());
		}
	}

	/**
	 * 自动创建工种
	 * @param worktype 工种信息
	 * @return 创建结果
	 */
	@AutoLog(value = "工种-自动创建")
	@ApiOperation(value="工种-自动创建", notes="工种-自动创建")
	@PostMapping(value = "/autoCreate")
	public Result<?> autoCreate(@RequestBody ZyWorktype worktype) {
		try {
			// 检查是否已存在同名工种
			QueryWrapper<ZyWorktype> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("name", worktype.getName());
			queryWrapper.eq("enable_flag", 1);
			ZyWorktype existingWorktype = zyWorktypeService.getOne(queryWrapper);

			if (existingWorktype != null) {
				return Result.error("工种名称已存在");
			}

			// 自动生成代码
			if (worktype.getCode() == null || worktype.getCode().trim().isEmpty()) {
				worktype.setCode(generateWorktypeCode(worktype.getName()));
			}

			// 设置默认值
			if (worktype.getEnableFlag() == null) {
				worktype.setEnableFlag(1);
			}
			if (worktype.getSort() == null) {
				worktype.setSort(999);
			}
			if (worktype.getUseCount() == null) {
				worktype.setUseCount(1);
			}

			zyWorktypeService.save(worktype);
			return Result.OK(worktype);
		} catch (Exception e) {
			log.error("自动创建工种失败", e);
			return Result.error("自动创建工种失败：" + e.getMessage());
		}
	}

	/**
	 * 生成工种代码
	 * @param name 工种名称
	 * @return 工种代码
	 */
	private String generateWorktypeCode(String name) {
		// 简单的代码生成逻辑，可以根据需要优化
		String baseCode = name.length() > 2 ? name.substring(0, 2) : name;

		// 查找最大的序号
		QueryWrapper<ZyWorktype> queryWrapper = new QueryWrapper<>();
		queryWrapper.likeRight("code", baseCode);
		queryWrapper.orderByDesc("code");
		queryWrapper.last("LIMIT 1");

		ZyWorktype lastWorktype = zyWorktypeService.getOne(queryWrapper);
		int nextNumber = 1;

		if (lastWorktype != null && lastWorktype.getCode() != null) {
			String lastCode = lastWorktype.getCode();
			try {
				String numberPart = lastCode.replaceAll("\\D", "");
				if (!numberPart.isEmpty()) {
					nextNumber = Integer.parseInt(numberPart) + 1;
				}
			} catch (NumberFormatException e) {
				// 如果解析失败，使用默认值1
			}
		}

		return baseCode + String.format("%03d", nextNumber);
	}
}
