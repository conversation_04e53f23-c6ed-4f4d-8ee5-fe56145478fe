package org.jeecg.modules.occu.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.occu.entity.ZyIndustry;
import org.jeecg.modules.occu.service.IZyIndustryService;
import org.jeecg.modules.occu.vo.ZyIndustryLeafNodeVO;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecg.common.system.base.controller.JeecgController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.apache.shiro.authz.annotation.RequiresPermissions;

 /**
 * @Description: 行业类别
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
@Api(tags="行业类别")
@RestController
@RequestMapping("/occu/zyIndustry")
@Slf4j
public class ZyIndustryController extends JeecgController<ZyIndustry, IZyIndustryService>{
	@Autowired
	private IZyIndustryService zyIndustryService;

	/**
	 * 分页列表查询
	 *
	 * @param zyIndustry
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "行业类别-分页列表查询")
	@ApiOperation(value="行业类别-分页列表查询", notes="行业类别-分页列表查询")
	@GetMapping(value = "/rootList")
	public Result<IPage<ZyIndustry>> queryPageList(ZyIndustry zyIndustry,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		String hasQuery = req.getParameter("hasQuery");
        if(hasQuery != null && "true".equals(hasQuery)){
            QueryWrapper<ZyIndustry> queryWrapper =  QueryGenerator.initQueryWrapper(zyIndustry, req.getParameterMap());
            List<ZyIndustry> list = zyIndustryService.queryTreeListNoPage(queryWrapper);
            IPage<ZyIndustry> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        }else{
            String parentId = zyIndustry.getPid();
            if (oConvertUtils.isEmpty(parentId)) {
                parentId = "0";
            }
            zyIndustry.setPid(null);
            QueryWrapper<ZyIndustry> queryWrapper = QueryGenerator.initQueryWrapper(zyIndustry, req.getParameterMap());
            // 使用 eq 防止模糊查询
            queryWrapper.eq("pid", parentId);
            Page<ZyIndustry> page = new Page<ZyIndustry>(pageNo, pageSize);
            IPage<ZyIndustry> pageList = zyIndustryService.page(page, queryWrapper);
            return Result.OK(pageList);
        }
	}

	 /**
	  * 【vue3专用】加载节点的子数据
	  *
	  * @param pid
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeChildren", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeChildren(@RequestParam(name = "pid") String pid) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = zyIndustryService.queryListByPid(pid);
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】加载一级节点/如果是同步 则所有数据
	  *
	  * @param async
	  * @param pcode
	  * @return
	  */
	 @RequestMapping(value = "/loadTreeRoot", method = RequestMethod.GET)
	 public Result<List<SelectTreeModel>> loadTreeRoot(@RequestParam(name = "async") Boolean async, @RequestParam(name = "pcode") String pcode) {
		 Result<List<SelectTreeModel>> result = new Result<>();
		 try {
			 List<SelectTreeModel> ls = zyIndustryService.queryListByCode(pcode);
			 if (!async) {
				 loadAllChildren(ls);
			 }
			 result.setResult(ls);
			 result.setSuccess(true);
		 } catch (Exception e) {
			 e.printStackTrace();
			 result.setMessage(e.getMessage());
			 result.setSuccess(false);
		 }
		 return result;
	 }

	 /**
	  * 【vue3专用】递归求子节点 同步加载用到
	  *
	  * @param ls
	  */
	 private void loadAllChildren(List<SelectTreeModel> ls) {
		 for (SelectTreeModel tsm : ls) {
			 List<SelectTreeModel> temp = zyIndustryService.queryListByPid(tsm.getKey());
			 if (temp != null && temp.size() > 0) {
				 tsm.setChildren(temp);
				 loadAllChildren(temp);
			 }
		 }
	 }

	 /**
      * 获取子数据
      * @param zyIndustry
      * @param req
      * @return
      */
	//@AutoLog(value = "行业类别-获取子数据")
	@ApiOperation(value="行业类别-获取子数据", notes="行业类别-获取子数据")
	@GetMapping(value = "/childList")
	public Result<IPage<ZyIndustry>> queryPageList(ZyIndustry zyIndustry,HttpServletRequest req) {
		QueryWrapper<ZyIndustry> queryWrapper = QueryGenerator.initQueryWrapper(zyIndustry, req.getParameterMap());
		List<ZyIndustry> list = zyIndustryService.list(queryWrapper);
		IPage<ZyIndustry> pageList = new Page<>(1, 10, list.size());
        pageList.setRecords(list);
		return Result.OK(pageList);
	}

    /**
      * 批量查询子节点
      * @param parentIds 父ID（多个采用半角逗号分割）
      * @return 返回 IPage
      * @param parentIds
      * @return
      */
	//@AutoLog(value = "行业类别-批量获取子数据")
    @ApiOperation(value="行业类别-批量获取子数据", notes="行业类别-批量获取子数据")
    @GetMapping("/getChildListBatch")
    public Result getChildListBatch(@RequestParam("parentIds") String parentIds) {
        try {
            QueryWrapper<ZyIndustry> queryWrapper = new QueryWrapper<>();
            List<String> parentIdList = Arrays.asList(parentIds.split(","));
            queryWrapper.in("pid", parentIdList);
            List<ZyIndustry> list = zyIndustryService.list(queryWrapper);
            IPage<ZyIndustry> pageList = new Page<>(1, 10, list.size());
            pageList.setRecords(list);
            return Result.OK(pageList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return Result.error("批量查询子节点失败：" + e.getMessage());
        }
    }
	
	/**
	 *   添加
	 *
	 * @param zyIndustry
	 * @return
	 */
	@AutoLog(value = "行业类别-添加")
	@ApiOperation(value="行业类别-添加", notes="行业类别-添加")
    @RequiresPermissions("occu:zy_industry:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody ZyIndustry zyIndustry) {
		zyIndustryService.addZyIndustry(zyIndustry);
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param zyIndustry
	 * @return
	 */
	@AutoLog(value = "行业类别-编辑")
	@ApiOperation(value="行业类别-编辑", notes="行业类别-编辑")
    @RequiresPermissions("occu:zy_industry:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody ZyIndustry zyIndustry) {
		zyIndustryService.updateZyIndustry(zyIndustry);
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "行业类别-通过id删除")
	@ApiOperation(value="行业类别-通过id删除", notes="行业类别-通过id删除")
    @RequiresPermissions("occu:zy_industry:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		zyIndustryService.deleteZyIndustry(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "行业类别-批量删除")
	@ApiOperation(value="行业类别-批量删除", notes="行业类别-批量删除")
    @RequiresPermissions("occu:zy_industry:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.zyIndustryService.removeByIds(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "行业类别-通过id查询")
	@ApiOperation(value="行业类别-通过id查询", notes="行业类别-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<ZyIndustry> queryById(@RequestParam(name="id",required=true) String id) {
		ZyIndustry zyIndustry = zyIndustryService.getById(id);
		if(zyIndustry==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(zyIndustry);
	}

    /**
    * 导出excel
    *
    * @param request
    * @param zyIndustry
    */
    @RequiresPermissions("occu:zy_industry:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, ZyIndustry zyIndustry) {
		return super.exportXls(request, zyIndustry, ZyIndustry.class, "行业类别");
    }

    /**
      * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("occu:zy_industry:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
		return super.importExcel(request, response, ZyIndustry.class);
    }

	/**
	 * 获取所有叶子节点（用于下拉选择）
	 *
	 * @param keyword 搜索关键词（可选）
	 * @return 叶子节点列表
	 */
	@ApiOperation(value = "获取所有叶子节点", notes = "获取所有叶子节点，支持关键词搜索，带Redis缓存")
	@GetMapping(value = "/leafNodes")
	public Result<List<ZyIndustryLeafNodeVO>> getLeafNodes(
			@RequestParam(name = "keyword", required = false) String keyword) {
		try {
			List<ZyIndustryLeafNodeVO> leafNodes = zyIndustryService.getAllLeafNodes(keyword);
			return Result.OK(leafNodes);
		} catch (Exception e) {
			log.error("获取叶子节点失败", e);
			return Result.error("获取叶子节点失败：" + e.getMessage());
		}
	}

	/**
	 * 刷新叶子节点缓存
	 *
	 * @return 操作结果
	 */
	@AutoLog(value = "行业类别-刷新叶子节点缓存")
	@ApiOperation(value = "刷新叶子节点缓存", notes = "手动刷新叶子节点Redis缓存")
	@RequiresPermissions("occu:zy_industry:edit")
	@PostMapping(value = "/refreshLeafNodesCache")
	public Result<String> refreshLeafNodesCache() {
		try {
			zyIndustryService.refreshLeafNodesCache();
			return Result.OK("缓存刷新成功！");
		} catch (Exception e) {
			log.error("刷新叶子节点缓存失败", e);
			return Result.error("刷新缓存失败：" + e.getMessage());
		}
	}

}
