package org.jeecg.modules.occu.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZySymptomDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 职业禁忌症字典
 * @Author: jeecg-boot
 * @Date:   2024-12-11
 * @Version: V1.0
 */
public interface ZySymptomDictMapper extends BaseMapper<ZySymptomDict> {

    ZySymptomDict getByCode(@Param("code") String code);
}
