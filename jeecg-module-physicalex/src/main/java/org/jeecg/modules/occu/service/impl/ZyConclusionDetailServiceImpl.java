package org.jeecg.modules.occu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.occu.entity.*;
import org.jeecg.modules.occu.mapper.*;
import org.jeecg.modules.occu.service.IZyConclusionDetailService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description: 职业检总检详细
 * @Author: jeecg-boot
 * @Date: 2024-05-31
 * @Version: V1.0
 */
@Service
public class ZyConclusionDetailServiceImpl extends ServiceImpl<ZyConclusionDetailMapper, ZyConclusionDetail> implements IZyConclusionDetailService {

    @Resource
    private ZyRiskFactorMapper zyRiskFactorMapper;
    @Resource
    private ZyDiseaseDictMapper zyDiseaseDictMapper;
    @Resource
    private ZySymptomDictMapper zySymptomDictMapper;
    @Resource
    private ZyConclusionDictMapper zyConclusionDictMapper;


    @Override
    public void fillText(ZyConclusionDetail detail) {
        if (detail == null) return;

        if (StringUtils.hasText(detail.getConclusion())) {
            ZyConclusionDict dict = zyConclusionDictMapper.getByCode(detail.getConclusion());
            String text = dict == null ? null : dict.getDictText();
            detail.setConclusionText(text);
        }
        // 处理危害因素
        if (StringUtils.hasText(detail.getRiskFactorId())) {
            ZyRiskFactor factor = zyRiskFactorMapper.selectById(detail.getRiskFactorId());
            String text = factor == null ? null : factor.getName();
            detail.setRiskFactor(text);
        }

        // 处理职业病 - 支持逗号分隔的多个值
        if (StringUtils.hasText(detail.getZyDisease())) {
            String[] diseaseCodes = detail.getZyDisease().split(",");
            List<String> diseaseTexts = new ArrayList<>();

            for (String code : diseaseCodes) {
                String trimmedCode = code.trim();
                if (StringUtils.hasText(trimmedCode)) {
                    ZyDiseaseDict dict = zyDiseaseDictMapper.getByCode(trimmedCode);
                    if (dict != null && StringUtils.hasText(dict.getDictText())) {
                        diseaseTexts.add(dict.getDictText());
                    }
                }
            }

            String text = diseaseTexts.isEmpty() ? null : String.join("，", diseaseTexts);
            detail.setZyDiseaseText(text);
        }

        // 处理职业禁忌症 - 支持逗号分隔的多个值
        if (StringUtils.hasText(detail.getZySymptom())) {
            String[] symptomCodes = detail.getZySymptom().split(",");
            List<String> symptomTexts = new ArrayList<>();

            for (String code : symptomCodes) {
                String trimmedCode = code.trim();
                if (StringUtils.hasText(trimmedCode)) {
                    ZySymptomDict dict = zySymptomDictMapper.getByCode(trimmedCode);
                    if (dict != null && StringUtils.hasText(dict.getDictText())) {
                        symptomTexts.add(dict.getDictText());
                    }
                }
            }

            String text = symptomTexts.isEmpty() ? null : String.join("，", symptomTexts);
            detail.setZySymptomText(text);
        }
    }
}
