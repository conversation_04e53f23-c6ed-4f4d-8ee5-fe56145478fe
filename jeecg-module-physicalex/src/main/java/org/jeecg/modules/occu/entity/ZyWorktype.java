package org.jeecg.modules.occu.entity;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.math.BigDecimal;
import java.util.List;

import com.baomidou.mybatisplus.annotation.*;
import org.jeecg.common.constant.ProvinceCityArea;
import org.jeecg.common.util.SpringContextUtils;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecg.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * @Description: 工种
 * @Author: jeecg-boot
 * @Date: 2024-12-11
 * @Version: V1.0
 */
@Data
@TableName("zy_worktype")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "zy_worktype对象", description = "工种")
public class ZyWorktype implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private java.lang.String id;
    /**
     * 名称
     */
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private java.lang.String name;
    /**
     * 代码
     */
    @Excel(name = "代码", width = 15)
    @ApiModelProperty(value = "代码")
    private java.lang.String code;
    /**
     * 助记码
     */
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码")
    private java.lang.String helpChar;
    /**
     * 启用
     */
    @Excel(name = "启用", width = 15, replace = {"是_1", "否_0"})
    @ApiModelProperty(value = "启用")
    private java.lang.Integer enableFlag;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private java.lang.Integer sort;
    /**
     * 使用频次
     */
    @Excel(name = "使用频次", width = 15)
    @ApiModelProperty(value = "使用频次")
    @TableField(value = "use_count")
    private java.lang.Integer useCount;

    @TableField(exist = false)
    List<ZyRiskFactorWorktype> riskFactorList;
}
