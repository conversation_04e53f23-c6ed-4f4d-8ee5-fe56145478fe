package org.jeecg.modules.occu.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.occu.entity.ZyConclusionDict;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description: 职业检结论字典
 * @Author: jeecg-boot
 * @Date: 2024-12-11
 * @Version: V1.0
 */
public interface ZyConclusionDictMapper extends BaseMapper<ZyConclusionDict> {
    ZyConclusionDict getByCode(@Param("code") String code);
}
