package org.jeecg.modules.comInterface.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.basicinfo.entity.Template;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.jeecg.modules.basicinfo.service.ITemplateService;
import org.jeecg.modules.comInterface.dto.CompanyRegBatchCreateDTO;
import org.jeecg.modules.comInterface.dto.CompanyRegCreateDTO;
import org.jeecg.modules.comInterface.dto.CustomerRegBatchCreateDTO;
import org.jeecg.modules.comInterface.service.ICompanyRegApiService;
import org.jeecg.modules.comInterface.vo.ApiResponse;
import org.jeecg.modules.comInterface.vo.BatchResultVO;
import org.jeecg.modules.mobile.utils.FileUrlUtils;
import org.jeecg.modules.reg.entity.CompanyReg;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.summary.bo.teamreport.TeamReportVO;
import org.jeecg.modules.summary.bo.teamreport.ZyTeamReportVO;
import org.jeecg.modules.summary.service.ITeamReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Positive;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 企业预约API控制器
 * @Author: system
 * @Date: 2024-07-30
 * @Version: V1.0
 */
@Api(tags = "企业预约API")
@RestController
@RequestMapping("/api/v1/company")
@Slf4j
@Validated
public class CompanyRegApiController {

    @Autowired
    private ICompanyRegApiService companyRegApiService;

    @Autowired
    private ITeamReportService teamReportService;

    @Autowired
    private ITemplateService templateService;

    @Autowired
    private ISysSettingService sysSettingService;


    @ApiOperation(value = "创建企业预约", notes = "创建企业预约，包含分组信息")
    @PostMapping("/registration")
    public ApiResponse<CompanyReg> createCompanyReg(@Valid @RequestBody CompanyRegCreateDTO createDTO) {
        try {
            log.info("Creating company registration: {}", createDTO.getRegName());
            CompanyReg companyReg = companyRegApiService.createCompanyReg(createDTO);
            return ApiResponse.success("Company registration created successfully", companyReg);
        } catch (Exception e) {
            log.error("Failed to create company registration", e);
            return ApiResponse.error("Failed to create company registration: " + e.getMessage());
        }
    }

    @ApiOperation(value = "企业端批量添加单位预约数据", notes = "处理企业端提供的批量添加单位预约数据，包含人员信息和职业病问卷")
    @PostMapping("/registration/batch")
    public ApiResponse<?> batchCreateCompanyReg(@RequestBody String info) {
        try {
            CompanyRegBatchCreateDTO batchCreateDTO = JSONObject.parseObject(info, CompanyRegBatchCreateDTO.class);
            log.info("Processing batch company registration data from enterprise");
            companyRegApiService.batchCreateCompanyReg(batchCreateDTO);
            return ApiResponse.success("数据接收成功");
        } catch (Exception e) {
            log.error("Failed to process batch company registration data", e);
            return ApiResponse.error("数据接收失败");
        }
    }

    @ApiOperation(value = "批量创建客户登记", notes = "批量创建客户登记信息")
    @PostMapping("/customers/batch")
    public ApiResponse<BatchResultVO<CustomerReg>> batchCreateCustomerReg(@Valid @RequestBody CustomerRegBatchCreateDTO batchCreateDTO) {
        try {
            log.info("Batch creating customer registrations, count: {}", batchCreateDTO.getCustomerList().size());
            BatchResultVO<CustomerReg> result = companyRegApiService.batchCreateCustomerReg(batchCreateDTO);

            if (result.getFailureCount() > 0) {
                return ApiResponse.success("Batch creation completed with some failures", result);
            } else {
                return ApiResponse.success("All customer registrations created successfully", result);
            }
        } catch (Exception e) {
            log.error("Failed to batch create customer registrations", e);
            return ApiResponse.error("Failed to batch create customer registrations: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取企业预约信息", notes = "根据ID获取企业预约详细信息")
    @GetMapping("/registration/{companyRegId}")
    public ApiResponse<CompanyReg> getCompanyReg(@ApiParam(value = "企业预约ID", required = true) @PathVariable @NotBlank String companyRegId) {
        try {
            log.info("Getting company registration: {}", companyRegId);
            CompanyReg companyReg = companyRegApiService.getCompanyRegById(companyRegId);

            if (companyReg == null) {
                return ApiResponse.error(404, "Company registration not found");
            }

            return ApiResponse.success("Company registration retrieved successfully", companyReg);
        } catch (Exception e) {
            log.error("Failed to get company registration: {}", companyRegId, e);
            return ApiResponse.error("Failed to get company registration: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取客户登记列表", notes = "分页获取指定企业预约下的客户登记列表")
    @GetMapping("/registration/{companyRegId}/customers")
    public ApiResponse<BatchResultVO<CustomerReg>> getCustomerRegList(@ApiParam(value = "企业预约ID", required = true) @PathVariable @NotBlank String companyRegId, @ApiParam(value = "页码", example = "1") @RequestParam(defaultValue = "1") @Positive Integer pageNo, @ApiParam(value = "页大小", example = "20") @RequestParam(defaultValue = "20") @Positive Integer pageSize) {
        try {
            log.info("Getting customer registration list for company: {}, page: {}, size: {}", companyRegId, pageNo, pageSize);

            // 限制页大小
            if (pageSize > 100) {
                pageSize = 100;
            }

            BatchResultVO<CustomerReg> result = companyRegApiService.getCustomerRegList(companyRegId, pageNo, pageSize);
            return ApiResponse.success("Customer registration list retrieved successfully", result);
        } catch (Exception e) {
            log.error("Failed to get customer registration list for company: {}", companyRegId, e);
            return ApiResponse.error("Failed to get customer registration list: " + e.getMessage());
        }
    }

    @ApiOperation(value = "健康检查", notes = "API健康检查接口")
    @GetMapping("/health")
    public ApiResponse<String> health() {
        return ApiResponse.success("API is healthy", "OK");
    }

    @ApiOperation(value = "获取团检报告渲染数据", notes = "获取常规团检报告的完整渲染数据，包含报告内容和模板")
    @GetMapping("/registration/{companyRegId}/team-report")
    public ApiResponse<Object> getTeamReportForRender(@ApiParam(value = "企业预约ID", required = true) @PathVariable @NotBlank String companyRegId, @ApiParam(value = "企业部门ID", required = false) @RequestParam(value = "companyDeptId", required = false) String companyDeptId) {
        try {
            log.info("Getting team report render data for company: {}, dept: {}", companyRegId, companyDeptId);

            // 获取团检报告数据
            TeamReportVO teamReportData = teamReportService.getTeamReportStat(companyRegId, companyDeptId);
            if (teamReportData == null) {
                return ApiResponse.error("为找到对应的单位预约信息");
            }

            // 获取团检报告模板
            Template reportTemplate = getTeamReportTemplate(teamReportData.getCompanyReg());
            if (reportTemplate == null) {
                return ApiResponse.error("没有匹配的报告模版");
            }

            // 处理图片URL，确保使用完整的URL路径
            String openFileUrl = sysSettingService.getValueByCode("open_file_url");
            processTeamReportImageUrls(teamReportData, openFileUrl);

            // 构建ActiveReportJS可直接渲染的数据结构
            Map<String, Object> renderData = new HashMap<>();
            JSONObject templateObj = JSONObject.parseObject(reportTemplate.getContent());
            renderData.put("template", templateObj);
            renderData.put("data", teamReportData);
            renderData.put("reportType", "team");
            renderData.put("companyRegId", companyRegId);
            renderData.put("companyDeptId", companyDeptId);

            // 更新模板中的数据源连接字符串
          /*  if (templateObj != null) {
                if (templateObj.containsKey("DataSources")) {
                    JSONArray dataSourcesObj = templateObj.getJSONArray("DataSources");
                    if (!dataSourcesObj.isEmpty()) {
                        JSONObject dataSource = dataSourcesObj.getJSONObject(0);
                        JSONObject connectionPropertiesObj = dataSource.getJSONObject("ConnectionProperties");
                        ObjectMapper objectMapper = new ObjectMapper();
                        String zyTeamReportJson = objectMapper.writeValueAsString(teamReportData);
                        connectionPropertiesObj.put("ConnectString", "jsondata=" + zyTeamReportJson);
                    }
                }
            }*/

            return ApiResponse.success(renderData);
        } catch (Exception e) {
            log.error("Failed to get team report render data for company: {}", companyRegId, e);
            return ApiResponse.error("Failed to get team report render data: " + e.getMessage());
        }
    }

    @ApiOperation(value = "获取职业病团检报告渲染数据", notes = "获取职业病团检报告的完整渲染数据，包含报告内容和模板")
    @GetMapping("/registration/{companyRegId}/zy-team-report")
    public ApiResponse<Object> getZyTeamReportForRender(@ApiParam(value = "企业预约ID", required = true) @PathVariable @NotBlank String companyRegId, @ApiParam(value = "企业部门ID", required = false) @RequestParam(value = "companyDeptId", required = false) String companyDeptId,@RequestParam(value = "jobStatus", required = false) String jobStatus) {
        try {
            log.info("Getting occupational team report render data for company: {}, dept: {}", companyRegId, companyDeptId);

            // 获取职业病团检报告数据
            ZyTeamReportVO zyTeamReportData = teamReportService.getZyTeamReportStat(companyRegId, companyDeptId,jobStatus);
            if (zyTeamReportData == null) {
                return ApiResponse.error(404, "Occupational team report data not found");
            }

            // 获取职业病团检报告模板
            Template reportTemplate = getZyTeamReportTemplate(zyTeamReportData.getCompanyReg());
            if (reportTemplate == null) {
                return ApiResponse.error(404, "Occupational team report template not found");
            }

            // 处理图片URL，确保使用完整的URL路径
            String localFileServerDomain = sysSettingService.getValueByCode("open_file_url");
            processZyTeamReportImageUrls(zyTeamReportData, localFileServerDomain);

            // 构建ActiveReportJS可直接渲染的数据结构
            Map<String, Object> renderData = new HashMap<>();
            JSONObject templateObj = JSONObject.parseObject(reportTemplate.getContent());
            renderData.put("template", templateObj);
            renderData.put("data", zyTeamReportData);
            renderData.put("reportType", "zyTeam");
            renderData.put("companyRegId", companyRegId);
            renderData.put("companyDeptId", companyDeptId);

            // 更新模板中的数据源连接字符串
            if (templateObj != null) {
                if (templateObj.containsKey("DataSources")) {
                    JSONArray dataSourcesObj = templateObj.getJSONArray("DataSources");
                    if (!dataSourcesObj.isEmpty()) {
                        JSONObject dataSource = dataSourcesObj.getJSONObject(0);
                        JSONObject connectionPropertiesObj = dataSource.getJSONObject("ConnectionProperties");
                        ObjectMapper objectMapper = new ObjectMapper();
                        String zyTeamReportJson = objectMapper.writeValueAsString(zyTeamReportData);
                        connectionPropertiesObj.put("ConnectString", "jsondata=" + zyTeamReportJson);
                    }
                }
            }

            return ApiResponse.success("Occupational team report render data retrieved successfully", renderData);
        } catch (Exception e) {
            log.error("Failed to get occupational team report render data for company: {}", companyRegId, e);
            return ApiResponse.error("Failed to get occupational team report render data: " + e.getMessage());
        }
    }

    // 辅助方法：获取团检报告模板
    private Template getTeamReportTemplate(CompanyReg companyReg) {
        Template reportTemplate = null;

        // 首先尝试根据体检分类获取对应的团检报告模板
        if (StringUtils.isNotBlank(companyReg.getExamType())) {
            LambdaQueryWrapper<Template> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Template::getType, ExConstants.TEMPLATE_TYPE_报告).eq(Template::getRegType, ExConstants.TEMPLATE_SUIT_团体).eq(Template::getExamCategory, companyReg.getExamType());
            List<Template> templates = templateService.list(queryWrapper);
            if (!templates.isEmpty()) {
                reportTemplate = templates.get(0);
            }
        }
        return reportTemplate;
    }

    // 辅助方法：获取职业病团检报告模板
    private Template getZyTeamReportTemplate(CompanyReg companyReg) {
        Template reportTemplate = null;

        // 首先尝试根据体检分类获取对应的职业病团检报告模板
        if (StringUtils.isNotBlank(companyReg.getExamType())) {
            LambdaQueryWrapper<Template> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Template::getEnableFlag, "1").eq(Template::getType, ExConstants.TEMPLATE_TYPE_报告).eq(Template::getRegType, ExConstants.BUYER_TYPE_单位).like(Template::getExamCategory, companyReg.getExamType()).and(wrapper -> wrapper.like(Template::getName, "职业病").or().like(Template::getName, "职业"));
            List<Template> templates = templateService.list(queryWrapper);
            if (!templates.isEmpty()) {
                reportTemplate = templates.get(0);
            }
        }

        // 如果没有找到匹配的模板，尝试获取默认的职业病团检报告模板
        if (reportTemplate == null) {
            LambdaQueryWrapper<Template> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Template::getEnableFlag, "1").eq(Template::getType, ExConstants.TEMPLATE_TYPE_报告).eq(Template::getRegType, ExConstants.BUYER_TYPE_单位).and(wrapper -> wrapper.like(Template::getName, "职业病").or().like(Template::getName, "职业"));
            List<Template> templates = templateService.list(queryWrapper);
            if (!templates.isEmpty()) {
                reportTemplate = templates.get(0);
            }
        }

        return reportTemplate;
    }

    // 辅助方法：处理团检报告中的图片URL
    private void processTeamReportImageUrls(TeamReportVO teamReportData, String localFileServerDomain) {
        if (StringUtils.isBlank(localFileServerDomain)) {
            return;
        }

        // 处理公司相关的图片（如果有logo字段的话）
        // 由于Company实体可能没有logo字段，这里先注释掉，避免编译错误
        // if (teamReportData.getCompany() != null && StringUtils.isNotBlank(teamReportData.getCompany().getLogo())) {
        //     if (!teamReportData.getCompany().getLogo().startsWith("http")) {
        //         String fullUrl = UrlUtils.concatenateUrl(localFileServerDomain, teamReportData.getCompany().getLogo());
        //         teamReportData.getCompany().setLogo(fullUrl);
        //     }
        // }

        // 可以根据需要处理更多的图片字段
    }

    // 辅助方法：处理职业病团检报告中的图片URL
    private void processZyTeamReportImageUrls(ZyTeamReportVO zyTeamReportData, String openFileUrl) {
        if (StringUtils.isBlank(openFileUrl)) {
            return;
        }

        // 处理机构信息中的图片
        if (zyTeamReportData.getOrgInfo() != null) {
            // 处理机构logo图片
            if (StringUtils.isNotBlank(zyTeamReportData.getOrgInfo().getLogoPicture())) {
                zyTeamReportData.getOrgInfo().setLogoPicture(
                        FileUrlUtils.replaceUrl(zyTeamReportData.getOrgInfo().getLogoPicture(), openFileUrl)
                );
            }

            // 处理机构封面图片
            if (StringUtils.isNotBlank(zyTeamReportData.getOrgInfo().getCoverPicture())) {
                zyTeamReportData.getOrgInfo().setCoverPicture(
                        FileUrlUtils.replaceUrl(zyTeamReportData.getOrgInfo().getCoverPicture(), openFileUrl)
                );
            }

            // 处理资质图片列表
            if (zyTeamReportData.getOrgInfo().getQualityPictureList() != null &&
                    !zyTeamReportData.getOrgInfo().getQualityPictureList().isEmpty()) {
                zyTeamReportData.getOrgInfo().getQualityPictureList().replaceAll(pic ->
                        StringUtils.isNotBlank(pic) ? FileUrlUtils.replaceUrl(pic, openFileUrl) : pic
                );
            }
        }

        // 可以根据需要处理更多的图片字段，比如签名图片等
    }
}
