package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 自动补全配置
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@TableName("auto_complete_config")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "auto_complete_config对象", description = "自动补全配置")
public class AutoCompleteConfig implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**分类标识*/
    @Excel(name = "分类标识", width = 15)
    @ApiModelProperty(value = "分类标识")
    private String category;

    /**分类名称*/
    @Excel(name = "分类名称", width = 15)
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**是否启用缓存*/
    @Excel(name = "是否启用缓存", width = 15, dicCode = "yes_no")
    @Dict(dicCode = "yes_no")
    @ApiModelProperty(value = "是否启用缓存(1-启用,0-禁用)")
    private Integer cacheEnabled;

    /**缓存过期时间*/
    @Excel(name = "缓存过期时间", width = 15)
    @ApiModelProperty(value = "缓存过期时间(小时)")
    private Integer cacheExpireHours;

    /**是否允许自动创建*/
    @Excel(name = "是否允许自动创建", width = 15, dicCode = "yes_no")
    @Dict(dicCode = "yes_no")
    @ApiModelProperty(value = "是否允许自动创建(1-允许,0-不允许)")
    private Integer autoCreateEnabled;

    /**最大建议数量*/
    @Excel(name = "最大建议数量", width = 15)
    @ApiModelProperty(value = "最大建议数量")
    private Integer maxSuggestions;

    /**搜索类型*/
    @Excel(name = "搜索类型", width = 15, dicCode = "auto_complete_search_type")
    @Dict(dicCode = "auto_complete_search_type")
    @ApiModelProperty(value = "搜索类型(name-仅名称,helpChar-仅助记码,both-两者)")
    private String searchType;

    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "valid_status")
    @Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "状态(1-启用,0-禁用)")
    private Integer status;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志*/
    @ApiModelProperty(value = "删除标志(0-正常,1-删除)")
    private Integer delFlag;
}
