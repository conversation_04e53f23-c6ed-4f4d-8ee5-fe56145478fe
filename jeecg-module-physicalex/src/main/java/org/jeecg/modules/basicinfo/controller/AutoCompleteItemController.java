package org.jeecg.modules.basicinfo.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.basicinfo.entity.AutoCompleteItem;
import org.jeecg.modules.basicinfo.service.IAutoCompleteItemService;
import org.jeecg.modules.basicinfo.vo.AutoCompleteCreateVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * @Description: 通用自动补全项目
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags = "通用自动补全项目")
@RestController
@RequestMapping("/basicinfo/autoCompleteItem")
@Slf4j
public class AutoCompleteItemController extends JeecgController<AutoCompleteItem, IAutoCompleteItemService> {

    @Autowired
    private IAutoCompleteItemService autoCompleteItemService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "通用自动补全项目-分页列表查询")
    @ApiOperation(value = "通用自动补全项目-分页列表查询", notes = "通用自动补全项目-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<AutoCompleteItem>> queryPageList(AutoCompleteItem autoCompleteItem,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                        HttpServletRequest req) {
        QueryWrapper<AutoCompleteItem> queryWrapper = QueryGenerator.initQueryWrapper(autoCompleteItem, req.getParameterMap());
        Page<AutoCompleteItem> page = new Page<AutoCompleteItem>(pageNo, pageSize);
        IPage<AutoCompleteItem> pageList = autoCompleteItemService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     */
    @AutoLog(value = "通用自动补全项目-添加")
    @ApiOperation(value = "通用自动补全项目-添加", notes = "通用自动补全项目-添加")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody AutoCompleteItem autoCompleteItem) {
        autoCompleteItemService.save(autoCompleteItem);
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     */
    @AutoLog(value = "通用自动补全项目-编辑")
    @ApiOperation(value = "通用自动补全项目-编辑", notes = "通用自动补全项目-编辑")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody AutoCompleteItem autoCompleteItem) {
        autoCompleteItemService.updateById(autoCompleteItem);
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "通用自动补全项目-通过id删除")
    @ApiOperation(value = "通用自动补全项目-通过id删除", notes = "通用自动补全项目-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        autoCompleteItemService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "通用自动补全项目-批量删除")
    @ApiOperation(value = "通用自动补全项目-批量删除", notes = "通用自动补全项目-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.autoCompleteItemService.removeByIds(java.util.Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "通用自动补全项目-通过id查询")
    @ApiOperation(value = "通用自动补全项目-通过id查询", notes = "通用自动补全项目-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<AutoCompleteItem> queryById(@RequestParam(name = "id", required = true) String id) {
        AutoCompleteItem autoCompleteItem = autoCompleteItemService.getById(id);
        if (autoCompleteItem == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(autoCompleteItem);
    }

    /**
     * 自动补全搜索
     */
    @ApiOperation(value = "自动补全搜索", notes = "自动补全搜索")
    @PostMapping(value = "/search")
    public Result<List<AutoCompleteResultVO>> autoCompleteSearch(@RequestBody AutoCompleteSearchVO searchVO) {
        try {
            List<AutoCompleteResultVO> results = autoCompleteItemService.autoCompleteSearch(searchVO);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("自动补全搜索失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 自动补全搜索（GET方式）
     */
    @ApiOperation(value = "自动补全搜索", notes = "自动补全搜索")
    @GetMapping(value = "/search")
    public Result<List<AutoCompleteResultVO>> autoCompleteSearchGet(@RequestParam String category,
                                                                   @RequestParam(required = false) String keyword,
                                                                   @RequestParam(defaultValue = "both") String searchType,
                                                                   @RequestParam(defaultValue = "10") Integer limit,
                                                                   @RequestParam(defaultValue = "true") Boolean includePopular) {
        try {
            AutoCompleteSearchVO searchVO = new AutoCompleteSearchVO();
            searchVO.setCategory(category);
            searchVO.setKeyword(keyword);
            searchVO.setSearchType(searchType);
            searchVO.setLimit(limit);
            searchVO.setIncludePopular(includePopular);
            
            List<AutoCompleteResultVO> results = autoCompleteItemService.autoCompleteSearch(searchVO);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("自动补全搜索失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门推荐
     */
    @ApiOperation(value = "获取热门推荐", notes = "获取热门推荐")
    @GetMapping(value = "/popular")
    public Result<List<AutoCompleteResultVO>> getPopularItems(@RequestParam String category,
                                                             @RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<AutoCompleteResultVO> results = autoCompleteItemService.getPopularItems(category, limit);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("获取热门推荐失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 自动创建项目
     */
    @ApiOperation(value = "自动创建项目", notes = "自动创建项目")
    @PostMapping(value = "/autoCreate")
    public Result<AutoCompleteItem> autoCreateItem(@RequestBody AutoCompleteCreateVO createVO) {
        try {
            AutoCompleteItem item = autoCompleteItemService.autoCreateItem(createVO);
            return Result.OK(item);
        } catch (Exception e) {
            log.error("自动创建项目失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新使用频次
     */
    @ApiOperation(value = "更新使用频次", notes = "更新使用频次")
    @PostMapping(value = "/updateUseCount")
    public Result<String> updateUseCount(@RequestParam String id) {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userId = loginUser != null ? loginUser.getId() : "anonymous";
            String userName = loginUser != null ? loginUser.getRealname() : "匿名用户";
            
            boolean success = autoCompleteItemService.updateUseCount(id, userId, userName);
            if (success) {
                return Result.OK("更新成功");
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            log.error("更新使用频次失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新使用频次
     */
    @ApiOperation(value = "批量更新使用频次", notes = "批量更新使用频次")
    @PostMapping(value = "/batchUpdateUseCount")
    public Result<String> batchUpdateUseCount(@RequestBody List<String> ids) {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userId = loginUser != null ? loginUser.getId() : "anonymous";
            String userName = loginUser != null ? loginUser.getRealname() : "匿名用户";
            
            boolean success = autoCompleteItemService.batchUpdateUseCount(ids, userId, userName);
            if (success) {
                return Result.OK("批量更新成功");
            } else {
                return Result.error("批量更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新使用频次失败", e);
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类统计信息
     */
    @ApiOperation(value = "获取分类统计信息", notes = "获取分类统计信息")
    @GetMapping(value = "/categoryStats")
    public Result<Object> getCategoryStats(@RequestParam String category) {
        try {
            Object stats = autoCompleteItemService.getCategoryStats(category);
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取分类统计信息失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 清除分类缓存
     */
    @ApiOperation(value = "清除分类缓存", notes = "清除分类缓存")
    @PostMapping(value = "/clearCache")
    public Result<String> clearCategoryCache(@RequestParam String category) {
        try {
            boolean success = autoCompleteItemService.clearCategoryCache(category);
            if (success) {
                return Result.OK("缓存清除成功");
            } else {
                return Result.error("缓存清除失败");
            }
        } catch (Exception e) {
            log.error("清除分类缓存失败", e);
            return Result.error("清除失败: " + e.getMessage());
        }
    }

    /**
     * 刷新分类缓存
     */
    @ApiOperation(value = "刷新分类缓存", notes = "刷新分类缓存")
    @PostMapping(value = "/refreshCache")
    public Result<String> refreshCategoryCache(@RequestParam String category) {
        try {
            boolean success = autoCompleteItemService.refreshCategoryCache(category);
            if (success) {
                return Result.OK("缓存刷新成功");
            } else {
                return Result.error("缓存刷新失败");
            }
        } catch (Exception e) {
            log.error("刷新分类缓存失败", e);
            return Result.error("刷新失败: " + e.getMessage());
        }
    }
}
