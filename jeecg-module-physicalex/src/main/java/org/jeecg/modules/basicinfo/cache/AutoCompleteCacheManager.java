package org.jeecg.modules.basicinfo.cache;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.AutoCompleteConfig;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @Description: 自动补全缓存管理器
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Component
@Slf4j
public class AutoCompleteCacheManager {

    @Resource
    @Qualifier("autoCompleteRedisTemplate")
    private RedisTemplate<String, Object> redisTemplate;

    // 缓存键前缀
    private static final String CACHE_PREFIX = "auto_complete:";
    private static final String POPULAR_PREFIX = "popular:";
    private static final String SEARCH_PREFIX = "search:";
    private static final String CONFIG_PREFIX = "config:";
    private static final String STATS_PREFIX = "stats:";

    /**
     * 获取热门项目缓存
     */
    @SuppressWarnings("unchecked")
    public List<AutoCompleteResultVO> getPopularCache(String category, Integer limit) {
        if (StringUtils.isBlank(category)) {
            return null;
        }
        
        String cacheKey = buildPopularCacheKey(category, limit);
        try {
            return (List<AutoCompleteResultVO>) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取热门项目缓存失败: category={}, error={}", category, e.getMessage());
            return null;
        }
    }

    /**
     * 设置热门项目缓存
     */
    public void setPopularCache(String category, Integer limit, List<AutoCompleteResultVO> data, int expireHours) {
        if (StringUtils.isBlank(category) || data == null) {
            return;
        }
        
        String cacheKey = buildPopularCacheKey(category, limit);
        try {
            redisTemplate.opsForValue().set(cacheKey, data, expireHours, TimeUnit.HOURS);
            log.debug("设置热门项目缓存成功: category={}, size={}", category, data.size());
        } catch (Exception e) {
            log.warn("设置热门项目缓存失败: category={}, error={}", category, e.getMessage());
        }
    }

    /**
     * 获取搜索结果缓存
     */
    @SuppressWarnings("unchecked")
    public List<AutoCompleteResultVO> getSearchCache(String category, String keyword, String searchType, Integer limit) {
        if (StringUtils.isBlank(category)) {
            return null;
        }
        
        String cacheKey = buildSearchCacheKey(category, keyword, searchType, limit);
        try {
            return (List<AutoCompleteResultVO>) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取搜索结果缓存失败: category={}, keyword={}, error={}", category, keyword, e.getMessage());
            return null;
        }
    }

    /**
     * 设置搜索结果缓存
     */
    public void setSearchCache(String category, String keyword, String searchType, Integer limit, 
                              List<AutoCompleteResultVO> data, int expireMinutes) {
        if (StringUtils.isBlank(category) || data == null) {
            return;
        }
        
        String cacheKey = buildSearchCacheKey(category, keyword, searchType, limit);
        try {
            redisTemplate.opsForValue().set(cacheKey, data, expireMinutes, TimeUnit.MINUTES);
            log.debug("设置搜索结果缓存成功: category={}, keyword={}, size={}", category, keyword, data.size());
        } catch (Exception e) {
            log.warn("设置搜索结果缓存失败: category={}, keyword={}, error={}", category, keyword, e.getMessage());
        }
    }

    /**
     * 获取配置缓存
     */
    public AutoCompleteConfig getConfigCache(String category) {
        if (StringUtils.isBlank(category)) {
            return null;
        }
        
        String cacheKey = buildConfigCacheKey(category);
        try {
            return (AutoCompleteConfig) redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取配置缓存失败: category={}, error={}", category, e.getMessage());
            return null;
        }
    }

    /**
     * 设置配置缓存
     */
    public void setConfigCache(String category, AutoCompleteConfig config, int expireHours) {
        if (StringUtils.isBlank(category) || config == null) {
            return;
        }
        
        String cacheKey = buildConfigCacheKey(category);
        try {
            redisTemplate.opsForValue().set(cacheKey, config, expireHours, TimeUnit.HOURS);
            log.debug("设置配置缓存成功: category={}", category);
        } catch (Exception e) {
            log.warn("设置配置缓存失败: category={}, error={}", category, e.getMessage());
        }
    }

    /**
     * 获取统计信息缓存
     */
    public Object getStatsCache(String category) {
        if (StringUtils.isBlank(category)) {
            return null;
        }
        
        String cacheKey = buildStatsCacheKey(category);
        try {
            return redisTemplate.opsForValue().get(cacheKey);
        } catch (Exception e) {
            log.warn("获取统计信息缓存失败: category={}, error={}", category, e.getMessage());
            return null;
        }
    }

    /**
     * 设置统计信息缓存
     */
    public void setStatsCache(String category, Object stats, int expireHours) {
        if (StringUtils.isBlank(category) || stats == null) {
            return;
        }
        
        String cacheKey = buildStatsCacheKey(category);
        try {
            redisTemplate.opsForValue().set(cacheKey, stats, expireHours, TimeUnit.HOURS);
            log.debug("设置统计信息缓存成功: category={}", category);
        } catch (Exception e) {
            log.warn("设置统计信息缓存失败: category={}, error={}", category, e.getMessage());
        }
    }

    /**
     * 清除分类相关的所有缓存
     */
    public boolean clearCategoryCache(String category) {
        if (StringUtils.isBlank(category)) {
            return false;
        }
        
        try {
            String pattern = CACHE_PREFIX + "*" + category + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清除分类缓存成功: category={}, keys={}", category, keys.size());
                return true;
            }
            return true;
        } catch (Exception e) {
            log.error("清除分类缓存失败: category={}, error={}", category, e.getMessage());
            return false;
        }
    }

    /**
     * 清除所有自动补全缓存
     */
    public boolean clearAllCache() {
        try {
            String pattern = CACHE_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null && !keys.isEmpty()) {
                redisTemplate.delete(keys);
                log.info("清除所有自动补全缓存成功: keys={}", keys.size());
                return true;
            }
            return true;
        } catch (Exception e) {
            log.error("清除所有自动补全缓存失败: error={}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取缓存统计信息
     */
    public Object getCacheStats() {
        try {
            String pattern = CACHE_PREFIX + "*";
            Set<String> keys = redisTemplate.keys(pattern);
            
            java.util.Map<String, Object> stats = new java.util.HashMap<>();
            stats.put("totalKeys", keys != null ? keys.size() : 0);
            stats.put("popularKeys", countKeysByPattern(CACHE_PREFIX + POPULAR_PREFIX + "*"));
            stats.put("searchKeys", countKeysByPattern(CACHE_PREFIX + SEARCH_PREFIX + "*"));
            stats.put("configKeys", countKeysByPattern(CACHE_PREFIX + CONFIG_PREFIX + "*"));
            stats.put("statsKeys", countKeysByPattern(CACHE_PREFIX + STATS_PREFIX + "*"));
            
            return stats;
        } catch (Exception e) {
            log.error("获取缓存统计信息失败: error={}", e.getMessage());
            return null;
        }
    }

    /**
     * 检查缓存是否存在
     */
    public boolean hasCache(String cacheKey) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey));
        } catch (Exception e) {
            log.warn("检查缓存是否存在失败: key={}, error={}", cacheKey, e.getMessage());
            return false;
        }
    }

    /**
     * 构建热门项目缓存键
     */
    private String buildPopularCacheKey(String category, Integer limit) {
        return CACHE_PREFIX + POPULAR_PREFIX + category + ":" + limit;
    }

    /**
     * 构建搜索结果缓存键
     */
    private String buildSearchCacheKey(String category, String keyword, String searchType, Integer limit) {
        return CACHE_PREFIX + SEARCH_PREFIX + category + ":" + 
               (keyword != null ? keyword : "empty") + ":" + 
               searchType + ":" + limit;
    }

    /**
     * 构建配置缓存键
     */
    private String buildConfigCacheKey(String category) {
        return CACHE_PREFIX + CONFIG_PREFIX + category;
    }

    /**
     * 构建统计信息缓存键
     */
    private String buildStatsCacheKey(String category) {
        return CACHE_PREFIX + STATS_PREFIX + category;
    }

    /**
     * 统计匹配模式的键数量
     */
    private int countKeysByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            log.warn("统计键数量失败: pattern={}, error={}", pattern, e.getMessage());
            return 0;
        }
    }
}
