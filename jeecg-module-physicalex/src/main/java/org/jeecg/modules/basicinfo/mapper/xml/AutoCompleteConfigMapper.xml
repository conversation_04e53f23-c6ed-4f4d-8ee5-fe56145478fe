<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.AutoCompleteConfigMapper">

    <!-- 根据分类获取配置 -->
    <select id="getConfigByCategory" resultType="org.jeecg.modules.basicinfo.entity.AutoCompleteConfig">
        SELECT * 
        FROM auto_complete_config 
        WHERE category = #{category} 
          AND del_flag = 0 
          AND status = 1
        LIMIT 1
    </select>

</mapper>
