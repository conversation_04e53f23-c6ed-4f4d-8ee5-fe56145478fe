package org.jeecg.modules.basicinfo.service.impl;


import org.jeecg.modules.basicinfo.enums.JobStatusType;
import org.jeecg.modules.basicinfo.enums.ReportType;
import org.jeecg.modules.basicinfo.mapper.ReportSequenceMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * 体检报告编号生成服务
 */
@Service
public class ReportNumberService {

    @Autowired
    private ReportSequenceMapper sequenceDao;

    /**
     * 生成健康检查报告编号
     * 格式: TJ000 + 年份 + 5位序列号 (例如: TJ000202500001)
     * @return 报告编号
     */
    public synchronized String generateHealthCheckReportNumber() {
        int year = LocalDate.now().getYear();
        int sequence = sequenceDao.getAndIncrementSequence(ReportType.HEALTH_CHECK, year, null);

        return String.format("%s%d%05d", ReportType.HEALTH_CHECK.getPrefix(), year, sequence);
    }

    /**
     * 生成职业检查报告编号
     * 格式: 体检科(年份) ZYJKJH--岗位类型+序列号 (例如: 体检科(2025) ZYJKJH--GZ1)
     * @param jobStatusType 岗位类型
     * @return 报告编号
     */
    public synchronized String generateOccupationalCheckReportNumber(JobStatusType jobStatusType) {
        int year = LocalDate.now().getYear();
        int sequence = sequenceDao.getAndIncrementSequence(ReportType.OCCUPATIONAL_CHECK, year, jobStatusType);
        
        return String.format("%s(%d) ZYJKJH--%s%d", 
            ReportType.OCCUPATIONAL_CHECK.getPrefix(), 
            year, jobStatusType.name(),
            sequence
        );
    }
}
