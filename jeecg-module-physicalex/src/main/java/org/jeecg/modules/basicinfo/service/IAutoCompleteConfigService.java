package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.AutoCompleteConfig;

/**
 * @Description: 自动补全配置服务接口
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface IAutoCompleteConfigService extends IService<AutoCompleteConfig> {

    /**
     * 根据分类获取配置
     * @param category 分类标识
     * @return 配置信息
     */
    AutoCompleteConfig getConfigByCategory(String category);

    /**
     * 获取默认配置
     * @return 默认配置
     */
    AutoCompleteConfig getDefaultConfig();
}
