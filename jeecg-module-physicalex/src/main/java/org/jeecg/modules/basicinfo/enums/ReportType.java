package org.jeecg.modules.basicinfo.enums;

/**
 * 体检报告类型枚举
 */
public enum ReportType {
    HEALTH_CHECK("健康检查", "TJ000"),
    OCCUPATIONAL_CHECK("职业检查", "体检科");

    private final String description;
    private final String prefix;

    ReportType(String description, String prefix) {
        this.description = description;
        this.prefix = prefix;
    }

    public String getDescription() {
        return description;
    }

    public String getPrefix() {
        return prefix;
    }
}
