package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.AutoCompleteItem;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;

import java.util.List;

/**
 * @Description: 通用自动补全项目
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface AutoCompleteItemMapper extends BaseMapper<AutoCompleteItem> {

    /**
     * 搜索自动补全项目
     * @param category 分类标识
     * @param keyword 搜索关键词
     * @param searchType 搜索类型
     * @param limit 限制数量
     * @return 搜索结果列表
     */
    List<AutoCompleteResultVO> searchAutoCompleteItems(@Param("category") String category,
                                                       @Param("keyword") String keyword,
                                                       @Param("searchType") String searchType,
                                                       @Param("limit") Integer limit);

    /**
     * 获取热门推荐项目
     * @param category 分类标识
     * @param limit 限制数量
     * @return 热门项目列表
     */
    List<AutoCompleteResultVO> getPopularItems(@Param("category") String category,
                                              @Param("limit") Integer limit);

    /**
     * 检查项目是否存在
     * @param category 分类标识
     * @param name 名称
     * @return 存在的项目ID，不存在返回null
     */
    String checkItemExists(@Param("category") String category,
                          @Param("name") String name);

    /**
     * 更新使用频次
     * @param id 项目ID
     * @param increment 增量
     * @return 更新行数
     */
    int updateUseCount(@Param("id") String id,
                      @Param("increment") Integer increment);

    /**
     * 批量更新使用频次
     * @param ids 项目ID列表
     * @param increment 增量
     * @return 更新行数
     */
    int batchUpdateUseCount(@Param("ids") List<String> ids,
                           @Param("increment") Integer increment);

    /**
     * 获取分类下的项目数量
     * @param category 分类标识
     * @return 项目数量
     */
    int getItemCountByCategory(@Param("category") String category);

    /**
     * 获取分类下使用频次最高的项目
     * @param category 分类标识
     * @param limit 限制数量
     * @return 项目列表
     */
    List<AutoCompleteItem> getTopUsedItems(@Param("category") String category,
                                          @Param("limit") Integer limit);
}
