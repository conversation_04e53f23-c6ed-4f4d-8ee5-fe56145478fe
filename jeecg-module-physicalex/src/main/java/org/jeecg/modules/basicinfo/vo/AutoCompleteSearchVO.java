package org.jeecg.modules.basicinfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 自动补全搜索请求VO
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@ApiModel(value = "AutoCompleteSearchVO", description = "自动补全搜索请求VO")
public class AutoCompleteSearchVO {

    @ApiModelProperty(value = "分类标识", required = true)
    private String category;

    @ApiModelProperty(value = "搜索关键词")
    private String keyword;

    @ApiModelProperty(value = "搜索类型(name-仅名称,helpChar-仅助记码,both-两者)", example = "both")
    private String searchType = "both";

    @ApiModelProperty(value = "最大返回数量", example = "10")
    private Integer limit = 10;

    @ApiModelProperty(value = "是否包含热门推荐", example = "true")
    private Boolean includePopular = true;
}
