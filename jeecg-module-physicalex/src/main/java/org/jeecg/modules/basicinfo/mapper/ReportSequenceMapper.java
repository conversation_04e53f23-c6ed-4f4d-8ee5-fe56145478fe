package org.jeecg.modules.basicinfo.mapper;


import org.jeecg.modules.basicinfo.entity.ReportSequence;
import org.jeecg.modules.basicinfo.enums.JobStatusType;
import org.jeecg.modules.basicinfo.enums.ReportType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 体检报告编号序列数据访问层
 */
@Repository
public class ReportSequenceMapper {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    private final RowMapper<ReportSequence> rowMapper = new RowMapper<ReportSequence>() {
        @Override
        public ReportSequence mapRow(ResultSet rs, int rowNum) throws SQLException {
            ReportSequence sequence = new ReportSequence();
            sequence.setId(rs.getLong("id"));
            sequence.setSequenceType(ReportType.valueOf(rs.getString("sequence_type")));
            sequence.setYear(rs.getInt("year"));
            String jobStatusStr = rs.getString("job_status");
            if (jobStatusStr != null) {
                sequence.setJobStatus(JobStatusType.valueOf(jobStatusStr));
            }
            sequence.setCurrentSequence(rs.getInt("current_sequence"));
            sequence.setCreateTime(rs.getTimestamp("create_time").toLocalDateTime());
            sequence.setUpdateTime(rs.getTimestamp("update_time").toLocalDateTime());
            return sequence;
        }
    };

    /**
     * 查找健康检查序列
     */
    public ReportSequence findHealthCheckSequence(int year) {
        String sql = "SELECT * FROM report_sequence WHERE sequence_type = ? AND year = ? ";
        try {
            return jdbcTemplate.queryForObject(sql, rowMapper, ReportType.HEALTH_CHECK.name(), year);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 查找职业检查序列
     */
    public ReportSequence findOccupationalCheckSequence(int year, JobStatusType jobStatusType) {
        String sql = "SELECT * FROM report_sequence WHERE sequence_type = ? AND year = ? AND job_status = ?";
        try {
            return jdbcTemplate.queryForObject(sql, rowMapper, ReportType.OCCUPATIONAL_CHECK.name(), year, jobStatusType.name());
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 创建新的序列记录
     */
    public void insert(ReportSequence sequence) {
        String sql = "INSERT INTO report_sequence (sequence_type, year, job_status, current_sequence) VALUES (?, ?, ?, ?)";
        jdbcTemplate.update(sql,
            sequence.getSequenceType().name(),
            sequence.getYear(),
            sequence.getJobStatus() != null ? sequence.getJobStatus().name() : null,
            sequence.getCurrentSequence()
        );
    }

    /**
     * 更新序列号（使用乐观锁）
     */
    @Transactional
    public boolean updateSequence(Long id, int oldSequence, int newSequence) {
        String sql = "UPDATE report_sequence SET current_sequence = ?, update_time = CURRENT_TIMESTAMP WHERE id = ? AND current_sequence = ?";
        int updatedRows = jdbcTemplate.update(sql, newSequence, id, oldSequence);
        return updatedRows > 0;
    }

    /**
     * 获取并递增序列号（原子操作）
     */
    @Transactional
    public int getAndIncrementSequence(ReportType reportType, int year,  JobStatusType jobStatusType) {
       ReportSequence sequence;
        
        if (reportType == ReportType.HEALTH_CHECK) {
            sequence = findHealthCheckSequence(year);
        } else {
            sequence = findOccupationalCheckSequence(year, jobStatusType);
        }

        if (sequence == null) {
            // 创建新序列
            sequence = new ReportSequence(reportType, year, jobStatusType, 1);
            insert(sequence);
            return 1;
        } else {
            // 递增现有序列
            int currentSequence = sequence.getCurrentSequence();
            int newSequence = currentSequence + 1;
            
            // 使用乐观锁更新
            boolean updated = updateSequence(sequence.getId(), currentSequence, newSequence);
            if (updated) {
                return newSequence;
            } else {
                // 并发冲突，重试
                return getAndIncrementSequence(reportType, year, jobStatusType);
            }
        }
    }
}
