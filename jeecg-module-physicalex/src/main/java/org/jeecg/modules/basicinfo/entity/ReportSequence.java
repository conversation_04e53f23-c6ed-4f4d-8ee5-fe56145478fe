package org.jeecg.modules.basicinfo.entity;


import lombok.Data;
import org.jeecg.modules.basicinfo.enums.JobStatusType;
import org.jeecg.modules.basicinfo.enums.ReportType;

import java.time.LocalDateTime;

/**
 * 体检报告编号序列实体类
 */
@Data
public class ReportSequence {
    private Long id;
    private ReportType sequenceType;
    private Integer year;
    private JobStatusType jobStatus;
    private Integer currentSequence;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    public ReportSequence(ReportType sequenceType, Integer year, JobStatusType jobStatus, Integer currentSequence) {
        this.sequenceType = sequenceType;
        this.year = year;
        this.jobStatus = jobStatus;
        this.currentSequence = currentSequence;
    }
    public ReportSequence() {
    }


}
