package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.aspect.annotation.Dict;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 通用自动补全项目
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@TableName("auto_complete_item")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "auto_complete_item对象", description = "通用自动补全项目")
public class AutoCompleteItem implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**分类标识*/
    @Excel(name = "分类标识", width = 15)
    @ApiModelProperty(value = "分类标识(worktype-工种,department-部门,position-职位等)")
    private String category;

    /**名称*/
    @Excel(name = "名称", width = 15)
    @ApiModelProperty(value = "名称")
    private String name;

    /**助记码*/
    @Excel(name = "助记码", width = 15)
    @ApiModelProperty(value = "助记码(拼音首字母)")
    private String helpChar;

    /**全拼*/
    @Excel(name = "全拼", width = 15)
    @ApiModelProperty(value = "全拼")
    private String pinyin;

    /**使用频次*/
    @Excel(name = "使用频次", width = 15)
    @ApiModelProperty(value = "使用频次")
    private Integer useCount;

    /**排序号*/
    @Excel(name = "排序号", width = 15)
    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    /**状态*/
    @Excel(name = "状态", width = 15, dicCode = "valid_status")
    @Dict(dicCode = "valid_status")
    @ApiModelProperty(value = "状态(1-启用,0-禁用)")
    private Integer status;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @ApiModelProperty(value = "备注")
    private String remark;

    /**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**删除标志*/
    @ApiModelProperty(value = "删除标志(0-正常,1-删除)")
    private Integer delFlag;

    /**分类名称（非持久化字段）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "分类名称")
    private String categoryName;

    /**是否为新建项（非持久化字段）*/
    @TableField(exist = false)
    @ApiModelProperty(value = "是否为新建项")
    private Boolean isNew;
}
