package org.jeecg.modules.basicinfo.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Description: 拼音工具类
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Slf4j
public class PinyinUtil {

    // 简化的汉字拼音映射表（实际项目中建议使用专业的拼音库如pinyin4j）
    private static final Map<Character, String> PINYIN_MAP = new HashMap<>();
    private static final Map<Character, Character> FIRST_CHAR_MAP = new HashMap<>();

    static {
        initPinyinMap();
    }

    /**
     * 获取中文字符串的拼音首字母
     * @param chinese 中文字符串
     * @return 拼音首字母大写字符串
     */
    public static String getFirstChars(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                // 中文字符
                Character firstChar = FIRST_CHAR_MAP.get(c);
                if (firstChar != null) {
                    result.append(firstChar);
                } else {
                    // 简单的映射规则
                    result.append(getSimpleFirstChar(c));
                }
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toUpperCase(c));
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 获取中文字符串的全拼
     * @param chinese 中文字符串
     * @return 全拼小写字符串
     */
    public static String getFullPinyin(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        StringBuilder result = new StringBuilder();
        for (char c : chinese.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                // 中文字符
                String pinyin = PINYIN_MAP.get(c);
                if (pinyin != null) {
                    result.append(pinyin);
                } else {
                    // 简单的映射规则
                    result.append(getSimplePinyin(c));
                }
            } else if (Character.isLetter(c)) {
                // 英文字符
                result.append(Character.toLowerCase(c));
            } else if (Character.isDigit(c)) {
                // 数字字符
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 简单的首字母映射（基于Unicode编码范围）
     */
    private static char getSimpleFirstChar(char c) {
        int code = (int) c;
        // 简化的映射规则，实际应该使用完整的拼音库
        if (code >= 0x4e00 && code <= 0x4fff) return 'A';
        if (code >= 0x5000 && code <= 0x51ff) return 'B';
        if (code >= 0x5200 && code <= 0x53ff) return 'C';
        if (code >= 0x5400 && code <= 0x55ff) return 'D';
        if (code >= 0x5600 && code <= 0x57ff) return 'E';
        if (code >= 0x5800 && code <= 0x59ff) return 'F';
        if (code >= 0x5a00 && code <= 0x5bff) return 'G';
        if (code >= 0x5c00 && code <= 0x5dff) return 'H';
        if (code >= 0x5e00 && code <= 0x5fff) return 'I';
        if (code >= 0x6000 && code <= 0x61ff) return 'J';
        if (code >= 0x6200 && code <= 0x63ff) return 'K';
        if (code >= 0x6400 && code <= 0x65ff) return 'L';
        if (code >= 0x6600 && code <= 0x67ff) return 'M';
        if (code >= 0x6800 && code <= 0x69ff) return 'N';
        if (code >= 0x6a00 && code <= 0x6bff) return 'O';
        if (code >= 0x6c00 && code <= 0x6dff) return 'P';
        if (code >= 0x6e00 && code <= 0x6fff) return 'Q';
        if (code >= 0x7000 && code <= 0x71ff) return 'R';
        if (code >= 0x7200 && code <= 0x73ff) return 'S';
        if (code >= 0x7400 && code <= 0x75ff) return 'T';
        if (code >= 0x7600 && code <= 0x77ff) return 'U';
        if (code >= 0x7800 && code <= 0x79ff) return 'V';
        if (code >= 0x7a00 && code <= 0x7bff) return 'W';
        if (code >= 0x7c00 && code <= 0x7dff) return 'X';
        if (code >= 0x7e00 && code <= 0x7fff) return 'Y';
        if (code >= 0x8000 && code <= 0x9fa5) return 'Z';
        return 'A';
    }

    /**
     * 简单的拼音映射
     */
    private static String getSimplePinyin(char c) {
        // 这里是极简化的实现，实际应该使用专业的拼音库
        return String.valueOf(getSimpleFirstChar(c)).toLowerCase() + "i";
    }

    /**
     * 初始化拼音映射表（这里只是示例，实际应该使用完整的拼音库）
     */
    private static void initPinyinMap() {
        // 常用汉字的拼音映射（示例）
        PINYIN_MAP.put('电', "dian");
        FIRST_CHAR_MAP.put('电', 'D');
        
        PINYIN_MAP.put('工', "gong");
        FIRST_CHAR_MAP.put('工', 'G');
        
        PINYIN_MAP.put('焊', "han");
        FIRST_CHAR_MAP.put('焊', 'H');
        
        PINYIN_MAP.put('司', "si");
        FIRST_CHAR_MAP.put('司', 'S');
        
        PINYIN_MAP.put('机', "ji");
        FIRST_CHAR_MAP.put('机', 'J');
        
        PINYIN_MAP.put('操', "cao");
        FIRST_CHAR_MAP.put('操', 'C');
        
        PINYIN_MAP.put('作', "zuo");
        FIRST_CHAR_MAP.put('作', 'Z');
        
        PINYIN_MAP.put('维', "wei");
        FIRST_CHAR_MAP.put('维', 'W');
        
        PINYIN_MAP.put('修', "xiu");
        FIRST_CHAR_MAP.put('修', 'X');
        
        PINYIN_MAP.put('清', "qing");
        FIRST_CHAR_MAP.put('清', 'Q');
        
        PINYIN_MAP.put('洁', "jie");
        FIRST_CHAR_MAP.put('洁', 'J');
        
        PINYIN_MAP.put('管', "guan");
        FIRST_CHAR_MAP.put('管', 'G');
        
        PINYIN_MAP.put('理', "li");
        FIRST_CHAR_MAP.put('理', 'L');
        
        PINYIN_MAP.put('员', "yuan");
        FIRST_CHAR_MAP.put('员', 'Y');
        
        PINYIN_MAP.put('技', "ji");
        FIRST_CHAR_MAP.put('技', 'J');
        
        PINYIN_MAP.put('术', "shu");
        FIRST_CHAR_MAP.put('术', 'S');
        
        PINYIN_MAP.put('质', "zhi");
        FIRST_CHAR_MAP.put('质', 'Z');
        
        PINYIN_MAP.put('检', "jian");
        FIRST_CHAR_MAP.put('检', 'J');
        
        PINYIN_MAP.put('安', "an");
        FIRST_CHAR_MAP.put('安', 'A');
        
        PINYIN_MAP.put('全', "quan");
        FIRST_CHAR_MAP.put('全', 'Q');
        
        // 可以继续添加更多常用汉字的映射...
    }

    /**
     * 检查字符串是否包含中文
     */
    public static boolean containsChinese(String str) {
        if (StringUtils.isBlank(str)) {
            return false;
        }
        
        for (char c : str.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成智能助记码
     * 优先使用拼音首字母，如果没有中文则使用英文首字母
     */
    public static String generateSmartHelpChar(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        if (containsChinese(name)) {
            return getFirstChars(name);
        } else {
            // 英文或数字，直接提取首字母
            StringBuilder result = new StringBuilder();
            for (char c : name.toCharArray()) {
                if (Character.isLetter(c)) {
                    result.append(Character.toUpperCase(c));
                } else if (Character.isDigit(c)) {
                    result.append(c);
                }
            }
            return result.toString();
        }
    }

    /**
     * 生成智能全拼
     */
    public static String generateSmartPinyin(String name) {
        if (StringUtils.isBlank(name)) {
            return "";
        }

        if (containsChinese(name)) {
            return getFullPinyin(name);
        } else {
            // 英文或数字，转为小写
            return name.toLowerCase().replaceAll("[^a-zA-Z0-9]", "");
        }
    }
}
