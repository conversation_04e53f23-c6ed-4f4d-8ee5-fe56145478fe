package org.jeecg.modules.basicinfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 自动补全结果VO
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@ApiModel(value = "AutoCompleteResultVO", description = "自动补全结果VO")
public class AutoCompleteResultVO {

    @ApiModelProperty(value = "项目ID")
    private String id;

    @ApiModelProperty(value = "分类标识")
    private String category;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "助记码")
    private String helpChar;

    @ApiModelProperty(value = "全拼")
    private String pinyin;

    @ApiModelProperty(value = "使用频次")
    private Integer useCount;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder;

    @ApiModelProperty(value = "是否为新建项")
    private Boolean isNew = false;

    @ApiModelProperty(value = "匹配类型(name-名称匹配,helpChar-助记码匹配,pinyin-拼音匹配)")
    private String matchType;

    @ApiModelProperty(value = "匹配得分(用于排序)")
    private Integer matchScore = 0;
}
