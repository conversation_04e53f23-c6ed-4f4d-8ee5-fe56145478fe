package org.jeecg.modules.basicinfo.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 自动补全创建请求VO
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Data
@ApiModel(value = "AutoCompleteCreateVO", description = "自动补全创建请求VO")
public class AutoCompleteCreateVO {

    @ApiModelProperty(value = "分类标识", required = true)
    private String category;

    @ApiModelProperty(value = "名称", required = true)
    private String name;

    @ApiModelProperty(value = "助记码")
    private String helpChar;

    @ApiModelProperty(value = "全拼")
    private String pinyin;

    @ApiModelProperty(value = "排序号")
    private Integer sortOrder = 0;

    @ApiModelProperty(value = "备注")
    private String remark;
}
