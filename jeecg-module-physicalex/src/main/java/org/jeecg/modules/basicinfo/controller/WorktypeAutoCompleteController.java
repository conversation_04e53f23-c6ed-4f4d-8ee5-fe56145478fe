package org.jeecg.modules.basicinfo.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.basicinfo.entity.AutoCompleteItem;
import org.jeecg.modules.basicinfo.service.IAutoCompleteItemService;
import org.jeecg.modules.basicinfo.vo.AutoCompleteCreateVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteSearchVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @Description: 工种自动补全专用接口
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Api(tags = "工种自动补全")
@RestController
@RequestMapping("/basicinfo/worktypeAutoComplete")
@Slf4j
public class WorktypeAutoCompleteController {

    @Autowired
    private IAutoCompleteItemService autoCompleteItemService;

    private static final String WORKTYPE_CATEGORY = "worktype";

    /**
     * 工种自动补全搜索
     */
    @ApiOperation(value = "工种自动补全搜索", notes = "工种自动补全搜索")
    @GetMapping(value = "/search")
    public Result<List<AutoCompleteResultVO>> search(@RequestParam(required = false) String keyword,
                                                    @RequestParam(defaultValue = "both") String searchType,
                                                    @RequestParam(defaultValue = "10") Integer limit) {
        try {
            AutoCompleteSearchVO searchVO = new AutoCompleteSearchVO();
            searchVO.setCategory(WORKTYPE_CATEGORY);
            searchVO.setKeyword(keyword);
            searchVO.setSearchType(searchType);
            searchVO.setLimit(limit);
            searchVO.setIncludePopular(true);
            
            List<AutoCompleteResultVO> results = autoCompleteItemService.autoCompleteSearch(searchVO);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("工种自动补全搜索失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门工种
     */
    @ApiOperation(value = "获取热门工种", notes = "获取使用频次最高的工种")
    @GetMapping(value = "/popular")
    public Result<List<AutoCompleteResultVO>> getPopular(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<AutoCompleteResultVO> results = autoCompleteItemService.getPopularItems(WORKTYPE_CATEGORY, limit);
            return Result.OK(results);
        } catch (Exception e) {
            log.error("获取热门工种失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 自动创建工种
     */
    @ApiOperation(value = "自动创建工种", notes = "当工种不存在时自动创建")
    @PostMapping(value = "/autoCreate")
    public Result<AutoCompleteItem> autoCreate(@RequestParam String name,
                                              @RequestParam(required = false) String helpChar,
                                              @RequestParam(required = false) String remark) {
        try {
            // 先检查是否已存在
            String existingId = autoCompleteItemService.checkItemExists(WORKTYPE_CATEGORY, name);
            if (existingId != null) {
                AutoCompleteItem existing = autoCompleteItemService.getById(existingId);
                return Result.OK(existing);
            }

            AutoCompleteCreateVO createVO = new AutoCompleteCreateVO();
            createVO.setCategory(WORKTYPE_CATEGORY);
            createVO.setName(name);
            createVO.setHelpChar(helpChar);
            createVO.setRemark(remark);
            
            AutoCompleteItem item = autoCompleteItemService.autoCreateItem(createVO);
            return Result.OK(item);
        } catch (Exception e) {
            log.error("自动创建工种失败", e);
            return Result.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新工种使用频次
     */
    @ApiOperation(value = "更新工种使用频次", notes = "记录工种使用情况")
    @PostMapping(value = "/updateUseCount")
    public Result<String> updateUseCount(@RequestParam String id) {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userId = loginUser != null ? loginUser.getId() : "anonymous";
            String userName = loginUser != null ? loginUser.getRealname() : "匿名用户";
            
            boolean success = autoCompleteItemService.updateUseCount(id, userId, userName);
            if (success) {
                return Result.OK("使用频次更新成功");
            } else {
                return Result.error("使用频次更新失败");
            }
        } catch (Exception e) {
            log.error("更新工种使用频次失败", e);
            return Result.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新工种使用频次
     */
    @ApiOperation(value = "批量更新工种使用频次", notes = "批量记录工种使用情况")
    @PostMapping(value = "/batchUpdateUseCount")
    public Result<String> batchUpdateUseCount(@RequestBody List<String> ids) {
        try {
            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            String userId = loginUser != null ? loginUser.getId() : "anonymous";
            String userName = loginUser != null ? loginUser.getRealname() : "匿名用户";
            
            boolean success = autoCompleteItemService.batchUpdateUseCount(ids, userId, userName);
            if (success) {
                return Result.OK("批量使用频次更新成功");
            } else {
                return Result.error("批量使用频次更新失败");
            }
        } catch (Exception e) {
            log.error("批量更新工种使用频次失败", e);
            return Result.error("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取工种统计信息
     */
    @ApiOperation(value = "获取工种统计信息", notes = "获取工种相关的统计数据")
    @GetMapping(value = "/stats")
    public Result<Object> getStats() {
        try {
            Object stats = autoCompleteItemService.getCategoryStats(WORKTYPE_CATEGORY);
            return Result.OK(stats);
        } catch (Exception e) {
            log.error("获取工种统计信息失败", e);
            return Result.error("获取失败: " + e.getMessage());
        }
    }

    /**
     * 刷新工种缓存
     */
    @ApiOperation(value = "刷新工种缓存", notes = "清除并重新加载工种缓存")
    @PostMapping(value = "/refreshCache")
    public Result<String> refreshCache() {
        try {
            boolean success = autoCompleteItemService.refreshCategoryCache(WORKTYPE_CATEGORY);
            if (success) {
                return Result.OK("工种缓存刷新成功");
            } else {
                return Result.error("工种缓存刷新失败");
            }
        } catch (Exception e) {
            log.error("刷新工种缓存失败", e);
            return Result.error("刷新失败: " + e.getMessage());
        }
    }
}
