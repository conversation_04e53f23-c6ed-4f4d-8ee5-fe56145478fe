<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.basicinfo.mapper.AutoCompleteItemMapper">

    <!-- 搜索自动补全项目 -->
    <select id="searchAutoCompleteItems" resultType="org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO">
        SELECT 
            id,
            category,
            name,
            help_char as helpChar,
            pinyin,
            use_count as useCount,
            sort_order as sortOrder,
            false as isNew,
            CASE 
                <if test="searchType == 'name' or searchType == 'both'">
                WHEN name LIKE CONCAT('%', #{keyword}, '%') THEN 'name'
                </if>
                <if test="searchType == 'helpChar' or searchType == 'both'">
                WHEN help_char LIKE CONCAT('%', #{keyword}, '%') THEN 'helpChar'
                </if>
                <if test="searchType == 'both'">
                WHEN pinyin LIKE CONCAT('%', #{keyword}, '%') THEN 'pinyin'
                </if>
                ELSE 'unknown'
            END as matchType,
            CASE 
                <if test="searchType == 'name' or searchType == 'both'">
                WHEN name = #{keyword} THEN 100
                WHEN name LIKE CONCAT(#{keyword}, '%') THEN 90
                WHEN name LIKE CONCAT('%', #{keyword}, '%') THEN 80
                </if>
                <if test="searchType == 'helpChar' or searchType == 'both'">
                WHEN help_char = #{keyword} THEN 95
                WHEN help_char LIKE CONCAT(#{keyword}, '%') THEN 85
                WHEN help_char LIKE CONCAT('%', #{keyword}, '%') THEN 75
                </if>
                <if test="searchType == 'both'">
                WHEN pinyin = #{keyword} THEN 70
                WHEN pinyin LIKE CONCAT(#{keyword}, '%') THEN 60
                WHEN pinyin LIKE CONCAT('%', #{keyword}, '%') THEN 50
                </if>
                ELSE 0
            END as matchScore
        FROM auto_complete_item 
        WHERE category = #{category} 
          AND del_flag = 0 
          AND status = 1
          <if test="keyword != null and keyword != ''">
          AND (
              <if test="searchType == 'name' or searchType == 'both'">
              name LIKE CONCAT('%', #{keyword}, '%')
              </if>
              <if test="searchType == 'helpChar' or searchType == 'both'">
              <if test="searchType == 'both'"> OR </if>
              help_char LIKE CONCAT('%', #{keyword}, '%')
              </if>
              <if test="searchType == 'both'">
              OR pinyin LIKE CONCAT('%', #{keyword}, '%')
              </if>
          )
          </if>
        ORDER BY matchScore DESC, use_count DESC, sort_order ASC
        <if test="limit != null and limit > 0">
        LIMIT #{limit}
        </if>
    </select>

    <!-- 获取热门推荐项目 -->
    <select id="getPopularItems" resultType="org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO">
        SELECT 
            id,
            category,
            name,
            help_char as helpChar,
            pinyin,
            use_count as useCount,
            sort_order as sortOrder,
            false as isNew,
            'popular' as matchType,
            use_count as matchScore
        FROM auto_complete_item 
        WHERE category = #{category} 
          AND del_flag = 0 
          AND status = 1
        ORDER BY use_count DESC, sort_order ASC
        <if test="limit != null and limit > 0">
        LIMIT #{limit}
        </if>
    </select>

    <!-- 检查项目是否存在 -->
    <select id="checkItemExists" resultType="java.lang.String">
        SELECT id 
        FROM auto_complete_item 
        WHERE category = #{category} 
          AND name = #{name} 
          AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 更新使用频次 -->
    <update id="updateUseCount">
        UPDATE auto_complete_item 
        SET use_count = use_count + #{increment},
            update_time = NOW()
        WHERE id = #{id} 
          AND del_flag = 0
    </update>

    <!-- 批量更新使用频次 -->
    <update id="batchUpdateUseCount">
        UPDATE auto_complete_item 
        SET use_count = use_count + #{increment},
            update_time = NOW()
        WHERE del_flag = 0
          AND id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 获取分类下的项目数量 -->
    <select id="getItemCountByCategory" resultType="java.lang.Integer">
        SELECT COUNT(*) 
        FROM auto_complete_item 
        WHERE category = #{category} 
          AND del_flag = 0 
          AND status = 1
    </select>

    <!-- 获取分类下使用频次最高的项目 -->
    <select id="getTopUsedItems" resultType="org.jeecg.modules.basicinfo.entity.AutoCompleteItem">
        SELECT * 
        FROM auto_complete_item 
        WHERE category = #{category} 
          AND del_flag = 0 
          AND status = 1
        ORDER BY use_count DESC, sort_order ASC
        <if test="limit != null and limit > 0">
        LIMIT #{limit}
        </if>
    </select>

</mapper>
