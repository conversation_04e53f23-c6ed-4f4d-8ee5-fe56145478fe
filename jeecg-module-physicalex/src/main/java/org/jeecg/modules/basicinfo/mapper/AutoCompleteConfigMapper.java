package org.jeecg.modules.basicinfo.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.basicinfo.entity.AutoCompleteConfig;

/**
 * @Description: 自动补全配置
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface AutoCompleteConfigMapper extends BaseMapper<AutoCompleteConfig> {

    /**
     * 根据分类获取配置
     * @param category 分类标识
     * @return 配置信息
     */
    AutoCompleteConfig getConfigByCategory(@Param("category") String category);
}
