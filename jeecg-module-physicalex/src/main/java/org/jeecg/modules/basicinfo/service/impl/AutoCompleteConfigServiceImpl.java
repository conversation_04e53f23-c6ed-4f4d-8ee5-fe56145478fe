package org.jeecg.modules.basicinfo.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.basicinfo.entity.AutoCompleteConfig;
import org.jeecg.modules.basicinfo.mapper.AutoCompleteConfigMapper;
import org.jeecg.modules.basicinfo.service.IAutoCompleteConfigService;
import org.springframework.stereotype.Service;
import org.jeecg.modules.basicinfo.cache.AutoCompleteCacheManager;

import javax.annotation.Resource;

/**
 * @Description: 自动补全配置服务实现
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
@Service
@Slf4j
public class AutoCompleteConfigServiceImpl extends ServiceImpl<AutoCompleteConfigMapper, AutoCompleteConfig> implements IAutoCompleteConfigService {

    @Resource
    private AutoCompleteCacheManager autoCompleteCacheManager;

    private static final int CONFIG_CACHE_EXPIRE_HOURS = 24;

    @Override
    public AutoCompleteConfig getConfigByCategory(String category) {
        if (StringUtils.isBlank(category)) {
            return getDefaultConfig();
        }

        // 尝试从缓存获取
        AutoCompleteConfig cachedConfig = autoCompleteCacheManager.getConfigCache(category);
        if (cachedConfig != null) {
            return cachedConfig;
        }

        // 从数据库查询
        AutoCompleteConfig config = baseMapper.getConfigByCategory(category);
        if (config == null) {
            config = getDefaultConfig();
        }

        // 存入缓存
        autoCompleteCacheManager.setConfigCache(category, config, CONFIG_CACHE_EXPIRE_HOURS);

        return config;
    }

    @Override
    public AutoCompleteConfig getDefaultConfig() {
        AutoCompleteConfig defaultConfig = new AutoCompleteConfig();
        defaultConfig.setCategory("default");
        defaultConfig.setCategoryName("默认配置");
        defaultConfig.setCacheEnabled(1);
        defaultConfig.setCacheExpireHours(24);
        defaultConfig.setAutoCreateEnabled(1);
        defaultConfig.setMaxSuggestions(10);
        defaultConfig.setSearchType("both");
        defaultConfig.setStatus(1);
        return defaultConfig;
    }
}
