package org.jeecg.modules.basicinfo.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.basicinfo.entity.AutoCompleteItem;
import org.jeecg.modules.basicinfo.vo.AutoCompleteCreateVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteResultVO;
import org.jeecg.modules.basicinfo.vo.AutoCompleteSearchVO;

import java.util.List;

/**
 * @Description: 通用自动补全项目服务接口
 * @Author: jeecg-boot
 * @Date: 2024-12-24
 * @Version: V1.0
 */
public interface IAutoCompleteItemService extends IService<AutoCompleteItem> {

    /**
     * 自动补全搜索
     * @param searchVO 搜索参数
     * @return 搜索结果列表
     */
    List<AutoCompleteResultVO> autoCompleteSearch(AutoCompleteSearchVO searchVO);

    /**
     * 获取热门推荐
     * @param category 分类标识
     * @param limit 限制数量
     * @return 热门项目列表
     */
    List<AutoCompleteResultVO> getPopularItems(String category, Integer limit);

    /**
     * 自动创建项目
     * @param createVO 创建参数
     * @return 创建的项目
     */
    AutoCompleteItem autoCreateItem(AutoCompleteCreateVO createVO);

    /**
     * 更新使用频次
     * @param id 项目ID
     * @param userId 用户ID
     * @param userName 用户名
     * @return 是否成功
     */
    boolean updateUseCount(String id, String userId, String userName);

    /**
     * 批量更新使用频次
     * @param ids 项目ID列表
     * @param userId 用户ID
     * @param userName 用户名
     * @return 是否成功
     */
    boolean batchUpdateUseCount(List<String> ids, String userId, String userName);

    /**
     * 检查项目是否存在
     * @param category 分类标识
     * @param name 名称
     * @return 存在的项目ID，不存在返回null
     */
    String checkItemExists(String category, String name);

    /**
     * 获取分类统计信息
     * @param category 分类标识
     * @return 统计信息
     */
    Object getCategoryStats(String category);

    /**
     * 清除分类缓存
     * @param category 分类标识
     * @return 是否成功
     */
    boolean clearCategoryCache(String category);

    /**
     * 刷新分类缓存
     * @param category 分类标识
     * @return 是否成功
     */
    boolean refreshCategoryCache(String category);
}
