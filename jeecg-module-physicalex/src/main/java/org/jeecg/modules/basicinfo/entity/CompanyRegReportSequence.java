package org.jeecg.modules.basicinfo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;
@TableName("company_reg_report_sequence")
@Data
public class CompanyRegReportSequence {
    private Long id;
    private String reportNo;
    private String reportType;
    private String companyRegId;
    private String companyRegName;
    private String jobStatus;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}
