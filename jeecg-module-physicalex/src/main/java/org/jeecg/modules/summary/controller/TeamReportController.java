package org.jeecg.modules.summary.controller;


import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.modules.summary.bo.teamreport.TeamReportVO;
import org.jeecg.modules.summary.bo.teamreport.ZyTeamReportVO;
import org.jeecg.modules.summary.service.ITeamReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
@Slf4j
@RestController
@RequestMapping("/summary/teamReport")
public class TeamReportController {

    @Autowired
    private ITeamReportService reportService;

    @GetMapping(value = "/getTeamReport")
    public Result<?> getTeamReport(@RequestParam("companyRegId") String companyRegId,@RequestParam(value = "companyDeptId",required = false)String companyDeptId) {
        try {
            TeamReportVO teamReportStat = reportService.getTeamReportStat(companyRegId, companyDeptId);
            return Result.OK(teamReportStat);
        } catch (Exception e) {
            log.error("团检报告查询异常", e);
            return Result.error("查询失败！"+e.getMessage());
        }
    }
    @GetMapping(value = "/getZyTeamReport")
    public Result<?> getZyTeamReport(@RequestParam("companyRegId") String companyRegId,@RequestParam(value = "companyDeptId",required = false)String companyDeptId,@RequestParam(value = "jobStatus",required = false)String jobStatus) {
        try {
            ZyTeamReportVO zyTeamReportStat = reportService.getZyTeamReportStat(companyRegId, companyDeptId,jobStatus);
            return Result.OK(zyTeamReportStat);
        } catch (Exception e) {
            log.error("职业团检报告查询异常", e);
            return Result.error("查询失败！"+e.getMessage());
        }

    }

}
