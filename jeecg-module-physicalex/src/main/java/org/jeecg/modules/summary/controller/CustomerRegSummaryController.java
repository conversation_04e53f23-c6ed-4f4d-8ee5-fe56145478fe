package org.jeecg.modules.summary.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.excommons.BatchResult;
import org.jeecg.excommons.ExConstants;
import org.jeecg.modules.ai.bo.AIMessage;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.service.ICustomerRegService;
import org.jeecg.modules.station.bo.StatusStat;
import org.jeecg.modules.station.service.ICustomerRegDepartSummaryService;
import org.jeecg.modules.station.service.ICustomerRegItemResultService;
import org.jeecg.modules.summary.bo.AbnormalSummaryAndAdvice;
import org.jeecg.modules.summary.bo.DepartAndGroupBean;
import org.jeecg.modules.summary.bo.ReportBean;
import org.jeecg.modules.summary.entity.AdviceBean;
import org.jeecg.modules.summary.entity.CustomerRegSummary;
import org.jeecg.modules.summary.service.ICustomerRegReportService;
import org.jeecg.modules.summary.service.ICustomerRegSummaryService;
import org.jeecg.modules.summary.service.ISummaryAdviceService;
import org.jeecg.modules.ai.service.DashScopeService;
import org.jeecg.modules.basicinfo.service.ISysSettingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
// 需要引入Spring Web依赖
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * @Description: 总检
 * @Author: jeecg-boot
 * @Date: 2024-05-20
 * @Version: V1.0
 */
@Api(tags = "总检")
@RestController
@RequestMapping("/summary/customerRegSummary")
@Slf4j
public class CustomerRegSummaryController extends JeecgController<CustomerRegSummary, ICustomerRegSummaryService> {
    @Autowired
    private ICustomerRegSummaryService customerRegSummaryService;
    @Autowired
    private ICustomerRegDepartSummaryService customerRegDepartSummaryService;
    @Autowired
    private ISummaryAdviceService summaryAdviceService;
    @Autowired
    private ICustomerRegReportService customerRegReportService;
    @Autowired
    private ICustomerRegItemResultService customerRegItemResultService;
    @Autowired
    private ICustomerRegService customerRegService;

    // 用于跟踪活动的SseEmitter实例
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();


    @ApiOperation(value = "流式生成AI总检建议", notes = "使用SSE协议实时推送AI生成的总检建议")
    @PostMapping("/ai/streamSummary")
    //@RequiresPermissions("summary:customer_reg_summary:aiSummary")
    public SseEmitter streamAiSummary(@RequestBody JSONObject info) {
        String clientId = UUID.randomUUID().toString();
        SseEmitter emitter = new SseEmitter(300_000L); // 300秒超时
        AtomicBoolean completed = new AtomicBoolean(false);
        // 1. 超时处理
        emitter.onTimeout(() -> {
            log.warn("SSE连接超时");
            try {
                emitters.remove(clientId);
                emitter.send(SseEmitter.event().name("timeout").data("SSE连接超时关闭"));
            } catch (IOException e) {
                log.error("超时事件推送失败", e);
            } finally {
                emitter.complete();
            }
        });

        // 2. 客户端关闭连接
        emitter.onCompletion(() -> {
            emitters.remove(clientId);
            log.info("SSE连接已完成或主动关闭");
        });

        // 3. 连接发生异常
        emitter.onError((Throwable t) -> {
            log.error("SSE连接发生异常", t);
            try {
                emitters.remove(clientId);
                emitter.send(SseEmitter.event().name("error").data("SSE连接发生异常，已关闭"));
            } catch (IOException e) {
                log.error("异常事件推送失败", e);
            } finally {
                emitter.completeWithError(t);
            }
        });

        try {
            // 存储emitter实例
            emitters.put(clientId, emitter);
            // 4. 生成并推送AI总检建
            List<AIMessage> messages = info.getJSONArray("messages").toJavaList(AIMessage.class);
            String modId = info.getString("modId");

            customerRegSummaryService.generateAiSummaryAsync(messages, emitter, completed, modId, clientId); // 传递 completed 标志和客户端ID

            emitter.onTimeout(() -> {
                log.warn("SSE连接超时");
                emitter.complete();
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return emitter;
    }


    // 用于客户端主动取消请求
    @PostMapping("/cancelStream")
    public ResponseEntity<String> cancelStream(@RequestParam String clientId) {
        SseEmitter emitter = emitters.get(clientId);
        if (emitter != null) {
            emitter.complete();
            emitters.remove(clientId);
            return ResponseEntity.ok("Stream canceled successfully");
        }
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body("No active stream found for the given ID");
    }


    /**
     * 根据ids批量预总检
     */
    @AutoLog(value = "总检-根据ids批量预总检")
    @ApiOperation(value = "总检-根据ids批量预总检", notes = "总检-根据ids批量预总检")
    @PostMapping(value = "/preSummaryByIds")
    public Result<?> preSummaryByIds(@RequestBody JSONObject info) {
        List<String> idList = info.getJSONArray("ids").toJavaList(String.class);
        BatchResult<CustomerReg> batchResult = customerRegSummaryService.preSummaryByIds(idList, ExConstants.PRE_SUMMARY_TYPE_手动);
        return Result.OK("操作成功！", batchResult);
    }

    /**
     * 根据ids批量取消预总检
     */
    @AutoLog(value = "总检-根据ids批量取消预总检")
    @ApiOperation(value = "总检-根据ids批量取消预总检", notes = "总检-根据ids批量取消预总检")
    @PostMapping(value = "/unPreSummaryByIds")
    public Result<?> unPreSummaryByIds(@RequestBody JSONObject info) {
        List<String> idList = info.getJSONArray("ids").toJavaList(String.class);
        BatchResult<CustomerReg> batchResult = customerRegSummaryService.unPreSummaryByIds(idList);
        return Result.OK("操作成功！", batchResult);
    }


    /**
     * 更新 reportEditLockFlag
     *
     * @param customerRegId
     * @param reportEditLockFlag
     * @return
     */
    @GetMapping("/updateReportEditLockFlag")
    public Result<?> updateReportEditLockFlag(@RequestParam(name = "customerRegId") String customerRegId, @RequestParam(name = "reportEditLockFlag") String reportEditLockFlag) {
        CustomerReg customerReg = customerRegService.getById(customerRegId);
        if (customerReg == null) {
            return Result.error("未找到对应数据");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (StringUtils.equals(reportEditLockFlag, "1")) {

            if (StringUtils.equals(customerReg.getReportEditLockFlag(), "1") && !StringUtils.equals(customerReg.getReportEditLockBy(), sysUser.getUsername())) {
                return Result.error("锁定失败，该报告已被其他用户锁定！");
            }
            customerReg.setReportEditLockBy(sysUser.getUsername());
            customerReg.setReportEditLocker(sysUser.getRealname());
            customerReg.setReportEditLockTime(new java.util.Date());
        }

        customerReg.setReportEditLockFlag(reportEditLockFlag);
        customerRegService.updateById(customerReg);
        return Result.OK("更新成功！");
    }

    //listGroupByCustomerReg
    @ApiOperation(value = "总检-根据登记ID查询体检小项", notes = "总检-根据登记ID查询体检小项")
    @GetMapping(value = "/listGroupByCustomerReg")
    public Result<?> listGroupByCustomerReg(String regId) {
        List<DepartAndGroupBean> departAndGroupBeanList = customerRegItemResultService.listDepartAndGroup(regId);
        return Result.OK(departAndGroupBeanList);
    }

    //saveHealthCardResult
    @ApiOperation(value = "总检-获取报告数据", notes = "总检-获取报告数据")
    @GetMapping(value = "/getReportData")
    public Result<?> getReportData(String customerRegId) {

        ReportBean reportBean = customerRegReportService.getReportBean(customerRegId);
        return Result.OK("获取成功！", reportBean);
    }

    //updateReportPrintTimes
    @ApiOperation(value = "总检-更新报告打印次数", notes = "总检-更新报告打印次数")
    @GetMapping(value = "/updateReportPrintTimes")
    public Result<?> updateReportPrintTimes(String id) {

        customerRegSummaryService.updateReportPrintTimes(id);
        return Result.OK("更新成功！");
    }


    //saveHealthCardResult
    @ApiOperation(value = "总检-保存体检结果", notes = "总检-保存体检结果")
    @PostMapping(value = "/saveHealthCardResult")
    public Result<?> saveHealthCardResult(@RequestBody JSONObject jsonObject) {
        String customerRegId = jsonObject.getString("customerRegId");
        String summaryId = jsonObject.getString("summaryId");
        String healthCardResult = jsonObject.getString("healthCardResult");

        customerRegSummaryService.saveHealthCardResult(customerRegId, summaryId, healthCardResult);
        return Result.OK("操作成功！");
    }


    /**
     * 根据customerRegId获取自动生成的总检建议
     *
     * @param customerRegId
     * @return
     */
    //@AutoLog(value = "总检-分页列表查询")
    @ApiOperation(value = "总检-根据customerRegId获取自动生成的总检建议", notes = "总检-根据customerRegId获取自动生成的总检建议")
    @GetMapping(value = "/getGeneratedSummaryAdviceByReg")
    public Result<?> getGeneratedSummaryAdviceByReg(String customerRegId) {
        List<AdviceBean> adviceList = null;
        try {
            adviceList = summaryAdviceService.generateAdviceByRegId(customerRegId);
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
        return Result.OK(adviceList);
    }

    /**
     * 根据customerRegId查询总检建议
     *
     * @param customerRegId
     * @return
     */
    //@AutoLog(value = "总检-分页列表查询")
    @ApiOperation(value = "总检-根据customerRegId查询总检建议", notes = "总检-根据customerRegId查询总检建议")
    @GetMapping(value = "/getSummaryAdviceByReg")
    public Result<?> getSummaryByReg(String customerRegId, String departSummaryType, String aiSummary, String abnormalDepartSummary) {
        try {
            CustomerRegSummary summary = customerRegSummaryService.getByRegId(customerRegId);
            CustomerReg customerReg = customerRegService.getById(customerRegId);
            boolean aiSummaryBool = StringUtils.equals(aiSummary, "true");
            if (summary == null) {
                summary = new CustomerRegSummary();
                AbnormalSummaryAndAdvice abnormalSummaryAndAdvice = customerRegSummaryService.generateAbnormalSummaryAndAdvice(customerReg, aiSummaryBool);
                List<AdviceBean> adviceBeanList = abnormalSummaryAndAdvice.getAdviceList();
                summary.setSummaryJson(adviceBeanList);
                summary.setCharacterSummary(abnormalSummaryAndAdvice.getAbnormalSummary());
                summary.setCustomerRegId(customerRegId);

                customerRegSummaryService.saveOrUpdateCustomerSummary(summary);
            } else {
                if (StringUtils.equals(summary.getStatus(), ExConstants.SUMMARY_STATUS_已初检) && StringUtils.isBlank(summary.getCharacterSummary())) {
                    AbnormalSummaryAndAdvice abnormalSummaryAndAdvice = customerRegSummaryService.generateAbnormalSummaryAndAdvice(customerReg, aiSummaryBool);
                    List<AdviceBean> adviceBeanList = abnormalSummaryAndAdvice.getAdviceList();
                    summary.setSummaryJson(adviceBeanList);
                    summary.setCharacterSummary(abnormalSummaryAndAdvice.getAbnormalSummary());
                    summary.setCustomerRegId(customerRegId);
                    customerRegSummaryService.saveOrUpdateCustomerSummary(summary);
                }
            }
            return Result.OK(summary);
        } catch (Exception e) {
            log.error("getSummaryByReg error", e);
            return Result.error(e.getMessage());
        }
    }


    //@AutoLog(value = "总检-分页列表查询")
    @ApiOperation(value = "总检-根据文本获取总检建议", notes = "总检-根据文本获取总检建议")
    @PostMapping(value = "/getSummaryAdviceByText")
    public Result<?> getSummaryAdviceByText(@RequestBody JSONObject jsonObject) {
        String summaryText = jsonObject.getString("summaryText");
        String customerRegId = jsonObject.getString("customerRegId");
        String aiSummary = jsonObject.getString("aiSummary");

        boolean aiSummaryBool = StringUtils.equals(aiSummary, "true");
        List<AdviceBean> adviceList = null;
        try {
            adviceList = customerRegSummaryService.generateAdviceByDepartSummary(aiSummaryBool, summaryText, customerRegId);
            JSONObject resp = new JSONObject();
            resp.put("customerRegId", customerRegId);
            resp.put("adviceList", adviceList);
            return Result.OK("刷新成功！", resp);
        } catch (Exception e) {
            log.error("getSummaryAdviceByText error", e);
            return Result.error(e.getMessage());
        }
    }

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "客户登记-分页列表查询", notes = "客户登记-分页列表查询")
    @GetMapping(value = "/listReg")
    public Result<IPage<CustomerReg>> listReg(@RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        long start = System.currentTimeMillis();
        Page<CustomerReg> page = new Page<CustomerReg>(pageNo, pageSize);

        String summaryStatus = StringUtils.trimToNull(req.getParameter("summaryStatus"));
        String status = StringUtils.trimToNull(req.getParameter("status"));
        String qualified = StringUtils.trimToNull(req.getParameter("qualified"));
        String examCatory = StringUtils.trimToNull(req.getParameter("examCatory"));
        String name = StringUtils.trimToNull(req.getParameter("name"));
        String gender = StringUtils.trimToNull(req.getParameter("gender"));
        String idCard = StringUtils.trimToNull(req.getParameter("idCard"));
        String phone = StringUtils.trimToNull(req.getParameter("phone"));
        String dateStart = StringUtils.trimToNull(req.getParameter("dateStart"));
        String dateEnd = StringUtils.trimToNull(req.getParameter("dateEnd"));
        String examNo = StringUtils.trimToNull(req.getParameter("examNo"));
        String examCardNo = StringUtils.trimToNull(req.getParameter("examCardNo"));
        String checkState = StringUtils.trimToNull(req.getParameter("checkState"));
        String companyRegId = StringUtils.trimToNull(req.getParameter("companyRegId"));
        String teamId = StringUtils.trimToNull(req.getParameter("teamId"));
        String printStatus = StringUtils.trimToNull(req.getParameter("printStatus"));
        String doctorType = StringUtils.trimToNull(req.getParameter("doctorType"));
        String doctor = StringUtils.trimToNull(req.getParameter("doctor"));
        String sortOrder = StringUtils.trimToNull(req.getParameter("sortOrder"));
        String filterStatus = StringUtils.trimToNull(req.getParameter("filterStatus"));
        String dateType = StringUtils.trimToNull(req.getParameter("dateType"));
        String preSummaryMethod = StringUtils.trimToNull(req.getParameter("preSummaryMethod"));
        String initailSummaryMethod = StringUtils.trimToNull(req.getParameter("initailSummaryMethod"));
        String eReportStatus = StringUtils.trimToNull(req.getParameter("eReportStatus"));
        String paperReportStatus = StringUtils.trimToNull(req.getParameter("paperReportStatus"));
        String daysFromReg = StringUtils.trimToNull(req.getParameter("daysFromReg"));

        //如果examNo不为空，忽略时间段
        if (StringUtils.isNotBlank(examNo)) {
            dateStart = null;
            dateEnd = null;
        }

        customerRegSummaryService.pageCustomerReg(page, examCatory, qualified, name, gender, idCard, phone, dateType, dateStart, dateEnd, examNo, examCardNo, checkState, companyRegId, teamId, printStatus, doctorType, doctor, filterStatus, status, summaryStatus, preSummaryMethod, initailSummaryMethod, eReportStatus, paperReportStatus, sortOrder, daysFromReg);
        long end = System.currentTimeMillis();
        System.out.println("总检列表耗时:" + (end - start));
        page.getRecords().forEach(reg -> {
            List<StatusStat> statList = customerRegItemResultService.statCheckStatusByRegId(reg.getId());
            reg.setStatusStatList(statList);
        });
        long end2 = System.currentTimeMillis();
        System.out.println("总检设置检查统计信息耗时:" + (end2 - end));

        return Result.OK(page);
    }

    /**
     * 根据id获取登记记
     *
     * @return
     */
    //@AutoLog(value = "客户登记-分页列表查询")
    @ApiOperation(value = "客户登记-根据id获取登记记录", notes = "客户登记-根据id获取登记记录")
    @GetMapping(value = "/getRegById")
    public Result<CustomerReg> getRegById(String regId) {

        CustomerReg customerReg = customerRegSummaryService.getRegById(regId);
        return Result.OK(customerReg);
    }


    /**
     * 分页列表查询
     *
     * @param customerRegSummary
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "总检-分页列表查询")
    @ApiOperation(value = "总检-分页列表查询", notes = "总检-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CustomerRegSummary>> queryPageList(CustomerRegSummary customerRegSummary, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo, @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        QueryWrapper<CustomerRegSummary> queryWrapper = QueryGenerator.initQueryWrapper(customerRegSummary, req.getParameterMap());
        Page<CustomerRegSummary> page = new Page<CustomerRegSummary>(pageNo, pageSize);
        IPage<CustomerRegSummary> pageList = customerRegSummaryService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param customerRegSummary
     * @return
     */
    @AutoLog(value = "总检-保存")
    @ApiOperation(value = "总检-保存", notes = "总检-保存")
    @RequiresPermissions("summary:customer_reg_summary:add")
    @PostMapping(value = "/saveSummary")
    public Result<?> saveSummary(@RequestBody CustomerRegSummary customerRegSummary) {
        try {
            customerRegSummaryService.saveOrUpdateCustomerSummary(customerRegSummary);
            return Result.OK("保存成功！");
        } catch (Exception e) {
            log.error("保存总检建议失败", e);
            return Result.error(e.getMessage());
        }
    }


    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "总检-通过id删除")
    @ApiOperation(value = "总检-通过id删除", notes = "总检-通过id删除")
    @RequiresPermissions("summary:customer_reg_summary:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        customerRegSummaryService.removeById(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "总检-批量删除")
    @ApiOperation(value = "总检-批量删除", notes = "总检-批量删除")
    @RequiresPermissions("summary:customer_reg_summary:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.customerRegSummaryService.removeByIds(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功!");
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "总检-通过id查询")
    @ApiOperation(value = "总检-通过id查询", notes = "总检-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CustomerRegSummary> queryById(@RequestParam(name = "id", required = true) String id) {
        CustomerRegSummary customerRegSummary = customerRegSummaryService.getById(id);
        if (customerRegSummary == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(customerRegSummary);
    }

    /**
     * 通过id查询
     *
     * @param regId
     * @return
     */
    //@AutoLog(value = "总检-通过id查询")
    @ApiOperation(value = "总检-通过id查询", notes = "总检-通过id查询")
    @GetMapping(value = "/queryByRegId")
    public Result<CustomerRegSummary> queryByRegId(@RequestParam(name = "regId", required = true) String regId) {
        CustomerRegSummary customerRegSummary = customerRegSummaryService.getOrGenerateByRedId(regId);

        return Result.OK(customerRegSummary);
    }

    @ApiOperation(value = "总检-根据建议库关键词更新名词解释", notes = "总检-根据建议库关键词更新名词解释")
    @GetMapping(value = "/updateKeywordsExplain")
    public Result<?> updateKeywordsExplain() {

        try {
            summaryAdviceService.updateKeywordsExplain();
            return Result.OK("更新成功");
        } catch (Exception e) {
            return Result.error(e.getMessage());
        }
    }

    /**
     * 导出excel
     *
     * @param request
     * @param customerRegSummary
     */
    @RequiresPermissions("summary:customer_reg_summary:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, CustomerRegSummary customerRegSummary) {
        return super.exportXls(request, customerRegSummary, CustomerRegSummary.class, "总检");
    }

    /**
     * 通过excel导入数据
     *
     * @param request
     * @param response
     * @return
     */
    @RequiresPermissions("summary:customer_reg_summary:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        return super.importExcel(request, response, CustomerRegSummary.class);
    }

}
